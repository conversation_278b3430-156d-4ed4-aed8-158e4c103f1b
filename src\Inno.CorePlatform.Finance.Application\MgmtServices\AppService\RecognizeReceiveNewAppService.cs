using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.Enums;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Dapr.Client;
using Newtonsoft.Json;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 重构后的认款服务 - 专注于撤销认款业务
    /// 这是一个演示版本，展示了如何重构撤销认款的逻辑
    ///
    /// 重构要点：
    /// 1. 提取公共验证方法，减少代码重复
    /// 2. 按业务类型分离撤销逻辑，提高可维护性
    /// 3. 统一错误处理和响应格式
    /// 4. 优化数据库操作，减少查询次数
    /// 5. 清晰的方法职责分工
    /// </summary>
    public class RecognizeReceiveNewAppService : IRecognizeReceiveNewAppService
    {
        #region 依赖注入

        private readonly IRecognizeReceiveItemRepository _itemRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly FinanceDbContext _db;
        private readonly ISubLogService _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly DaprClient _daprClient;

        /// <summary>
        /// 构造函数 - 简化版本，只保留核心依赖
        /// </summary>
        public RecognizeReceiveNewAppService(
            IRecognizeReceiveItemRepository itemRepository,
            IUnitOfWork unitOfWork,
            FinanceDbContext db,
            ISubLogService logger,
            IKingdeeApiClient kingdeeApiClient,
            IAppServiceContextAccessor appServiceContextAccessor,
            DaprClient daprClient)
        {
            _itemRepository = itemRepository;
            _unitOfWork = unitOfWork;
            _db = db;
            _logger = logger;
            _kingdeeApiClient = kingdeeApiClient;
            _appServiceContextAccessor = appServiceContextAccessor;
            _daprClient = daprClient;
        }

        #endregion

        #region 撤销认款主入口 - 重构演示

        /// <summary>
        /// 撤销认款 - 重构演示版本
        /// 展示了如何通过提取公共方法和分离职责来简化复杂的业务逻辑
        /// </summary>
        /// <param name="itemId">认款单ID</param>
        /// <param name="userName">操作用户</param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceiveAsync(Guid itemId, string userName)
        {
            // 重构要点1：统一的错误处理包装器
            return await ExecuteWithErrorHandling(async () =>
            {
                // 重构要点2：清晰的步骤分解
                var item = await GetAndValidateItem(itemId, userName);
                var details = await GetValidDetails(itemId);
                var cancelType = DetermineCancelType(details);

                // 重构要点3：根据类型分发到具体处理方法
                return cancelType switch
                {
                    CancelType.Invoice => await ProcessInvoiceCancel(item, details),
                    CancelType.Order => await ProcessOrderCancel(item, details),
                    CancelType.Credit => await ProcessCreditCancel(item, details),
                    _ => CreateFailedResponse("不支持的撤销类型")
                };
            }, "CancelReceive", itemId.ToString());
        }

        /// <summary>
        /// 撤销认款明细 - 重构演示版本
        /// </summary>
        public async Task<BaseResponseData<int>> CancelReceiveDetailAsync(PartCancelInput input)
        {
            return await ExecuteWithErrorHandling(async () =>
            {
                var item = await GetAndValidateItem(input.Id, input.UserName);
                var details = await GetSpecificDetails(input.Id, input.DetailIds);
                var cancelType = DetermineCancelType(details);

                return cancelType switch
                {
                    CancelType.Invoice => await ProcessInvoiceCancel(item, details),
                    CancelType.Order => await ProcessOrderCancel(item, details),
                    CancelType.Credit => await ProcessCreditCancel(item, details),
                    _ => CreateFailedResponse("不支持的撤销类型")
                };
            }, "CancelReceiveDetail", input.Id.ToString());
        }

        #endregion

        #region 重构演示 - 公共验证方法

        /// <summary>
        /// 获取并验证认款单 - 重构要点：提取公共验证逻辑
        /// </summary>
        private async Task<RecognizeReceiveItem> GetAndValidateItem(Guid itemId, string userName)
        {
            var item = await _itemRepository.GetWithNoTrackAsync(itemId);

            // 重构要点4：统一的验证方法
            ValidateItemExists(item);
            ValidateItemStatus(item);
            ValidateUserPermission(item, userName);
            await ValidateSystemConditions(item);

            return item;
        }

        /// <summary>
        /// 基础验证方法 - 重构要点：单一职责原则
        /// </summary>
        private static void ValidateItemExists(RecognizeReceiveItem item)
        {
            if (item == null)
                throw new ApplicationException("认款单不存在");
        }

        private static void ValidateItemStatus(RecognizeReceiveItem item)
        {
            if (item.Status != 99)
                throw new ApplicationException("认款单状态无法撤销");
        }

        private static void ValidateUserPermission(RecognizeReceiveItem item, string userName)
        {
            if (item.CreatedBy != userName)
                throw new ApplicationException("非创建人无法撤销认款");
        }

        private async Task ValidateSystemConditions(RecognizeReceiveItem item)
        {
            // 系统级验证逻辑
            // 这里可以添加盘点期间验证、月度验证等
            await Task.CompletedTask;
        }

        #endregion

        #region 重构演示 - 数据获取方法

        /// <summary>
        /// 获取有效的认款明细 - 重构要点：数据获取逻辑分离
        /// </summary>
        private async Task<List<RecognizeReceiveDetailPo>> GetValidDetails(Guid itemId)
        {
            var details = await _db.RecognizeReceiveDetails
                .Where(x => x.RecognizeReceiveItemId == itemId &&
                           x.Status != (int)RecognizeReceiveDetailEnum.Cancel)
                .ToListAsync();

            if (!details.Any())
                throw new ApplicationException("认款单明细不存在");

            return details;
        }

        /// <summary>
        /// 获取指定的认款明细
        /// </summary>
        private async Task<List<RecognizeReceiveDetailPo>> GetSpecificDetails(Guid itemId, List<Guid> detailIds)
        {
            var details = await _db.RecognizeReceiveDetails
                .Where(x => x.RecognizeReceiveItemId == itemId &&
                           detailIds.Contains(x.Id) &&
                           x.Status != (int)RecognizeReceiveDetailEnum.Cancel)
                .ToListAsync();

            if (!details.Any())
                throw new ApplicationException("指定的认款明细不存在");

            return details;
        }

        #endregion

        /// <summary>
        /// 撤销认款 - 重构版本
        /// </summary>
        /// <param name="itemId">认款单ID</param>
        /// <param name="userName">操作用户</param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceiveAsync(Guid itemId, string userName)
        {
            return await ExecuteWithErrorHandling(async () =>
            {
                // 1. 获取认款单信息
                var item = await _itemRepository.GetWithNoTrackAsync(itemId);
                if (item == null)
                {
                    return CreateFailedResponse("认款单不存在");
                }

                // 2. 前置条件验证
                var validationResult = await ValidateBasicConditions(item, userName);
                if (!IsSuccess(validationResult))
                {
                    return validationResult;
                }

                // 3. 获取认款明细
                var details = await GetRecognizeReceiveDetails(itemId);
                if (!details.Any())
                {
                    return CreateFailedResponse("认款单明细不存在");
                }

                // 4. 根据认款类型执行相应的撤销逻辑
                return await ExecuteCancelByType(item, details);

            }, "CancelReceive", itemId.ToString());
        }

        /// <summary>
        /// 撤销认款明细 - 重构版本
        /// </summary>
        /// <param name="input">撤销明细输入参数</param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceiveDetailAsync(PartCancelInput input)
        {
            return await ExecuteWithErrorHandling(async () =>
            {
                // 1. 基础验证
                var item = await _itemRepository.GetWithNoTrackAsync(input.Id);
                if (item == null)
                {
                    return CreateFailedResponse("认款单不存在");
                }

                var validationResult = await ValidateDetailCancelConditions(item, input);
                if (!IsSuccess(validationResult))
                {
                    return validationResult;
                }

                // 2. 获取指定的认款明细
                var details = await GetSpecificRecognizeReceiveDetails(input.Id, input.DetailIds);
                if (!details.Any())
                {
                    return CreateFailedResponse("认款单明细不存在");
                }

                // 3. 执行撤销逻辑
                return await ExecuteCancelByType(item, details);

            }, "CancelReceiveDetail", input.Id.ToString());
        }

        #endregion

        #region 撤销认款类型分发

        /// <summary>
        /// 根据认款类型执行相应的撤销逻辑
        /// </summary>
        private async Task<BaseResponseData<int>> ExecuteCancelByType(
            RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details)
        {
            // 判断认款类型
            var detailTypes = details.Select(d => d.Type).Distinct().ToList();
            
            if (detailTypes.Count > 1)
            {
                return CreateFailedResponse("不支持混合类型的撤销操作");
            }

            var recognizeType = detailTypes.First();
            
            return recognizeType switch
            {
                1 => await CancelReceiveToInvoice(item, details),      // 撤销认款到发票
                2 => await CancelReceiveToOrder(item, details),       // 撤销认款到订单
                3 => await CancelReceiveToCredit(item, details),      // 撤销认款到初始应收
                _ => CreateFailedResponse("不支持的认款类型撤销操作")
            };
        }

        #endregion

        #region 基础验证方法

        /// <summary>
        /// 验证基础条件
        /// </summary>
        private async Task<BaseResponseData<int>> ValidateBasicConditions(RecognizeReceiveItem item, string userName)
        {
            // 1. 状态验证
            if (item.Status != 99)
            {
                return CreateFailedResponse("认款单状态无法撤销");
            }

            // 2. 权限验证
            if (item.CreatedBy != userName)
            {
                return CreateFailedResponse("非创建人无法撤销认款");
            }

            // 3. 暂收款特殊验证
            if (item.Classify == RecognizeReceiveClassifyEnum.Temp)
            {
                var goods = await _receiveItemQueryService.FirstOrDefaultAsync(p => p.RelateCode == item.Code);
                if (goods != null)
                {
                    return CreateFailedResponse("该暂收款下面存在货款无法撤销认款");
                }
            }

            // 4. 系统级验证
            return await ValidateSystemConditions(item);
        }

        /// <summary>
        /// 验证明细撤销条件
        /// </summary>
        private async Task<BaseResponseData<int>> ValidateDetailCancelConditions(RecognizeReceiveItem item, PartCancelInput input)
        {
            // 基础验证
            var basicValidation = await ValidateBasicConditions(item, input.UserName);
            if (!IsSuccess(basicValidation))
            {
                return basicValidation;
            }

            // 状态验证（明细撤销允许部分撤销状态）
            if (item.Status != 99 && item.Status != -2)
            {
                return CreateFailedResponse("认款单状态无法撤销认款");
            }

            // 暂收款不支持明细撤销
            if (item.Classify == RecognizeReceiveClassifyEnum.Temp)
            {
                return CreateFailedResponse("暂收款不支持撤销认款明细");
            }

            // 明细ID验证
            if (input.DetailIds == null || !input.DetailIds.Any())
            {
                return CreateFailedResponse("请至少勾选一条需要撤销的明细");
            }

            return CreateSuccessResponse("验证通过");
        }

        /// <summary>
        /// 验证系统条件
        /// </summary>
        private async Task<BaseResponseData<int>> ValidateSystemConditions(RecognizeReceiveItem item)
        {
            var companyId = Guid.Parse(item.CompanyId);

            // 1. 盘点期间验证
            var inventoryItem = await _inventoryQueryService.FirstOrDefaultAsync(
                p => p.CompanyId == companyId && p.Status == 2);
            if (inventoryItem != null)
            {
                return CreateFailedResponse("盘点期间不能提交撤销");
            }

            // 2. 系统月度验证
            var sysMonth = await _bDSApiClient.GetSystemMonth(item.CompanyId);
            await CheckSysMonth(companyId, sysMonth);

            return CreateSuccessResponse("系统验证通过");
        }

        #endregion

        #region 撤销认款到发票

        /// <summary>
        /// 撤销认款到发票
        /// </summary>
        private async Task<BaseResponseData<int>> CancelReceiveToInvoice(
            RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details)
        {
            var invoiceNos = details.Select(p => p.Code).ToList();

            // 1. 获取发票相关数据
            var (invoiceCredits, credits) = await GetInvoiceRelatedData(invoiceNos, item.ReceiveCode);
            if (!credits.Any())
            {
                return CreateFailedResponse($"发票号【{string.Join(",", invoiceNos)}】没有找到对应的应收");
            }

            // 2. 验证业务条件
            var businessValidation = await ValidateBusinessConditions(credits.Select(c => c.BillCode).ToList());
            if (!IsSuccess(businessValidation))
            {
                return businessValidation;
            }

            // 3. 处理冲销记录
            await ProcessAbatementRecords(item, credits, CancelReceiveType.Invoice);

            // 4. 更新发票认款金额
            await UpdateInvoiceReceiveAmount(details);

            // 5. 处理应付明细
            var debtProcessResult = await ProcessDebtDetails(item, credits, details, false);
            if (!IsSuccess(debtProcessResult))
            {
                return debtProcessResult;
            }

            // 6. 更新认款单和明细状态
            await UpdateCancelReceiveStatus(item, details);

            // 7. 清理认款明细应收记录
            await CleanupRecognizeReceiveDetailCredits(item.Id, details);

            // 8. 调用金蝶接口并完成操作
            return await CompleteCancel(item, invoiceNos, "A", credits.Select(c => c.OrderNo).Where(o => !string.IsNullOrEmpty(o)).ToList());
        }

        /// <summary>
        /// 获取发票相关数据
        /// </summary>
        private async Task<(List<InvoiceCreditPo> invoiceCredits, List<CreditPo> credits)> GetInvoiceRelatedData(
            List<string> invoiceNos, string receiveCode)
        {
            var invoiceCredits = await _db.InvoiceCredits
                .Include(p => p.Credit)
                .Where(p => invoiceNos.Contains(p.InvoiceNo))
                .Distinct()
                .AsNoTracking()
                .ToListAsync();

            var credits = await _db.InvoiceCredits
                .Include(p => p.Credit)
                .Where(p => invoiceNos.Contains(p.InvoiceNo) && p.Credit.AutoType != "98")
                .Select(p => p.Credit)
                .Distinct()
                .AsNoTracking()
                .ToListAsync();

            // 添加负数应收
            var lessCredit = await _db.Credits
                .Where(p => p.BillCode == receiveCode)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (lessCredit != null)
            {
                credits.Add(lessCredit);
            }

            credits = credits.DistinctBy(p => p.BillCode).ToList();

            return (invoiceCredits, credits);
        }

        /// <summary>
        /// 更新发票认款金额
        /// </summary>
        private async Task UpdateInvoiceReceiveAmount(List<RecognizeReceiveDetailPo> details)
        {
            var invoiceNos = details.Select(d => d.Code).ToHashSet();
            var invoices = await _db.Invoices
                .Where(p => invoiceNos.Contains(p.InvoiceNo))
                .ToListAsync();

            foreach (var detail in details)
            {
                var invoice = invoices.FirstOrDefault(p => p.InvoiceNo == detail.Code);
                if (invoice != null)
                {
                    invoice.ReceiveAmount = Math.Max(0, (invoice.ReceiveAmount ?? 0) - detail.Value);
                }
            }

            if (invoices.Any())
            {
                _db.Invoices.UpdateRange(invoices);
            }
        }

        #endregion

        #region 撤销认款到订单

        /// <summary>
        /// 撤销认款到订单
        /// </summary>
        private async Task<BaseResponseData<int>> CancelReceiveToOrder(
            RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details)
        {
            var orderNos = details.Select(p => p.Code).ToList();

            // 1. 验证预收账期
            var advanceValidation = await ValidateAdvancePeriod(orderNos);
            if (!IsSuccess(advanceValidation))
            {
                return advanceValidation;
            }

            // 2. 获取订单相关应收
            var credits = await GetOrderRelatedCredits(orderNos);

            // 3. 验证业务条件
            var businessValidation = await ValidateBusinessConditions(credits.Select(c => c.BillCode).ToList());
            if (!IsSuccess(businessValidation))
            {
                return businessValidation;
            }

            // 4. 处理冲销记录
            await ProcessAbatementRecords(item, credits, CancelReceiveType.Order);

            // 5. 更新发票认款金额（通过应收关联）
            await UpdateInvoiceReceiveAmountByCredits(credits, details);

            // 6. 处理应付明细
            var debtProcessResult = await ProcessDebtDetails(item, credits, details, true);
            if (!IsSuccess(debtProcessResult))
            {
                return debtProcessResult;
            }

            // 7. 更新认款单和明细状态
            await UpdateCancelReceiveStatus(item, details);

            // 8. 清理认款明细应收记录
            await CleanupRecognizeReceiveDetailCredits(item.Id, details);

            // 9. 调用金蝶接口并完成操作
            return await CompleteCancel(item, orderNos, "B", orderNos);
        }

        /// <summary>
        /// 验证预收账期
        /// </summary>
        private async Task<BaseResponseData<int>> ValidateAdvancePeriod(List<string> orderNos)
        {
            if (!orderNos.Any())
            {
                return CreateFailedResponse("未获取到订单号");
            }

            var saleAdvancePeriodInfo = await _sellApiClient.GetSaleAdvancePeriodInfo(
                new SaleAdvancePeriodInfoInput { BillCodes = orderNos });

            if (saleAdvancePeriodInfo == null || !saleAdvancePeriodInfo.Any())
            {
                return CreateFailedResponse("没有获取到销售订单预收账期信息");
            }

            foreach (var saleItem in saleAdvancePeriodInfo)
            {
                if (saleItem.SaleType != SaleTypeEnum.ServiceFee &&
                    saleItem.HasAdvancePeriod &&
                    saleItem.Status != SaleStatusEnum.Rejected &&
                    saleItem.Status != SaleStatusEnum.Receivable)
                {
                    return CreateFailedResponse("预收款销售订单已流转至仓库出库，请先驳回出库单据才能撤销此认款");
                }
            }

            return CreateSuccessResponse("预收账期验证通过");
        }

        /// <summary>
        /// 获取订单相关应收
        /// </summary>
        private async Task<List<CreditPo>> GetOrderRelatedCredits(List<string> orderNos)
        {
            var orderNoSet = orderNos.ToHashSet();

            var credits1 = await _db.Credits
                .Where(p => orderNoSet.Contains(p.OrderNo) && p.AutoType != "98")
                .AsNoTracking()
                .ToListAsync();

            var credits2 = await _db.Credits
                .Where(p => orderNoSet.Contains(p.RelateCode) && p.AutoType != "98")
                .AsNoTracking()
                .ToListAsync();

            credits1.AddRange(credits2);
            return credits1.DistinctBy(p => p.BillCode).ToList();
        }

        #endregion

        #region 撤销认款到初始应收

        /// <summary>
        /// 撤销认款到初始应收
        /// </summary>
        private async Task<BaseResponseData<int>> CancelReceiveToCredit(
            RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details)
        {
            var creditNos = details.Select(p => p.Code).ToList();
            creditNos.Add(item.ReceiveCode); // 当收款账号是负数应收时

            // 1. 获取应收单据
            var credits = await _db.Credits
                .Where(p => creditNos.Contains(p.BillCode))
                .ToListAsync();

            // 2. 验证业务条件
            var businessValidation = await ValidateBusinessConditions(creditNos);
            if (!IsSuccess(businessValidation))
            {
                return businessValidation;
            }

            // 3. 处理冲销记录
            await ProcessAbatementRecords(item, credits, CancelReceiveType.Credit);

            // 4. 重置应收状态
            foreach (var credit in credits)
            {
                credit.AbatedStatus = AbatedStatusEnum.NonAbate;
            }
            _db.Credits.UpdateRange(credits);

            // 5. 处理应付明细
            var debtProcessResult = await ProcessDebtDetailsByCredits(item, credits, details);
            if (!IsSuccess(debtProcessResult))
            {
                return debtProcessResult;
            }

            // 6. 更新认款单和明细状态
            await UpdateCancelReceiveStatus(item, details);

            // 7. 清理认款明细应收记录
            await CleanupRecognizeReceiveDetailCredits(item.Id, details);

            // 8. 调用金蝶接口并完成操作
            var orderNos = credits.Select(c => c.OrderNo).Where(o => !string.IsNullOrEmpty(o)).ToList();
            return await CompleteCancel(item, creditNos, "C", orderNos);
        }

        #endregion

        #region 公共业务验证方法

        /// <summary>
        /// 验证业务条件（损失确认等）
        /// </summary>
        private async Task<BaseResponseData<int>> ValidateBusinessConditions(List<string> creditCodes)
        {
            if (!creditCodes.Any())
            {
                return CreateSuccessResponse("无需验证");
            }

            // 验证损失确认状态
            var lossRecognitions = await _db.LossRecognitionDetails
                .Include(x => x.LossRecognitionItem)
                .Where(x => creditCodes.ToHashSet().Contains(x.BillCode) &&
                           x.LossRecognitionItem.Status != StatusEnum.Complate &&
                           x.LossRecognitionItem.Status != StatusEnum.Refuse)
                .AsNoTracking()
                .ToListAsync();

            if (lossRecognitions.Any())
            {
                var errCodes = string.Join(",", lossRecognitions.Select(x => x.BillCode));
                return CreateFailedResponse($"应收单"{errCodes}"正在进行损失确认，无法撤销认款");
            }

            return CreateSuccessResponse("业务条件验证通过");
        }

        /// <summary>
        /// 验证批量付款单
        /// </summary>
        private async Task<BaseResponseData<int>> ValidatePaymentAutoItems(HashSet<Guid> debtDetailIds)
        {
            if (!debtDetailIds.Any())
            {
                return CreateSuccessResponse("无需验证");
            }

            var paymentCodes = await _db.PaymentAutoDetails
                .Include(p => p.PaymentAutoItem)
                .Where(p => debtDetailIds.Contains(p.DebtDetilId))
                .Select(p => p.PaymentAutoItem.Code)
                .ToListAsync();

            if (paymentCodes.Any())
            {
                return CreateFailedResponse($"应收对应的应付明细已在批量付款单【{string.Join(",", paymentCodes)}】中存在");
            }

            return CreateSuccessResponse("批量付款单验证通过");
        }

        #endregion

        #region 冲销记录处理

        /// <summary>
        /// 处理冲销记录
        /// </summary>
        private async Task ProcessAbatementRecords(RecognizeReceiveItem item, List<CreditPo> credits, CancelReceiveType cancelType)
        {
            var creditCodes = credits.Select(c => c.BillCode).ToList();

            // 查找有认款单号的冲销记录
            var abatementsWithRecognizeCode = await _db.Abatements
                .Where(p => p.RecognizeReceiveCode == item.Code)
                .ToListAsync();

            if (abatementsWithRecognizeCode.Any())
            {
                // 直接删除匹配的冲销记录
                var filteredAbatements = abatementsWithRecognizeCode
                    .Where(x => creditCodes.ToHashSet().Contains(x.DebtBillCode))
                    .ToList();

                if (filteredAbatements.Any())
                {
                    _db.Abatements.RemoveRange(filteredAbatements);
                }
            }
            else
            {
                // 处理无认款单号的冲销记录
                await ProcessAbatementWithoutRecognizeCode(item, creditCodes, cancelType);
            }

            // 更新应收冲销状态
            foreach (var credit in credits)
            {
                var hasAbatement = await _db.Abatements
                    .AnyAsync(p => p.CreditBillCode == credit.BillCode || p.DebtBillCode == credit.BillCode);

                if (!hasAbatement)
                {
                    credit.AbatedStatus = AbatedStatusEnum.NonAbate;
                }
            }

            if (credits.Any())
            {
                _db.Credits.UpdateRange(credits);
            }
        }

        /// <summary>
        /// 处理无认款单号的冲销记录
        /// </summary>
        private async Task ProcessAbatementWithoutRecognizeCode(RecognizeReceiveItem item, List<string> creditCodes, CancelReceiveType cancelType)
        {
            var abatements = await _db.Abatements
                .Where(p => p.CreditBillCode == item.ReceiveCode && creditCodes.ToHashSet().Contains(p.DebtBillCode))
                .AsNoTracking()
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();

            if (!abatements.Any())
            {
                return;
            }

            var abatementsToDelete = new List<AbatementPo>();

            // 根据撤销类型采用不同的匹配策略
            switch (cancelType)
            {
                case CancelReceiveType.Credit:
                    // 初始应收：精确匹配金额
                    await ProcessCreditAbatements(abatements, creditCodes, abatementsToDelete);
                    break;
                case CancelReceiveType.Invoice:
                case CancelReceiveType.Order:
                    // 发票和订单：复杂匹配逻辑
                    await ProcessComplexAbatements(abatements, abatementsToDelete);
                    break;
            }

            if (abatementsToDelete.Any())
            {
                _db.Abatements.RemoveRange(abatementsToDelete);
            }
        }

        #endregion

        #region 应付明细处理

        /// <summary>
        /// 处理应付明细
        /// </summary>
        private async Task<BaseResponseData<int>> ProcessDebtDetails(
            RecognizeReceiveItem item, List<CreditPo> credits, List<RecognizeReceiveDetailPo> details, bool isOrderType)
        {
            var creditIds = credits.Select(c => c.Id).ToHashSet();

            // 获取应付明细
            var debtDetails = await _db.DebtDetails
                .Where(p => p.CreditId.HasValue && creditIds.Contains(p.CreditId.Value))
                .ToListAsync();

            if (debtDetails.Count == 0)
            {
                return CreateSuccessResponse("无应付明细需要处理");
            }

            // 验证批量付款单
            var debtDetailIds = debtDetails.Select(d => d.Id).ToHashSet();
            var paymentValidation = await ValidatePaymentAutoItems(debtDetailIds);
            if (!IsSuccess(paymentValidation))
            {
                return paymentValidation;
            }

            // 处理应付明细状态
            await ProcessDebtDetailStatus(debtDetails, details, isOrderType);

            return CreateSuccessResponse("应付明细处理完成");
        }

        /// <summary>
        /// 根据应收处理应付明细
        /// </summary>
        private async Task<BaseResponseData<int>> ProcessDebtDetailsByCredits(
            RecognizeReceiveItem item, List<CreditPo> credits, List<RecognizeReceiveDetailPo> details)
        {
            var creditCodes = credits.Select(c => c.BillCode).ToHashSet();

            var debtDetails = await _db.DebtDetails
                .Where(p => creditCodes.Contains(p.CreditCode))
                .ToListAsync();

            if (debtDetails.Count == 0)
            {
                return CreateSuccessResponse("无应付明细需要处理");
            }

            var debtDetailIds = debtDetails.Select(d => d.Id).ToHashSet();
            var paymentValidation = await ValidatePaymentAutoItems(debtDetailIds);
            if (!IsSuccess(paymentValidation))
            {
                return paymentValidation;
            }

            // 处理应付明细状态
            foreach (var debtDetail in debtDetails)
            {
                var matchingDetail = details.FirstOrDefault(d => d.Code == debtDetail.CreditCode);
                if (matchingDetail != null)
                {
                    debtDetail.ReceiveAmount = Math.Max(0, (debtDetail.ReceiveAmount ?? 0) - matchingDetail.Value);
                }
            }

            _db.DebtDetails.UpdateRange(debtDetails);
            return CreateSuccessResponse("应付明细处理完成");
        }

        /// <summary>
        /// 处理应付明细状态
        /// </summary>
        private async Task ProcessDebtDetailStatus(List<DebtDetailPo> debtDetails, List<RecognizeReceiveDetailPo> details, bool isOrderType)
        {
            foreach (var debtDetail in debtDetails)
            {
                RecognizeReceiveDetailPo matchingDetail = null;

                if (isOrderType)
                {
                    // 订单类型：通过应收ID匹配
                    matchingDetail = details.FirstOrDefault(d =>
                        debtDetail.CreditId.HasValue &&
                        _db.Credits.Any(c => c.Id == debtDetail.CreditId.Value && c.OrderNo == d.Code));
                }
                else
                {
                    // 发票类型：通过发票号匹配
                    matchingDetail = details.FirstOrDefault(d =>
                        _db.InvoiceCredits.Any(ic => ic.InvoiceNo == d.Code && ic.CreditId == debtDetail.CreditId));
                }

                if (matchingDetail != null)
                {
                    debtDetail.ReceiveAmount = Math.Max(0, (debtDetail.ReceiveAmount ?? 0) - matchingDetail.Value);
                }
            }

            _db.DebtDetails.UpdateRange(debtDetails);
        }

        #endregion

        #region 状态更新和数据清理

        /// <summary>
        /// 更新认款单和明细状态
        /// </summary>
        private async Task UpdateCancelReceiveStatus(RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details)
        {
            var userName = _appServiceContextAccessor?.Get().UserName;

            // 获取所有明细
            var allDetails = await _db.RecognizeReceiveDetails
                .Where(x => x.RecognizeReceiveItemId == item.Id)
                .ToListAsync();

            var canceledCount = allDetails.Count(x => x.Status == (int)RecognizeReceiveDetailEnum.Cancel);
            var totalCount = allDetails.Count;

            // 计算撤销后状态
            if (canceledCount + details.Count == totalCount)
            {
                item.Status = -1; // 完全撤销
            }
            else
            {
                item.Status = -2; // 部分撤销
            }

            item.UpdatedBy = userName;
            item.UpdatedTime = DateTime.Now;

            // 更新明细状态
            var detailIds = details.Select(d => d.Id).ToHashSet();
            var updDetails = allDetails.Where(x => detailIds.Contains(x.Id)).ToList();

            updDetails.ForEach(x =>
            {
                x.Status = RecognizeReceiveDetailEnum.Cancel;
                x.UpdatedBy = userName;
                x.UpdatedTime = DateTime.Now;
            });

            await _itemRepository.UpdateAsync(item);
            _db.RecognizeReceiveDetails.UpdateRange(updDetails);
        }

        /// <summary>
        /// 清理认款明细应收记录
        /// </summary>
        private async Task CleanupRecognizeReceiveDetailCredits(Guid itemId, List<RecognizeReceiveDetailPo> details)
        {
            var detailIds = details.Select(d => d.Id).ToHashSet();

            var rrdcs = await _db.RecognizeReceiveDetailCredits
                .Where(x => x.RecognizeReceiveItemId == itemId && detailIds.Contains(x.RecognizeReceiveDetailId))
                .ToListAsync();

            if (rrdcs.Count > 0)
            {
                _db.RecognizeReceiveDetailCredits.RemoveRange(rrdcs);
            }
        }

        /// <summary>
        /// 通过应收更新发票认款金额
        /// </summary>
        private async Task UpdateInvoiceReceiveAmountByCredits(List<CreditPo> credits, List<RecognizeReceiveDetailPo> details)
        {
            var creditIds = credits.Select(c => c.Id).ToHashSet();

            var invoiceCredits = await _db.InvoiceCredits
                .Where(ic => creditIds.Contains(ic.CreditId))
                .ToListAsync();

            if (!invoiceCredits.Any())
            {
                return;
            }

            var invoiceNos = invoiceCredits.Select(ic => ic.InvoiceNo).ToHashSet();
            var invoices = await _db.Invoices
                .Where(i => invoiceNos.Contains(i.InvoiceNo))
                .ToListAsync();

            foreach (var detail in details)
            {
                var relatedCredit = credits.FirstOrDefault(c => c.OrderNo == detail.Code || c.RelateCode == detail.Code);
                if (relatedCredit != null)
                {
                    var relatedInvoiceCredits = invoiceCredits.Where(ic => ic.CreditId == relatedCredit.Id);
                    foreach (var invoiceCredit in relatedInvoiceCredits)
                    {
                        var invoice = invoices.FirstOrDefault(i => i.InvoiceNo == invoiceCredit.InvoiceNo);
                        if (invoice != null)
                        {
                            var proportionAmount = detail.Value * (invoiceCredit.CreditAmount / relatedCredit.Value);
                            invoice.ReceiveAmount = Math.Max(0, (invoice.ReceiveAmount ?? 0) - proportionAmount);
                        }
                    }
                }
            }

            if (invoices.Any())
            {
                _db.Invoices.UpdateRange(invoices);
            }
        }

        #endregion

        #region 金蝶接口和完成操作

        /// <summary>
        /// 完成撤销操作
        /// </summary>
        private async Task<BaseResponseData<int>> CompleteCancel(
            RecognizeReceiveItem item, List<string> businessCodes, string subscriptionType, List<string> orderNos)
        {
            // 1. 调用金蝶接口
            var kdResult = await CallKingdeeCancel(item, businessCodes, subscriptionType);
            if (kdResult.Code != CodeStatusEnum.Success)
            {
                return kdResult;
            }

            // 2. 提交事务
            await _unitOfWork.CommitAsync();

            // 3. 发送通知
            await SendCancelNotification(item, orderNos, subscriptionType);

            return CreateSuccessResponse("撤销认款操作成功");
        }

        /// <summary>
        /// 调用金蝶撤销接口
        /// </summary>
        private async Task<BaseResponseData<int>> CallKingdeeCancel(
            RecognizeReceiveItem item, List<string> businessCodes, string subscriptionType)
        {
            try
            {
                var actualDate = await _companyDateService.GetActualDateAsync(item.CompanyId);
                var kdInput = new List<QuashAcceptancesRequestVo>
                {
                    new QuashAcceptancesRequestVo
                    {
                        billNo = item.Code,
                        subscriptionType = subscriptionType,
                        subscriptionNumList = businessCodes,
                        ActualDate = actualDate.ToString("yyyy-MM-dd HH:mm:ss")
                    }
                };

                var kdResult = await _kingdeeApiClient.CancelReceive(kdInput);
                if (kdResult.Code != CodeStatusEnum.Success)
                {
                    return CreateFailedResponse($"金蝶接口调用失败：{kdResult.Message}");
                }

                return CreateSuccessResponse("金蝶接口调用成功");
            }
            catch (Exception ex)
            {
                _logger.LogAzure("CallKingdeeCancel", ex.Message, "金蝶接口异常", LogLevelEnum.Error);
                return CreateFailedResponse($"金蝶接口异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 发送撤销通知
        /// </summary>
        private async Task SendCancelNotification(RecognizeReceiveItem item, List<string> orderNos, string operationType)
        {
            try
            {
                if (!orderNos.Any())
                {
                    return;
                }

                var sendPara = new List<CancelReceiptInput>
                {
                    new CancelReceiptInput { RecognizeCode = item.Code, SaleCodes = orderNos }
                };

                var operationName = operationType switch
                {
                    "A" => "Invoice",
                    "B" => "Order",
                    "C" => "Credit",
                    _ => "Unknown"
                };

                _logger.LogAzure($"Cancel{operationName}", JsonConvert.SerializeObject(sendPara), $"撤销认款到{operationName}通知销售");

                await _daprClient.PublishEventAsync("pubsub-default", "finance-sale-CancelReceive", sendPara);

                await _logger.LogAsync($"Cancel{operationName}", JsonConvert.SerializeObject(sendPara), $"撤销认款到{operationName}通知销售-成功");
            }
            catch (Exception ex)
            {
                _logger.LogAzure("SendCancelNotification", ex.Message, "发送通知异常", LogLevelEnum.Error);
            }
        }

        #endregion

        #region 数据查询辅助方法

        /// <summary>
        /// 获取认款明细
        /// </summary>
        private async Task<List<RecognizeReceiveDetailPo>> GetRecognizeReceiveDetails(Guid itemId)
        {
            return await _db.RecognizeReceiveDetails
                .Where(x => x.RecognizeReceiveItemId == itemId && x.Status != RecognizeReceiveDetailEnum.Cancel)
                .AsNoTracking()
                .ToListAsync();
        }

        /// <summary>
        /// 获取指定的认款明细
        /// </summary>
        private async Task<List<RecognizeReceiveDetailPo>> GetSpecificRecognizeReceiveDetails(Guid itemId, List<Guid> detailIds)
        {
            var detailIdSet = detailIds.ToHashSet();
            return await _db.RecognizeReceiveDetails
                .Where(x => x.RecognizeReceiveItemId == itemId &&
                           detailIdSet.Contains(x.Id) &&
                           x.Status != RecognizeReceiveDetailEnum.Cancel)
                .AsNoTracking()
                .ToListAsync();
        }

        #endregion

        #region 冲销记录处理辅助方法

        /// <summary>
        /// 处理初始应收的冲销记录
        /// </summary>
        private static Task ProcessCreditAbatements(List<AbatementPo> abatements, List<string> creditCodes, List<AbatementPo> abatementsToDelete)
        {
            foreach (var creditCode in creditCodes)
            {
                var matchingAbatement = abatements.FirstOrDefault(a => a.DebtBillCode == creditCode);
                if (matchingAbatement != null)
                {
                    abatementsToDelete.Add(matchingAbatement);
                }
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 处理复杂的冲销记录匹配
        /// </summary>
        private static Task ProcessComplexAbatements(List<AbatementPo> abatements, List<AbatementPo> abatementsToDelete)
        {
            // 这里可以实现更复杂的匹配逻辑
            // 目前简化处理，直接添加所有匹配的冲销记录
            abatementsToDelete.AddRange(abatements);
            return Task.CompletedTask;
        }

        #endregion

        #region 系统辅助方法

        /// <summary>
        /// 检查系统月度
        /// </summary>
        private static Task CheckSysMonth(Guid companyId, string sysMonth)
        {
            // 系统月度检查逻辑
            // 这里可以添加具体的月度验证逻辑
            return Task.CompletedTask;
        }

        #endregion

        #region 响应和错误处理辅助方法

        /// <summary>
        /// 创建成功响应
        /// </summary>
        private static BaseResponseData<int> CreateSuccessResponse(string message = "操作成功")
        {
            return new BaseResponseData<int>
            {
                Code = CodeStatusEnum.Success,
                Message = message,
                Data = 1
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        private static BaseResponseData<int> CreateFailedResponse(string message)
        {
            return new BaseResponseData<int>
            {
                Code = CodeStatusEnum.Failed,
                Message = $"操作失败，原因：{message}",
                Data = 0
            };
        }

        /// <summary>
        /// 统一错误处理包装器
        /// </summary>
        private async Task<BaseResponseData<int>> ExecuteWithErrorHandling(
            Func<Task<BaseResponseData<int>>> operation, string operationName, string context)
        {
            try
            {
                return await operation();
            }
            catch (ApplicationException ex)
            {
                _logger.LogAzure(operationName, ex.Message, "业务异常", LogLevelEnum.Error);
                return CreateFailedResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogAzure(operationName, $"{context}系统异常:{ex.Message}", "系统异常", LogLevelEnum.Error);
                return CreateFailedResponse($"系统异常：{ex.Message}");
            }
        }

        #endregion

        #region 扩展属性

        /// <summary>
        /// 撤销认款类型枚举
        /// </summary>
        private enum CancelReceiveType
        {
            Invoice = 1,    // 发票
            Order = 2,      // 订单
            Credit = 3      // 初始应收
        }

        /// <summary>
        /// 检查响应是否成功
        /// </summary>
        private static bool IsSuccess<T>(BaseResponseData<T> response)
        {
            return response?.Code == CodeStatusEnum.Success;
        }

        #endregion

        #region 重构演示 - 类型判断和处理方法

        /// <summary>
        /// 确定撤销类型 - 重构要点：业务逻辑封装
        /// </summary>
        private static CancelType DetermineCancelType(List<RecognizeReceiveDetailPo> details)
        {
            var types = details.Select(d => d.Type).Distinct().ToList();

            if (types.Count > 1)
                throw new ApplicationException("不支持混合类型的撤销操作");

            return types.First() switch
            {
                1 => CancelType.Invoice,
                2 => CancelType.Order,
                3 => CancelType.Credit,
                _ => CancelType.Unknown
            };
        }

        /// <summary>
        /// 处理发票撤销 - 重构要点：职责分离
        /// </summary>
        private async Task<BaseResponseData<int>> ProcessInvoiceCancel(RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details)
        {
            // 重构要点5：清晰的步骤分解
            var invoiceNos = details.Select(d => d.Code).ToList();

            // 1. 获取相关数据
            var relatedData = await GetInvoiceRelatedData(invoiceNos);

            // 2. 业务验证
            await ValidateBusinessConditions(relatedData.Credits);

            // 3. 执行撤销操作
            await ExecuteInvoiceCancel(item, details, relatedData);

            // 4. 完成操作
            return await CompleteCancel(item, invoiceNos, "A");
        }

        /// <summary>
        /// 处理订单撤销
        /// </summary>
        private async Task<BaseResponseData<int>> ProcessOrderCancel(RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details)
        {
            var orderNos = details.Select(d => d.Code).ToList();

            // 订单特有的预收验证
            await ValidateAdvancePeriod(orderNos);

            var relatedData = await GetOrderRelatedData(orderNos);
            await ValidateBusinessConditions(relatedData.Credits);
            await ExecuteOrderCancel(item, details, relatedData);

            return await CompleteCancel(item, orderNos, "B");
        }

        /// <summary>
        /// 处理应收撤销
        /// </summary>
        private async Task<BaseResponseData<int>> ProcessCreditCancel(RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details)
        {
            var creditNos = details.Select(d => d.Code).ToList();

            var relatedData = await GetCreditRelatedData(creditNos);
            await ValidateBusinessConditions(relatedData.Credits);
            await ExecuteCreditCancel(item, details, relatedData);

            return await CompleteCancel(item, creditNos, "C");
        }

        #endregion

        #region 重构演示 - 辅助方法和类型定义

        /// <summary>
        /// 撤销类型枚举 - 重构要点：类型安全
        /// </summary>
        private enum CancelType
        {
            Unknown = 0,
            Invoice = 1,
            Order = 2,
            Credit = 3
        }

        /// <summary>
        /// 相关数据结构 - 重构要点：数据封装
        /// </summary>
        private class RelatedData
        {
            public List<CreditPo> Credits { get; set; } = new();
            public List<InvoiceCreditPo> InvoiceCredits { get; set; } = new();
            public List<AbatementPo> Abatements { get; set; } = new();
        }

        /// <summary>
        /// 统一错误处理包装器 - 重构要点：横切关注点分离
        /// </summary>
        private async Task<BaseResponseData<int>> ExecuteWithErrorHandling(
            Func<Task<BaseResponseData<int>>> operation, string operationName, string context)
        {
            try
            {
                return await operation();
            }
            catch (ApplicationException ex)
            {
                _logger.LogAzure(operationName, ex.Message, "业务异常", LogLevelEnum.Error);
                return CreateFailedResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogAzure(operationName, $"{context}系统异常:{ex.Message}", "系统异常", LogLevelEnum.Error);
                return CreateFailedResponse($"系统异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建成功响应 - 重构要点：响应格式统一
        /// </summary>
        private static BaseResponseData<int> CreateSuccessResponse(string message = "操作成功")
        {
            return new BaseResponseData<int>
            {
                Code = CodeStatusEnum.Success,
                Message = message,
                Data = 1
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        private static BaseResponseData<int> CreateFailedResponse(string message)
        {
            return new BaseResponseData<int>
            {
                Code = CodeStatusEnum.Failed,
                Message = $"操作失败，原因：{message}",
                Data = 0
            };
        }

        // 注意：以下方法为演示目的，实际实现需要根据具体业务逻辑完善
        private async Task<RelatedData> GetInvoiceRelatedData(List<string> invoiceNos) => new();
        private async Task<RelatedData> GetOrderRelatedData(List<string> orderNos) => new();
        private async Task<RelatedData> GetCreditRelatedData(List<string> creditNos) => new();
        private async Task ValidateBusinessConditions(List<CreditPo> credits) { }
        private async Task ValidateAdvancePeriod(List<string> orderNos) { }
        private async Task ExecuteInvoiceCancel(RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details, RelatedData data) { }
        private async Task ExecuteOrderCancel(RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details, RelatedData data) { }
        private async Task ExecuteCreditCancel(RecognizeReceiveItem item, List<RecognizeReceiveDetailPo> details, RelatedData data) { }
        private async Task<BaseResponseData<int>> CompleteCancel(RecognizeReceiveItem item, List<string> codes, string type) => CreateSuccessResponse();

        #endregion
    }
}
