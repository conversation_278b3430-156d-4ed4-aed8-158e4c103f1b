﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.CommonDto;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using NPOI.POIFS.Crypt.Dsig.Facets;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 
    /// </summary>
    public interface IBDSApiClient
    {
        /// <summary>
        /// 获取员工所属公司
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<List<StaffCompanyDto>> GetStaffCompaniesAsync(GetStaffCompaniesInputDto inputParam);

        /// <summary>
        /// 获取员工信息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<StaffInfoDto> GetStaffInfoAsync(GetStaffInfoInputDto inputParam);

        /// <summary>
        /// 员工信息元数据
        /// </summary>
        /// <returns></returns>
        Task<List<StaffInfo>> GetStaffMetaAsync(StaffsInput input);
        /// <summary>
        /// 所有员工信息元数据
        /// </summary>
        /// <returns></returns>
        Task<List<StaffInfo>> GetStaffALLAsync(StaffsInput input);
        /// <summary>
        /// 获取员工信息(带事业部信息)
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<DaprUserInfoOutput> GetUserStaffInfoAsync(BDSBaseInput inputParam);

        /// <summary>
        /// 从用户中心，获取员工数据 byNames
        /// </summary>
        /// <returns></returns>
        Task<ResponseData<UserOutput>> GetUserByNamesAsync(GetUserInput input);

        /// <summary>
        /// 根据真实姓名获取用户名
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<SmallUserQueryOutput>> GetSmallUsersByDisplayNames(List<string?>? input);

        /// <summary>
        /// 获取员工可以操作的业务单元
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<ServiceMetaOutput>> GetServiceMetaAsync(ServiceMetaInput input);
        /// <summary>
        /// 获取公司（带数据策略权限）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CompanyMetaOutput>> GetCompanyMetaAsync(CompanyMetaInput input);
        /// <summary>
        /// 获取公司信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CompanyMetaInfosOut>> GetCompanyMetaInfosAsync(CompanyMetaInfosInput input);
        /// <summary>
        /// 获取医院（带数据策略权限）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<HospitalMetaOutput>> GetHospitalMetaAsync(HospitalMetaInput input);
        /// <summary>
        /// 查询客户部门信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CustomerDeptMetaInfoOutput>> GetCustomerDept(BDSBaseInput input);
        /// <summary>
        /// 根据名称查询客户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CustomerDeptMetaInfoOutput>> GetCustomerByNames(BDSBaseInput input);
        /// <summary>
        /// 查询客户信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<CustomerOutput> GetCustomer(BDSBaseInput input);

        /// <summary>
        /// 查询客户收款信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CustomerBankOutput>> SelectReceiptInfo(SelectReceiptInfoInput input);

        /// <summary>
        /// 获取用户可以操作的核算部门权限(带数据策略权限)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<BusinessDeptOutput>> GetBusinessDeptMetaAsync(BusinessDeptMetaInput input);

        /// <summary>
        /// 获取核算部门列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<BusinessDeptOutput>> GetBusinessDeptListAsync(BusinessDeptMetaInput input);

        /// <summary>
        /// 根据ids获取核算部门列表
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<List<BusinessDeptQueryOutput>> GetBusinessDeptListByIds(List<string?> ids);

        /// <summary>
        /// 根据当前登录用户获取厂家信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<ProducerMetaOutput>> GetProducerMetaListAsync(ProducerMetaInput input);
        /// <summary>
        /// 获取供应商详细信息
        /// </summary>
        /// <param name="inputParam"></param>
        /// <returns></returns>
        Task<List<DaprAgentInfoOutput>> GetAgentInfoAsync(BDSBaseInput inputParam);
        /// <summary>
        /// 获取公司详细信息
        /// </summary>
        /// <param name="inputParam"></param>
        /// <returns></returns>
        Task<List<DaprCompanyInfoOutput>> GetCompanyInfoAsync(BDSBaseInput inputParam);
        /// <summary>
        /// 根据当前用户获取供应商信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<AgentMetaOutput>> GetAgentMetaListAsync(AgentMetaInput input);
        /// <summary>
        /// 获取业务单元详情列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<ServiceOutput>> QueryServiceItem(BDSBaseInput input);
        /// <summary>
        /// 获取系统月度
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        Task<string> GetSystemMonth(string companyId);

        /// <summary>
        /// 获取公司
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        Task<List<companyInvoice>> GetCompanyById(string companyId);

        /// <summary>
        /// 获取产品信息
        /// </summary>
        /// <param name="productNameIds"></param>
        /// <returns></returns>
        Task<List<ProductNameInfoOutput>> GetProductNameInfoAsync(List<Guid> productNameIds);

        /// <summary>
        /// 获取货号信息
        /// </summary>
        /// <param name="productIds"></param>
        /// <param name="inputs">供应商id和货号id</param>
        /// <returns></returns>
        Task<List<ProductNameInfoOutput>> GetProductbyIdsAsync(List<Guid> productIds, string companyId, List<ProductIdAndAgentIdInput> inputs);

        /// <summary>
        /// 获取货号信息
        /// </summary>
        /// <param name="productIds"></param>
        /// <param name="inputs">供应商id和货号id</param>
        /// <returns></returns>
        Task<List<ProductNoOuput>> GetByNos(List<string> productNos, string companyId, List<ProductIdAndAgentIdInput> inputs);

        /// <summary>
        /// 获取货号信息
        /// </summary>
        /// <param name="productIds"></param>
        /// <returns></returns>
        Task<List<ProductNoOuput>> GetByNos(List<string> productNos, string companyId);
        /// <summary>
        /// 获取供应商银行账号信息
        /// </summary>
        /// <param name="agentIds"></param>
        /// <returns></returns>
        Task<List<AgentBankInfo>> GetAgentBankInfoByAgentIds(List<Guid> agentIds);



        /// <summary>
        /// 批量更新公司系统月度
        /// </summary>
        /// <param name="companyIds">公司ID列表</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns></returns>
        Task<bool> UpdateSysMonth(List<Guid> companyIds, string sysMonth);
        /// <summary>
        /// 获取事业部编码（生成code）
        /// </summary>
        /// <param name="deptId"></param>
        /// <param name="userName"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<List<ValueLabelDTO>> GetBusinessDeptById(string deptId, string userName, string userId);
        Task<(List<ProductNameInfoOutput>, int)> GetProductClassificationPageAsync(List<Guid> productIds, int page = 1);
        Task<List<ProductItem>> QueryProductsAsync(ProductQueryInput input);
        Task<List<DataDictionaryOutput>> GetDataDictionaryListByType(string dicType);
        Task<List<TaxClassCodeOutput>> GetAllTaxClassCode();
        Task<List<SelectProductInvoicesOutput>> SelectProductInvoices(SelectProductInvoicesInput input);
        Task<List<GetFlatCheckedDeptsOutput>> GetFlatCheckedDepts(GetFlatCheckedDeptsInput input, string userName, string userId);
    }
}
