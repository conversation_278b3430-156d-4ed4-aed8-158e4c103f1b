﻿using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.EPPlus;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Enums;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Data.Utilities;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.ServiceClient;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Npoi.Mapper;
using OfficeOpenXml;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using static Inno.CorePlatform.Finance.Application.DTOs.PrepayBillInput;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class CustomizeInvoiceAppService : ICustomizeInvoiceAppService
    {
        private readonly FinanceDbContext _db;
        private readonly ICustomizeInvoiceItemRepository _customizeInvoiceItemRepository;
        private readonly ICustomizeInvoiceCreditRepository _customizeInvoiceCreditRepository;
        private readonly ICustomizeInvoiceDetailRepository _customizeInvoiceDetailRepository;
        private readonly ICustomizeInvoiceSubDetailRepository _customizeInvoiceSubDetailRepository;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ICodeGenClient _codeGenClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISellApiClient _sellApiClient;
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IWeaverApiClient _weaverApiClient;
        private readonly IConfiguration _configuration;
        private readonly ISPDApiClient _sPDApiClient;
        private readonly ILogisticsApiClient _logisticsApiClient;
        private readonly DaprClient _daprClient;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        private readonly IPCApiClient _pCApiClient;
        private readonly IProjectApiExcuteClient _projectApiExcuteClient;
        private readonly IICApiClient _iCApiClient;
        public CustomizeInvoiceAppService(
            FinanceDbContext db,
            ICodeGenClient codeGenClient,
            IBDSApiClient bDSApiClient,
            ICustomizeInvoiceItemRepository customizeInvoiceItemRepository,
            ICustomizeInvoiceDetailRepository customizeInvoiceDetailRepository,
            ICustomizeInvoiceSubDetailRepository customizeInvoiceSubDetailRepository,
            ICustomizeInvoiceCreditRepository customizeInvoiceCreditRepository,
            IKingdeeApiClient kingdeeApiClient,
            IInventoryApiClient inventoryApiClient,
            ISellApiClient sellApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IWeaverApiClient weaverApiClient,
            IConfiguration configuration,
            ISPDApiClient sPDApiClient,
            ILogisticsApiClient logisticsApiClient,
            DaprClient daprClient,
            IEasyCachingProvider easyCaching,
            IUnitOfWork unitOfWork,
            IFileGatewayClient fileGatewayClient,
            ICustomizeInvoiceQueryService customizeInvoiceQueryService,
            IProjectApiExcuteClient projectApiExcuteClient,
            IICApiClient iCApiClient,
            IPCApiClient pCApiClient)
        {
            this._configuration = configuration;
            this._bDSApiClient = bDSApiClient;
            this._codeGenClient = codeGenClient;
            this._db = db;
            this._customizeInvoiceItemRepository = customizeInvoiceItemRepository;
            this._customizeInvoiceDetailRepository = customizeInvoiceDetailRepository;
            this._customizeInvoiceSubDetailRepository = customizeInvoiceSubDetailRepository;
            this._kingdeeApiClient = kingdeeApiClient;
            this._customizeInvoiceCreditRepository = customizeInvoiceCreditRepository;
            this._unitOfWork = unitOfWork;
            this._sellApiClient = sellApiClient;
            this._inventoryApiClient = inventoryApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._weaverApiClient = weaverApiClient;
            this._sPDApiClient = sPDApiClient;
            this._daprClient = daprClient;
            this._logisticsApiClient = logisticsApiClient;
            this._easyCaching = easyCaching;
            this._fileGatewayClient = fileGatewayClient;
            this._customizeInvoiceQueryService = customizeInvoiceQueryService;
            this._pCApiClient = pCApiClient;
            this._projectApiExcuteClient = projectApiExcuteClient;
            this._iCApiClient = iCApiClient;
        }

        /// <summary>
        /// 制作-保存
        /// </summary>
        public async Task<(int, string)> SaveCustomizeInvoice(SaveCustomizeInvoiceInput input)
        {
            int count = 1;
            if (input.DetailList.GroupBy(t => t.CustomerId).Count() > 1)
            {
                return (0, "开票明细必须属于同一个付款单位!");
            }
            if (input.DetailList.Count(p => string.IsNullOrEmpty(p.ProductName)) > 0)
            {
                return (0, "开票明细必须有完整开票名称!");
            }
            var creditBillCodes = input.DetailList.Select(p => p.CreditBillCode).ToList();
            var orderNoStrs = input.DetailList.Select(p => p.RelateCode).Distinct().ToList();
            var creditBillCodeStr = string.Join(',', creditBillCodes);
            var creditBillCodeLst = creditBillCodeStr.Split(",").Distinct().ToHashSet();
            var credits = await _db.Credits.Where(p => creditBillCodeLst.Contains(p.BillCode)).OrderBy(p => string.IsNullOrEmpty(p.Note) ? 0 : p.Note.Length).ToListAsync();
            var noNeedInvoices = credits.Where(p => p.IsNoNeedInvoice == IsNoNeedInvoiceEnum.NoNeed).Select(p => p.BillCode).ToList();
            if (noNeedInvoices.Count() > 0)
            {
                return (0, $"[{string.Join(",", noNeedInvoices)}]应收单为无需开票!");
            }
            var cachekey = "SaveCustomizeInvoice_" + credits.First().CompanyId + "_" + credits.First().CustomerId;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, creditBillCodeStr, TimeSpan.FromSeconds(5));

                    if (credits.Count() != creditBillCodeLst.Count())
                    {
                        _easyCaching.Remove(cachekey);
                        return (0, $"操作失败，原因：【{string.Join(",", creditBillCodeLst)}】中存在已经删除的应收单,请刷新页面后操作!");
                    }
                    var creditIds = credits.Select(p => p.Id).ToHashSet();
                    var invoices = await _db.InvoiceCredits.Where(p => p.CreditId.HasValue && creditIds.Contains(p.CreditId.Value)).AsNoTracking().ToListAsync();
                    var customizeInvoiceItemId = Guid.NewGuid();
                    var customizeInvoiceClassifyRemark = string.Empty;

                    var creditDetails = await _db.CreditDetails.Include(p => p.Credit).Where(p => creditBillCodeLst.Contains(p.Credit.BillCode)).ToListAsync();
                    var creditDetailIds = creditDetails.Select(p => p.Id).ToHashSet();
                    var creditInvoiceDetailTemps = await _db.CreditInvoiceDetailTemps.Where(p => creditDetailIds.Contains(p.CreditDetailId.Value) && p.CreatedBy == input.CreateBy).ToListAsync();
                    var checkRet = MargeCheck(input.DetailList, creditDetails, input.OriginDetailList, creditInvoiceDetailTemps, input.Source.Value);
                    if (checkRet.Code != CodeStatusEnum.Success)
                    {
                        _easyCaching.Remove(cachekey);
                        return (0, checkRet.Message);
                    }

                    var redReversalConsumNos = credits.Where(p => !string.IsNullOrEmpty(p.RedReversalConsumNo) && string.IsNullOrEmpty(p.ShipmentCode)).Select(p => p.RedReversalConsumNo).Distinct().ToList();
                    var applyCode = string.Empty;


                    var ShipmentCodes = credits.Where(p => !string.IsNullOrEmpty(p.ShipmentCode) && p.SaleSource == SaleSourceEnum.Spd).Select(p => p.ShipmentCode).Distinct().ToList();
                    if (ShipmentCodes.Count() > 1)
                    {
                        _easyCaching.Remove(cachekey);
                        return (0, $"操作失败，原因：[{string.Join("、", ShipmentCodes)}]三方开票申请单不一致，不能保存开票明细!");
                    }
                    //SPD应收单不能和其它应收合并开票! 
                    var spdCredits = credits.Where(p => p.SaleSource == SaleSourceEnum.Spd).ToList();
                    if (spdCredits.Count() > 0 && spdCredits.Count() != credits.Count())
                    {
                        _easyCaching.Remove(cachekey);
                        return (0, $"操作失败，原因：SPD应收单不能和其它应收合并开票!");
                    }
                    if (spdCredits.Count() > 0 && spdCredits.Count() == credits.Count() && ShipmentCodes.Count() > 0)
                    {
                        var nullShipmentCodes = credits.Where(p => string.IsNullOrEmpty(p.ShipmentCode)).ToList();
                        if (nullShipmentCodes.Any())
                        {
                            _easyCaching.Remove(cachekey);
                            return (0, $"操作失败，原因：应收单号：[{string.Join("、", nullShipmentCodes.Select(p => p.BillCode))}]没有找到对应的三方开票申请单，不能保存开票明细!");
                        }
                        if (ShipmentCodes.Count() > 1)
                        {
                            _easyCaching.Remove(cachekey);
                            return (0, $"操作失败，原因：三方开票申请单[{string.Join("、", ShipmentCodes)}]不一致，不能够保存开票明细!");
                        }
                    }
                    if (redReversalConsumNos.Any())
                    {
                        var responseData = await _iCApiClient.GetSpdApplyCodeAsync(redReversalConsumNos);
                        if (responseData == null || responseData.Data == null || responseData.Data.Count <= 0)
                        {
                            _easyCaching.Remove(cachekey);
                            return (0, $"操作失败，原因：红字消耗单号：[{string.Join("、", redReversalConsumNos)}]没有找到对应的三方开票申请单，不能保存开票明细!");
                        }
                        else
                        {
                            var nullApplyCodess = responseData.Data.Where(p => string.IsNullOrEmpty(p.ApplyCode)).ToList();
                            if (nullApplyCodess.Any())
                            {
                                _easyCaching.Remove(cachekey);
                                return (0, $"操作失败，原因：红字消耗单号：[{string.Join("、", nullApplyCodess.Select(p => p.RedReverseConsumeCode))}]没有找到对应的三方开票申请单，不能保存开票明细!");
                            }
                            var applyCodes = responseData.Data.Select(p => p.ApplyCode).Distinct().ToList();
                            if (applyCodes.Distinct().Count() > 1)
                            {
                                _easyCaching.Remove(cachekey);
                                return (0, $"操作失败，原因：三方开票申请单[{string.Join("、", applyCodes)}]不一致，不能够保存开票明细!");
                            }
                            else
                            {
                                var redReverseConsumeCodes = responseData.Data.Select(p => p.RedReverseConsumeCode).Distinct().ToList();
                                var temp = redReversalConsumNos.Except(redReverseConsumeCodes).ToList();
                                if (temp.Any() && temp.Count() > 0)
                                {
                                    _easyCaching.Remove(cachekey);
                                    return (0, $"操作失败，原因：红字消耗单号：[{string.Join("、", temp)}]没有找到对应的三方开票申请单，不能保存开票明细!");
                                }
                                var nullShipmentCodeBillCodes = credits.Where(p => string.IsNullOrEmpty(p.ShipmentCode) && string.IsNullOrEmpty(p.RedReversalConsumNo)).Select(p => p.BillCode).Distinct().ToList();
                                if (nullShipmentCodeBillCodes.Count() > 0)
                                {
                                    _easyCaching.Remove(cachekey);
                                    return (0, $"操作失败，原因：[{string.Join("、", nullShipmentCodeBillCodes)}]应收没有三方开票申请单，不能保存开票明细!");
                                }
                                applyCode = applyCodes.First();
                                if (ShipmentCodes.Any() && applyCode != ShipmentCodes.First())
                                {
                                    _easyCaching.Remove(cachekey);
                                    return (0, $"操作失败，原因：[{applyCode},{ShipmentCodes.First()}]三方开票申请单不一致，不能保存开票明细!");
                                }
                            }
                        }
                    }


                    var currentCreditBillCodes = input.OriginDetailList.Select(p => p.CreditBillCode).ToList();
                    var invoiceCreditDetailIds = await _db.CustomizeInvoiceCredits.
                                                Where(p => currentCreditBillCodes.Contains(p.CreditCode)).
                                                Select(p => p.CreditDetailId.Value).ToListAsync();
                    foreach (var credit in credits)
                    {
                        if (invoices != null && invoices.Any())
                        {
                            if (credit.SaleSource == SaleSourceEnum.SunPurchase && !string.IsNullOrEmpty(credit.CustomerOrderCode))
                            {
                                #region 获取医保代码
                                var logisticDetails = await _logisticsApiClient.getFullDetails(new ShycShipmentDetaillnput
                                {
                                    purchaseCode = credit.SunPurchaseRelatecode
                                });
                                if (logisticDetails.Data == null || !logisticDetails.Data.Any())
                                {
                                    _easyCaching.Remove(cachekey);
                                    return (0, $"操作失败，原因：阳采，配送单未上报，请上报后再操作!");
                                }
                                #endregion
                            }
                        }
                        if (string.IsNullOrEmpty(customizeInvoiceClassifyRemark) && !string.IsNullOrEmpty(credit.Note))
                        {
                            customizeInvoiceClassifyRemark = credit.Note;
                        }
                        if (string.IsNullOrEmpty(credit.ShipmentCode) && !string.IsNullOrEmpty(applyCode))
                        {
                            credit.ShipmentCode = applyCode;
                        }
                        if (input.Source == 0)
                        {
                            credit.InvoiceStatus = InvoiceStatusEnum.invoiced;
                        }
                        else
                        {
                            var creditDetailsTemp = creditDetails.Where(p => p.CreditId == credit.Id).ToList();
                            //查询未开票金额汇总
                            var noInvoiceAmountTotal = creditDetailsTemp.Sum(p => p.NoInvoiceAmount);
                            //查询本次开票金额汇总
                            var currentInvoiceAmountTotal = input.OriginDetailList.Where(p => p.CreditBillCode == credit.BillCode).Sum(p => p.NoInvoiceAmount);
                            if (noInvoiceAmountTotal == currentInvoiceAmountTotal)
                            {
                                //0元应收明细处理
                                var zoreCreditDetails = creditDetailsTemp.Where(p => p.Amount == 0).ToList();
                                if (zoreCreditDetails.Any())
                                {
                                    var zoreCreditDetailIds = zoreCreditDetails.Select(p => p.Id).ToList();
                                    var readlyZoreCreditDetailIds = new List<Guid>();
                                    var zoreCreditDetailIds1 = invoiceCreditDetailIds.Where(p => zoreCreditDetailIds.Contains(p)).ToList();
                                    if (zoreCreditDetailIds1.Any())
                                    {
                                        readlyZoreCreditDetailIds.AddRange(zoreCreditDetailIds1);
                                    }
                                    var currentCreditDetailIdsTemp = input.OriginDetailList.Where(p => p.CreditBillCode == credit.BillCode).Select(p => p.CreditDetailId).ToList();
                                    var zoreCreditDetailIds2 = currentCreditDetailIdsTemp.Where(p => zoreCreditDetailIds.Contains(p)).ToList();
                                    if (zoreCreditDetailIds2.Any())
                                    {
                                        readlyZoreCreditDetailIds.AddRange(zoreCreditDetailIds2);
                                    }
                                    if (readlyZoreCreditDetailIds.Distinct().Count() >= zoreCreditDetailIds.Count())
                                    {
                                        credit.InvoiceStatus = InvoiceStatusEnum.invoiced;
                                    }
                                }
                                else
                                {
                                    credit.InvoiceStatus = InvoiceStatusEnum.invoiced;
                                }
                            }
                        }
                    }
                    var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        ids = new List<string> { credits.First().CompanyId.ToString() }
                    })).FirstOrDefault();
                    var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        id = credits.First().CustomerId.ToString()
                    });
                    string attachFileIds = await GetAttachFileIds(orderNoStrs);

                    var code = input.DetailList.First().RelateCode;
                    var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = code.Split('-')[0],
                        BillType = "CI",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1,
                        CompanyCode = companyInfo.nameCode
                    });
                    if (outPut.Status)
                    {
                        var outPutClassify = await _codeGenClient.ApplyCode(new ApplyCodeInput
                        {
                            BusinessArea = code.Split('-')[0],
                            BillType = "CIC",
                            SysMonth = companyInfo.sysMonth,
                            DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                            Num = 1,
                            CompanyCode = companyInfo.nameCode
                        });
                        var classifyId = Guid.NewGuid();
                        //添加分类
                        var invoiceClassify = new CustomizeInvoiceClassifyPo
                        {
                            Id = classifyId,
                            BillCode = outPutClassify.Codes.First(),
                            CompanyId = Guid.Parse(companyInfo.companyId),
                            CompanyName = companyInfo.companyName,
                            CustomerId = credits.First().CustomerId.Value,
                            CustomerName = credits.First().CustomerName,
                            CreatedBy = input.CreateBy,
                            Status = CustomizeInvoiceStatusEnum.WaitSubmit,
                            AttachFileIds = attachFileIds,
                            Remark = customizeInvoiceClassifyRemark,
                            Classify = CustomizeInvoiceClassifyEnum.Credit,
                            CreditSaleSubType = input.CreditSaleSubType,//销售应收子类型，旺店通
                            SaleSystemName = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.SaleSystemName)).Select(p => p.SaleSystemName).Distinct()),

                        };
                        await _db.CustomizeInvoiceClassify.AddAsync(invoiceClassify);

                        var customizeInvoiceItem = new CustomizeInvoiceItem()
                        {
                            Id = customizeInvoiceItemId,
                            Code = outPut.Codes.First(),
                            CustomerId = invoiceClassify.CustomerId.ToString(),
                            CustomerName = invoiceClassify.CustomerName,
                            CompanyId = invoiceClassify.CompanyId,
                            CompanyName = invoiceClassify.CompanyName,
                            BillDate = DateTime.Now,
                            NameCode = companyInfo.nameCode,
                            IsPush = false,
                            IsInvoiced = false,
                            Status = 0,
                            Remark = string.Empty,
                            InvoiceType = (InvoiceTypeEnum)(int.Parse(customer?.customerInvoices?.FirstOrDefault(t => t.isInvoiceUnit == 1)?.salesInvoiceDetails ?? "0")),
                            CustomizeInvoiceClassifyId = classifyId
                        };
                        customizeInvoiceItem.CreateBy(input.CreateBy);
                        var produnctAndAgentInputs = new List<ProductIdAndAgentIdInput>();
                        foreach (var item in input.DetailList)
                        {
                            if (!string.IsNullOrEmpty(item.AgentId) && item.AgentId.Contains(','))
                            {
                                foreach (var agentId in item.AgentId.Split(',').ToList())
                                {
                                    if (!string.IsNullOrEmpty(agentId))
                                    {
                                        produnctAndAgentInputs.Add(new ProductIdAndAgentIdInput()
                                        {
                                            agentId = agentId,
                                            productId = item.ProductId
                                        });
                                    }
                                }
                            }
                            else
                            {
                                produnctAndAgentInputs.Add(new ProductIdAndAgentIdInput()
                                {
                                    agentId = item.AgentId,
                                    productId = item.ProductId
                                });
                            }
                            //如果数量是正数，单价为负数，则改为数量为负数，单价为正数
                            if (item.Quantity > 0 && item.Price < 0)
                            {
                                item.Quantity = -Math.Abs(item.Quantity);
                                item.Price = Math.Abs(item.Price);
                            }
                        }
                        produnctAndAgentInputs = produnctAndAgentInputs.Distinct().ToList();
                        // 昆明致新康德医疗供应链管理有限公司 自动生成备注
                        if (customizeInvoiceItem.CompanyName == "昆明致新康德医疗供应链管理有限公司")
                        {
                            string remark = string.Empty;
                            var cicInput = new CustomizeInvoiceClassifyInput();
                            var classifyOutput = new CustomizeInvoiceClassifyOutput();
                            classifyOutput.CustomerId = Guid.Parse(customizeInvoiceItem.CustomerId);
                            cicInput.classifyOutput = classifyOutput;
                            var ret = await BeforeSubmitClassfiy(cicInput);
                            if (ret.Code == CodeStatusEnum.Success && ret.Data != null && ret.Data.Any())
                            {
                                customerInvoice customerInvoice = ret.Data.FirstOrDefault();
                                if (customerInvoice != null)
                                {
                                    // 组装备注
                                    remark += string.Concat("购买方地址:", customerInvoice.invoiceAddr, ";    电话:", customerInvoice.invoiceTel, ";");
                                    remark += string.Concat("\n购方开户银行:", customerInvoice.invoiceBank, ";    银行账号:", customerInvoice.invoiceBankNo, ";");
                                }
                            }

                            var companyInvoices = await _bDSApiClient.GetCompanyById(customizeInvoiceItem.CompanyId.Value.ToString());
                            if (companyInvoices != null && companyInvoices.Any())
                            {
                                var companyInvoice = companyInvoices.FirstOrDefault();
                                remark += string.Concat("\n销售方地址:", companyInvoice.customerInvoiceAddr, ";    电话:", companyInvoice.customerInvoiceTel, ";");
                                remark += string.Concat("\n销方开户银行:", companyInvoice.customerInvoiceBank, ";    银行账号:", companyInvoice.customerInvoiceBankNo);
                            }
                            customizeInvoiceItem.Remark = remark;
                        }
                        else if (customizeInvoiceItem?.CompanyName == "建发致新智慧（上海）医疗管理有限公司" && customer?.customerName == "郑州市中心医院")
                        {
                            string remark = string.Empty;

                            List<string?> storeOutCodes = new List<string?>();
                            foreach (var orderNo in orderNoStrs)
                            {
                                if (!string.IsNullOrEmpty(orderNo))
                                {
                                    if (orderNo.Contains(','))
                                    {
                                        storeOutCodes.AddRange(orderNo.Split(','));
                                    }
                                    else
                                    {
                                        storeOutCodes.Add(orderNo);
                                    }
                                }
                            }
                            var storeOuts = await _inventoryApiClient.QueryStoreOutByCodes(storeOutCodes);

                            if (input.DetailList.Any(p => p.IFHighValue == 1))//有高值
                            {
                                var bdsAgents = produnctAndAgentInputs.Select(p => p.productId.Value).ToList();
                                var agents = await _bDSApiClient.GetAgentBankInfoByAgentIds(bdsAgents);

                                if (agents != null && agents.Count == 1)
                                {
                                    remark += $"{agents.First().agentAbbr} {DateTime.Now.ToString("yyyy")}年{DateTime.Now.Month - 1}月高值消耗";
                                    if (storeOuts != null && storeOuts.Any())
                                    {
                                        List<string> lotNos = new List<string>();
                                        foreach (var storeOut in storeOuts)
                                        {
                                            if (storeOut != null && storeOut.Details != null && storeOut.Details.Any())
                                            {
                                                var nos = storeOut!.Details.Where(p => !string.IsNullOrEmpty(p.lotNo)).Select(p => p.lotNo);
                                                lotNos.AddRange(nos);
                                            }
                                        }

                                        lotNos.Distinct();
                                        if (lotNos.Any())
                                        {
                                            remark += string.Concat(" 批号 ", string.Join(",", lotNos));
                                        }
                                    }
                                }
                                else if (agents != null && agents.Count > 1)
                                {
                                    for (int i = 0; i < agents.Count; i++)
                                    {
                                        var agent = agents[i];
                                        var agentRelateCodes = input.DetailList.Where(x => !string.IsNullOrEmpty(x.AgentId) && x.AgentId.Equals(agent.agentId.ToString(), StringComparison.OrdinalIgnoreCase))?.Select(o => o.RelateCode);
                                        var agentStoreOuts = storeOuts.Where(x => agentRelateCodes.Contains(x.storeOutCode) && x.agentId == agent.agentId);

                                        remark += i == 0 ? $"{agent.agentAbbr} {DateTime.Now.ToString("yyyy")}年{DateTime.Now.Month - 1}月高值消耗" : $"{agent.agentAbbr}";
                                        if (agentStoreOuts != null && agentStoreOuts.Any())
                                        {
                                            List<string> lotNos = new List<string>();
                                            foreach (var storeOut in agentStoreOuts)
                                            {
                                                if (storeOut != null && storeOut.Details != null && storeOut.Details.Any())
                                                {
                                                    var nos = storeOut!.Details.Where(p => p.agentId == agent.agentId && !string.IsNullOrEmpty(p.lotNo)).Select(p => p.lotNo);
                                                    if (nos != null && nos.Any())
                                                    {
                                                        lotNos.AddRange(nos);
                                                    }
                                                }
                                            }

                                            lotNos.Distinct();
                                            if (lotNos.Any())
                                            {
                                                remark += $" 批号 {string.Join(",", lotNos)}\n";
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                if (storeOuts != null && storeOuts.Any())
                                {
                                    List<string> lotNos = new List<string>();
                                    foreach (var storeOut in storeOuts)
                                    {
                                        if (storeOut != null && storeOut.Details != null && storeOut.Details.Any())
                                        {
                                            var nos = storeOut!.Details.Where(p => !string.IsNullOrEmpty(p.lotNo)).Select(p => p.lotNo);
                                            lotNos.AddRange(nos);
                                        }
                                    }

                                    lotNos.Distinct();
                                    if (lotNos.Any())
                                    {
                                        remark += string.Concat("批号 ", string.Join(",", lotNos));
                                    }
                                }
                            }
                            customizeInvoiceItem.Remark = remark.Length > 200 ? remark.Substring(0, 200) : remark;
                        }

                        var productNoList = await _bDSApiClient.GetByNos(
                                                                input.DetailList.Select(p => p.ProductNo).ToList(),
                                                                input.DetailList.FirstOrDefault().CompanyId.ToString(),
                                                                produnctAndAgentInputs);
                        var customizeInvoiceDetails = input.DetailList.Adapt<List<CustomizeInvoiceDetail>>();
                        int sortIndex = 1;

                        foreach (var item in customizeInvoiceDetails)
                        {
                            if (string.IsNullOrEmpty(item.ProductNo))
                            {
                                var origin = input.OriginDetailList.FirstOrDefault(p => p.ProductId == item.ProductId);
                                if (origin != null)
                                {
                                    item.ProductNo = origin.ProductNo;
                                }
                            }
                            item.Sort = sortIndex;
                            item.PackUnit = item.PackUnit ?? "";
                            var product = productNoList.Find(p => p.productNo == item.ProductNo);
                            if (product != null)
                            {
                                if (product.ifTool != 3)
                                {
                                    item.TaxTypeNo = product.taxClassCode;
                                }
                                else
                                {
                                    item.TaxTypeNo = product.taxControlCode;
                                }
                            }
                            item.Id = Guid.NewGuid();
                            item.CustomizeInvoiceItemId = customizeInvoiceItemId;
                            item.Specification = item.Specification ?? "";
                            item.OriginProductName = item.OriginProductName;
                            if (!item.ProductId.HasValue)
                            {
                                var origin = input.OriginDetailList.FirstOrDefault(p => p.ProductNo == item.ProductNo);
                                if (origin != null)
                                {
                                    item.ProductId = origin.ProductId;
                                }
                            }
                            item.Value = Math.Round(item.Quantity * item.Price, 2);
                            item.TaxAmount = (item.Value / (1 + (item.TaxRate / 100))) * (item.TaxRate / 100);
                            sortIndex++;
                        }
                        customizeInvoiceItem.InvoiceTotalAmount = customizeInvoiceDetails.Sum(t => t.Value);
                        await _customizeInvoiceItemRepository.AddAsync(customizeInvoiceItem);

                        var origindetailList = input.OriginDetailList.Adapt<List<CustomizeInvoiceSubDetail>>();
                        var originDetailIds = origindetailList.Select(p => p.OriginDetailId).ToList();
                        var originCreditBillCodes = origindetailList.Select(p => p.CreditBillCode).ToList();
                        //var invoiceRedDetailPos = await _db.CustomizeInvoiceRedDetail.Where(p => originCreditBillCodes.Contains(p.CreditBillCode)).ToListAsync();

                        var customizeInvoiceCredits = new List<CustomizeInvoiceCreditPo>();

                        var originDetailIdsOfCds = customizeInvoiceDetails.Select(p => p.OriginDetailId).ToList();
                        var originDetailIdsOfCdStrs = string.Join(",", originDetailIdsOfCds);
                        var originDetailIdsOfCdLst = originDetailIdsOfCdStrs.Split(",").ToList();
                        var errorLst = new List<string>();
                        if (originDetailIdsOfCdLst.Count == originDetailIdsOfCdLst.Distinct().Count())
                        {
                            //分配应收单数量(老逻辑)
                            foreach (var odl in origindetailList)
                            {
                                var creditDetail = creditDetails.FirstOrDefault(p =>
                                                                                p.ProductId == odl.ProductId &&
                                                                                p.Credit.BillCode == odl.CreditBillCode &&
                                                                                p.OriginDetailId == odl.OriginDetailId);
                                if (creditDetail != null)
                                {
                                    var detailListTemp = customizeInvoiceDetails.Where(dl => dl.OriginDetailId.Contains(odl.OriginDetailId)).ToList();
                                    var customizeInvoiceCreditPo = new CustomizeInvoiceCreditPo
                                    {
                                        Id = Guid.NewGuid(),
                                        CreatedBy = input.CreateBy,
                                        CreditCode = odl.CreditBillCode,
                                        CustomizeInvoiceDetailAmount = creditDetail.NoInvoiceAmount.Value,
                                        CustomizeInvoiceItemCode = customizeInvoiceItem.Code,
                                        CustomizeInvoiceItemId = customizeInvoiceItem.Id,
                                        ProductId = creditDetail.ProductId,
                                        ProductNo = creditDetail.ProductNo,
                                        Quantity = creditDetail.Quantity,
                                        Price = creditDetail.Price,
                                        OriginDetailId = creditDetail.OriginDetailId,
                                        CustomizeInvoiceDetailId = detailListTemp.First().Id,
                                        CreditDetailId = creditDetail.Id,
                                        CreditDetailAmount = creditDetail.Amount,
                                        Value = creditDetail.Credit.Value,
                                    };
                                    (int ret, string msg) = InitCreditDetailInvoiceAmount(input, creditInvoiceDetailTemps, creditDetail);
                                    if (input.Source != 0)
                                    {
                                        var creditInvoiceDetailTemp = creditInvoiceDetailTemps.Where(p => p.CreditDetailId == creditDetail.Id).FirstOrDefault();
                                        customizeInvoiceCreditPo.CustomizeInvoiceDetailAmount = creditInvoiceDetailTemp.NoInvoiceAmount.Value;
                                    }
                                    customizeInvoiceCredits.Add(customizeInvoiceCreditPo);
                                    if (ret == 0)
                                    {
                                        errorLst.Add(msg);
                                    }
                                }
                                else
                                {
                                    errorLst.Add($"保存失败，原因：应收单：{odl.CreditBillCode}没有找到货号：{odl.ProductNo}数据");
                                }
                            }
                        }
                        else
                        {
                            var oneDetailList = customizeInvoiceDetails.Where(p => !p.OriginDetailId.Contains(",")).ToList();
                            var manyDetailList = customizeInvoiceDetails.Where(p => p.OriginDetailId.Contains(",")).ToList();
                            //先把没有合并明细的写入customizeInvoiceCredits表
                            foreach (var item in oneDetailList)
                            {
                                var creditDetail = creditDetails.FirstOrDefault(p => p.OriginDetailId == item.OriginDetailId);
                                var customizeInvoiceCreditPo = new CustomizeInvoiceCreditPo
                                {
                                    Id = Guid.NewGuid(),
                                    CreatedBy = input.CreateBy,
                                    CreditCode = item.CreditBillCode,
                                    CustomizeInvoiceDetailAmount = item.Value,
                                    CustomizeInvoiceItemCode = customizeInvoiceItem.Code,
                                    CustomizeInvoiceItemId = customizeInvoiceItem.Id,
                                    ProductId = creditDetail.ProductId,
                                    ProductNo = creditDetail.ProductNo,
                                    Quantity = creditDetail.Quantity,
                                    Price = creditDetail.Price,
                                    OriginDetailId = creditDetail.OriginDetailId,
                                    CustomizeInvoiceDetailId = item.Id,
                                    CreditDetailId = creditDetail.Id,
                                    CreditDetailAmount = creditDetail.Amount,
                                    Value = creditDetail.Credit.Value,
                                };
                                (int ret, string msg) = InitCreditDetailInvoiceAmount(input, creditInvoiceDetailTemps, creditDetail);
                                //if (input.Source != 0)
                                //{
                                //    var creditInvoiceDetailTemp = creditInvoiceDetailTemps.Where(p => p.CreditDetailId == creditDetail.Id).FirstOrDefault();
                                //    customizeInvoiceCreditPo.CustomizeInvoiceDetailAmount = item.Value creditInvoiceDetailTemp.NoInvoiceAmount.Value;
                                //}
                                customizeInvoiceCredits.Add(customizeInvoiceCreditPo);
                                if (ret == 0)
                                {
                                    errorLst.Add(msg);
                                }
                            }
                            //先把有合并明细的写入customizeInvoiceCredits表
                            foreach (var item in manyDetailList)
                            {
                                var originDetailIdsTemp = item.OriginDetailId.Split(",").ToList();
                                var originDetailIdOne = string.Empty;
                                var noInvoiceAmountOne = 0M;
                                foreach (var subitem in originDetailIdsTemp)
                                {
                                    var countTemp = originDetailIdsOfCdLst.Where(p => p == subitem).Count();
                                    if (countTemp == 1)
                                    {
                                        //先把合并中只有一个明细的写入 
                                        var creditDetail = creditDetails.FirstOrDefault(p => p.OriginDetailId == subitem);
                                        var customizeInvoiceCreditPo = new CustomizeInvoiceCreditPo
                                        {
                                            Id = Guid.NewGuid(),
                                            CreatedBy = input.CreateBy,
                                            CreditCode = creditDetail.Credit.BillCode,
                                            CustomizeInvoiceDetailAmount = creditDetail.NoInvoiceAmount.Value,
                                            CustomizeInvoiceItemCode = customizeInvoiceItem.Code,
                                            CustomizeInvoiceItemId = customizeInvoiceItem.Id,
                                            ProductId = creditDetail.ProductId,
                                            ProductNo = creditDetail.ProductNo,
                                            Quantity = creditDetail.Quantity,
                                            Price = creditDetail.Price,
                                            OriginDetailId = creditDetail.OriginDetailId,
                                            CustomizeInvoiceDetailId = item.Id,
                                            CreditDetailId = creditDetail.Id,
                                            CreditDetailAmount = creditDetail.Amount,
                                            Value = creditDetail.Credit.Value,
                                        };
                                        (int ret2, string msg2) = InitCreditDetailInvoiceAmount(input, creditInvoiceDetailTemps, creditDetail);
                                        if (input.Source != 0)
                                        {
                                            var creditInvoiceDetailTemp = creditInvoiceDetailTemps.Where(p => p.CreditDetailId == creditDetail.Id).FirstOrDefault();
                                            customizeInvoiceCreditPo.CustomizeInvoiceDetailAmount = creditInvoiceDetailTemp.NoInvoiceAmount.Value;
                                            noInvoiceAmountOne = creditInvoiceDetailTemp.NoInvoiceAmount.Value;
                                        }
                                        else
                                        {
                                            noInvoiceAmountOne = customizeInvoiceCreditPo.CustomizeInvoiceDetailAmount;
                                        }
                                        customizeInvoiceCredits.Add(customizeInvoiceCreditPo);
                                        if (ret2 == 0)
                                        {
                                            errorLst.Add(msg2);
                                        }
                                        originDetailIdOne = subitem;
                                        break;
                                    }
                                }
                                var otherOriginDetailId = originDetailIdsTemp.First(p => p != originDetailIdOne);
                                //再写把合并中只有一个明细的写入 
                                var otherCreditDetail = creditDetails.FirstOrDefault(p => p.OriginDetailId == otherOriginDetailId);
                                var otherCustomizeInvoiceCreditPo = new CustomizeInvoiceCreditPo
                                {
                                    Id = Guid.NewGuid(),
                                    CreatedBy = input.CreateBy,
                                    CreditCode = otherCreditDetail.Credit.BillCode,
                                    CustomizeInvoiceDetailAmount = item.Value - noInvoiceAmountOne,
                                    CustomizeInvoiceItemCode = customizeInvoiceItem.Code,
                                    CustomizeInvoiceItemId = customizeInvoiceItem.Id,
                                    ProductId = otherCreditDetail.ProductId,
                                    ProductNo = otherCreditDetail.ProductNo,
                                    Quantity = otherCreditDetail.Quantity,
                                    Price = otherCreditDetail.Price,
                                    OriginDetailId = otherCreditDetail.OriginDetailId,
                                    CustomizeInvoiceDetailId = item.Id,
                                    CreditDetailId = otherCreditDetail.Id,
                                    CreditDetailAmount = otherCreditDetail.Amount,
                                    Value = otherCreditDetail.Credit.Value,
                                };
                                customizeInvoiceCredits.Add(otherCustomizeInvoiceCreditPo);
                                if (!oneDetailList.Select(p => p.OriginDetailId).Contains(otherOriginDetailId))
                                {
                                    (int ret, string msg) = InitCreditDetailInvoiceAmount(input, creditInvoiceDetailTemps, otherCreditDetail);
                                    if (ret == 0)
                                    {
                                        errorLst.Add(msg);
                                    }
                                } 
                            }
                        }

                        if (errorLst.Count > 0)
                        {
                            _easyCaching.Remove(cachekey);
                            errorLst = errorLst.Distinct().ToList();
                            return (0, $"操作失败，原因：{string.Join("；", errorLst)}");
                        }
                        //合并0元明细逻辑
                        var zoreDetailLst = customizeInvoiceDetails.Where(p => p.Quantity == 0).ToList();
                        var notZoreDetailLst = customizeInvoiceDetails.Where(p => p.Quantity != 0).ToList();
                        if (zoreDetailLst.Any())
                        {
                            var notZoreDetailIds = notZoreDetailLst.Select(p => p.Id).ToList();

                            var customizeInvoiceCredits1 = customizeInvoiceCredits.Where(p => notZoreDetailIds.Contains(p.CustomizeInvoiceDetailId)).ToList();
                            var creditCodes1 = customizeInvoiceCredits1.Select(p => p.CreditCode).ToList();
                            var creditDetailIds1 = customizeInvoiceCredits1.Select(p => p.CreditDetailId).ToList();
                            var creditDetail1 = creditDetails.Where(p => creditDetailIds1.Contains(p.Id)).Distinct().ToList();

                            foreach (var item in zoreDetailLst)
                            {
                                var needUpdateCustomizeInvoiceCredits = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == item.Id).ToList();
                                var creditCodes = needUpdateCustomizeInvoiceCredits.Select(p => p.CreditCode).ToList();
                                var creditDetailIdsTemp = needUpdateCustomizeInvoiceCredits.Select(p => p.CreditDetailId).ToList();
                                var originalIds = creditDetails.Where(p => creditDetailIds.Contains(p.Id) && p.OriginalId.HasValue)
                                                  .Select(p => p.OriginalId).Distinct().ToList();
                                var creditDetailTemp = creditDetail1.Where(p => originalIds.Contains(p.OriginalId)).FirstOrDefault();
                                if (creditDetailTemp != null)
                                {
                                    var customizeInvoiceCreditTemp = customizeInvoiceCredits.FirstOrDefault(p => p.CreditDetailId == creditDetailTemp.Id);
                                    if (customizeInvoiceCreditTemp != null)
                                    {
                                        foreach (var subitem in needUpdateCustomizeInvoiceCredits)
                                        {
                                            subitem.CustomizeInvoiceDetailId = customizeInvoiceCreditTemp.CustomizeInvoiceDetailId;
                                        }
                                    }
                                }
                                else
                                {
                                    //随机给一个
                                    var customizeInvoiceCreditTemp = customizeInvoiceCredits.FirstOrDefault(p => p.CreditDetailId == creditDetail1.First().Id);
                                    if (customizeInvoiceCreditTemp != null)
                                    {
                                        foreach (var subitem in needUpdateCustomizeInvoiceCredits)
                                        {
                                            subitem.CustomizeInvoiceDetailId = customizeInvoiceCreditTemp.CustomizeInvoiceDetailId;
                                        }
                                    }

                                }
                            }
                        }
                        foreach (var item in notZoreDetailLst)
                        {
                            var customizeInvoiceCreditsTemp = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == item.Id).ToList();
                            var creditCodes = customizeInvoiceCreditsTemp.Select(p => p.CreditCode).Distinct();
                            item.CreditBillCode = string.Join(",", creditCodes);
                            var creditTemps = credits.Where(p => creditCodes.Contains(p.BillCode)).ToList();
                            item.OrderNo = string.Join(",", creditTemps.Select(p => p.OrderNo).Distinct());
                            item.RelateCode = string.Join(",", creditTemps.Select(p => p.RelateCode).Distinct());
                        }
                        await _customizeInvoiceDetailRepository.AddManyAsync(notZoreDetailLst);
                        if (customizeInvoiceCredits.Any())
                        {
                            await _db.CustomizeInvoiceCredits.AddRangeAsync(customizeInvoiceCredits);
                        }
                        if (input.Source == 1)
                        {
                            _db.CreditInvoiceDetailTemps.RemoveRange(creditInvoiceDetailTemps);
                        }
                        if (!string.IsNullOrEmpty(invoiceClassify.BillCode))
                        {
                            _easyCaching.Remove(cachekey);
                            await _unitOfWork.CommitAsync();
                            return (count, invoiceClassify.BillCode);
                        }
                        else
                        {
                            _easyCaching.Remove(cachekey);
                            return (0, "保存失败，单号为空");
                        }
                    }
                    else
                    {
                        _easyCaching.Remove(cachekey);
                        return (0, outPut.Msg);
                    }
                }
                else
                {
                    _easyCaching.Remove(cachekey);
                    return (0, $"保存失败,原因：{credits.First().CompanyName}公司和{credits.First().CustomerName}客户同时间有并发保存，请稍后5s后操作！");
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                return (0, ex.Message);
            }
        }

        private static (int, string) InitCreditDetailInvoiceAmount(SaveCustomizeInvoiceInput input, List<CreditInvoiceDetailTempPo> creditInvoiceDetailTemps, CreditDetailPo? creditDetail)
        {
            if (input.Source == 0)
            {
                creditDetail.NoInvoiceAmount = 0;
                creditDetail.InvoiceAmount = creditDetail.Amount;
            }
            else
            {
                var creditInvoiceDetailTemp = creditInvoiceDetailTemps.Where(p => p.CreditDetailId == creditDetail.Id).FirstOrDefault();
                if (creditInvoiceDetailTemp != null && creditInvoiceDetailTemp.NoInvoiceAmount.HasValue)
                {
                    var creditDetailNoInvoiceAmount = Math.Abs(creditDetail.NoInvoiceAmount.Value);
                    var creditInvoiceDetailTempNoInvoiceAmount = Math.Abs(creditInvoiceDetailTemp.NoInvoiceAmount.Value);
                    if (creditDetail.NoInvoiceAmount != 0)
                    {
                        creditDetail.NoInvoiceAmount = creditDetail.NoInvoiceAmount.HasValue ? creditDetailNoInvoiceAmount - creditInvoiceDetailTempNoInvoiceAmount : creditInvoiceDetailTempNoInvoiceAmount;
                        creditDetail.InvoiceAmount = Math.Abs(creditDetail.Amount.Value) - Math.Abs(creditDetail.NoInvoiceAmount.Value);
                    }
                    if (creditDetail.Amount < 0)
                    {
                        creditDetail.NoInvoiceAmount = creditDetail.NoInvoiceAmount * -1;
                        creditDetail.InvoiceAmount = creditDetail.InvoiceAmount * -1;
                    }
                }
                else
                {
                    return (0, $"应收单：{creditDetail.Credit.BillCode},货号{creditDetail.ProductNo}，没有找到开票明细数据，请刷新后操作");
                }
            }
            return (1, "");
        }

        /// <summary>
        /// 校验开票明细中数量*单价是否等于可开金额(保存后端校验)
        /// </summary>
        /// <param name="details">开票明细</param>
        /// <param name="creditDetails">应收明细</param>
        /// <param name="originDetails">原始明细</param>
        private BaseResponseData<string> MargeCheck(
            List<OriginDetailOutput> details,
            List<CreditDetailPo> creditDetails,
            List<OriginDetailOutput> originDetails,
            List<CreditInvoiceDetailTempPo> creditInvoiceDetailTemps,
            int source)
        {
            var ret = BaseResponseData<string>.Success("校验成功！");
            var errorMsg = new List<string>();
            var originDetailIdsOfCds = details.Select(p => p.OriginDetailId).ToList();
            var originDetailIdsOfCdStrs = string.Join(",", originDetailIdsOfCds);
            var originDetailIdsOfCdLst = originDetailIdsOfCdStrs.Split(",").ToList();
            if (originDetailIdsOfCdLst.Count == originDetailIdsOfCdLst.Distinct().Count()) // 去重后数量与原数量一致，则无重复
            {
                foreach (var item in details)
                {
                    var originDetailsTemp = originDetails.Where(odl => item.OriginDetailId.Contains(odl.OriginDetailId)).ToList();
                    var productIds = originDetailsTemp.Select(p => p.ProductId).ToList();
                    var originDetailIds = originDetailsTemp.Select(p => p.OriginDetailId).ToList();
                    var creditDetailsTempDB = creditDetails.Where(p => productIds.Contains(p.ProductId) && originDetailIds.Contains(p.OriginDetailId)).Select(p => new
                    {
                        p.Id,
                        p.ProductNo,
                        CreditBillCode = p.Credit.BillCode,
                        p.NoInvoiceAmount
                    }).ToList();
                    var noInvoiceAmount = creditDetailsTempDB.Sum(p => p.NoInvoiceAmount);
                    if (source != 0)
                    {
                        var creditDetailsTemp = creditInvoiceDetailTemps.Where(p => productIds.Contains(p.ProductId) && originDetailIds.Contains(p.OriginDetailId)).Select(p => new
                        {
                            Id = p.CreditDetailId.Value,
                            p.OriginDetailId,
                            p.NoInvoiceAmount
                        }).ToList();
                        noInvoiceAmount = creditDetailsTemp.Sum(p => p.NoInvoiceAmount);
                        if (creditDetailsTempDB.Any())
                        {
                            foreach (var creditDetail in creditDetailsTempDB)
                            {
                                var creditInvoiceDetailTemp = creditInvoiceDetailTemps.Where(p => p.CreditDetailId == creditDetail.Id).FirstOrDefault();
                                if (creditInvoiceDetailTemp != null)
                                {
                                    var creditDetailNoInvoiceAmount = Math.Abs(creditDetail.NoInvoiceAmount.Value);
                                    var creditInvoiceDetailTempNoInvoiceAmount = Math.Abs(creditInvoiceDetailTemp.NoInvoiceAmount.Value);
                                    if (creditDetailNoInvoiceAmount < creditInvoiceDetailTempNoInvoiceAmount)
                                    {
                                        errorMsg.Add($"应收单：{creditDetail.CreditBillCode},货号：{creditDetail.ProductNo}，未开票金额（绝对值）{creditDetailNoInvoiceAmount}<开票金额（绝对值）{creditInvoiceDetailTempNoInvoiceAmount}，请删除开票明细后操作");
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        var amountTemp = item.Quantity * item.Price;
                        if (Math.Round(amountTemp, 2) != noInvoiceAmount)
                        {
                            errorMsg.Add($"开票名称：{item.ProductName},货号：{item.ProductNo},数量{item.Quantity}*单价{item.Price}≠可开票金额{noInvoiceAmount}");
                        }
                    }
                }
                if (errorMsg.Count > 0)
                {
                    if (source != 0)
                    {
                        ret = BaseResponseData<string>.Failed(500, $"操作失败，原因：{string.Join(",", errorMsg)}，请删除开票明细后操作");
                    }
                    else
                    {
                        ret = BaseResponseData<string>.Failed(500, $"操作失败，原因：{string.Join(",", errorMsg)}请修改后或重新拉取操作！");
                    }
                }
            }
            else
            {
                var manyDetails = details.Where(p => p.OriginDetailId.Contains(",")).ToList();
                var oneDetails = details.Where(p => !p.OriginDetailId.Contains(",")).ToList();
                var readyCheckDetails = new List<OriginDetailOutput>();
                foreach (var item in oneDetails)
                {
                    var manyDetailTemps = manyDetails.Where(p => p.OriginDetailId.Contains(item.OriginDetailId)).ToList();
                    CheckKD(creditDetails, originDetails, errorMsg, item, manyDetailTemps, creditInvoiceDetailTemps, source);
                    readyCheckDetails.AddRange(manyDetailTemps);
                }
                manyDetails = manyDetails.Where(p => !readyCheckDetails.Select(x => x.OriginalId).Contains(p.OriginalId)).ToList();
                var margeManyDetails = new List<OriginDetailOutput>();
                foreach (var item in manyDetails)
                {
                    if (margeManyDetails.Contains(item))
                    {
                        continue;
                    }
                    var OriginDetails = item.OriginDetailId.Split(",").ToList();
                    var manyDetailLst = new List<OriginDetailOutput>();
                    foreach (var subitem in OriginDetails)
                    {
                        var manyDetailTemps = manyDetails.Where(p => p.OriginDetailId.Contains(subitem) && p.OriginDetailId != item.OriginDetailId).ToList();
                        manyDetailLst.AddRange(manyDetailTemps);
                        margeManyDetails.AddRange(manyDetailTemps);
                    }
                    manyDetailLst = manyDetailLst.Distinct().ToList();
                    CheckKD(creditDetails, originDetails, errorMsg, item, manyDetailLst, creditInvoiceDetailTemps, source);
                }
                if (errorMsg.Count > 0)
                {
                    if (source != 0)
                    {
                        ret = BaseResponseData<string>.Failed(500, $"操作失败，原因：{string.Join(",", errorMsg)}，请删除开票明细后操作");
                    }
                    else
                    {
                        ret = BaseResponseData<string>.Failed(500, $"操作失败，原因：{string.Join(",", errorMsg)}请修改后或重新拉取操作！");
                    }
                }
            }
            return ret;
        }


        private static void CheckKD(
            List<CreditDetailPo> creditDetails,
            List<OriginDetailOutput> originDetails,
            List<string> errorMsg,
            OriginDetailOutput? item,
            List<OriginDetailOutput> manyDetailTemps,
            List<CreditInvoiceDetailTempPo> creditInvoiceDetailTemps,
            int source)
        {
            var originDetailIds = new List<string?>();
            var amount = item.Value;
            if (manyDetailTemps.Any())
            {
                originDetailIds = manyDetailTemps.Select(p => p.OriginDetailId).ToList();
                amount = amount + manyDetailTemps.Sum(p => p.Value);
            }
            originDetailIds.Add(item.OriginDetailId);
            var originDetailIdLst = string.Join(",", originDetailIds).Split(",").Distinct().ToList();
            var originDetailIdStr = string.Join(",", originDetailIdLst);
            var originDetailsTemp = originDetails.Where(odl => originDetailIdStr.Contains(odl.OriginDetailId)).ToList();
            var productIds = originDetailsTemp.Select(p => p.ProductId).ToList();

            var creditDetailsTempDB = creditDetails.Where(p => productIds.Contains(p.ProductId) && originDetailIdLst.Contains(p.OriginDetailId)).Select(p => new
            {
                p.Id,
                p.ProductNo,
                CreditBillCode = p.Credit.BillCode,
                p.NoInvoiceAmount
            }).ToList();

            var noInvoiceAmount = creditDetailsTempDB.Sum(p => p.NoInvoiceAmount);
            if (source != 0)
            {
                var creditDetailsTemp = creditInvoiceDetailTemps.Where(p => productIds.Contains(p.ProductId) && originDetailIdLst.Contains(p.OriginDetailId)).Select(p => new
                {
                    Id = p.CreditDetailId.Value,
                    p.ProductNo,
                    CreditBillCode = p.CreditBillCode,
                    p.NoInvoiceAmount
                }).ToList();
                noInvoiceAmount = creditDetailsTempDB.Sum(p => p.NoInvoiceAmount);
                if (creditDetailsTempDB.Any())
                {
                    foreach (var creditDetail in creditDetailsTempDB)
                    {
                        var creditInvoiceDetailTemp = creditInvoiceDetailTemps.Where(p => p.CreditDetailId == creditDetail.Id).FirstOrDefault();
                        if (creditInvoiceDetailTemp != null)
                        {
                            var creditDetailNoInvoiceAmount = Math.Abs(creditDetail.NoInvoiceAmount.Value);
                            var creditInvoiceDetailTempNoInvoiceAmount = Math.Abs(creditInvoiceDetailTemp.NoInvoiceAmount.Value);
                            if (creditDetailNoInvoiceAmount < creditInvoiceDetailTempNoInvoiceAmount)
                            {
                                errorMsg.Add($"应收单：{creditDetail.CreditBillCode},货号：{creditDetail.ProductNo}，未开票金额（绝对值）{creditDetailNoInvoiceAmount}<开票金额（绝对值）{creditInvoiceDetailTempNoInvoiceAmount}");
                            }
                        }
                    }
                }
            }
            else
            {
                if (Math.Round(amount, 2) != noInvoiceAmount)
                {
                    errorMsg.Add($"开票名称：{item.ProductName},数量{item.Quantity}*单价{item.Price}≠可开票金额；");
                    if (manyDetailTemps.Any())
                    {
                        foreach (var subitem in manyDetailTemps)
                        {
                            errorMsg.Add($"开票名称：{subitem.ProductName},数量{subitem.Quantity}*单价{subitem.Price}≠可开票金额；");
                        }
                    }
                }
            }

        }

        public BaseResponseData<string> MargeCheck(List<OriginDetailOutput> details, int source)
        {
            var ret = BaseResponseData<string>.Success("校验成功！");
            var errorMsg = new List<string>();
            foreach (var item in details)
            {
                var amountTemp = item.Quantity * item.Price;
                if (Math.Round(amountTemp, 2) != Math.Round(item.NoInvoiceAmount.Value, 2))
                {
                    if (source == 1)//应收明细
                    {
                        errorMsg.Add($"开票名称：{item.ProductName},数量{item.Quantity}*单价{item.Price}≠开票金额{item.NoInvoiceAmount}；");
                    }
                    else
                    {
                        errorMsg.Add($"开票名称：{item.ProductName},数量{item.Quantity}*单价{item.Price}≠可开票金额{item.NoInvoiceAmount}；");
                    }
                }
            }
            if (errorMsg.Count > 0)
            {
                if (source == 1)//应收明细
                {
                    ret = BaseResponseData<string>.Failed(500, $"操作失败，原因：{string.Join(",", errorMsg)}请重新操作！");
                }
                else
                {
                    ret = BaseResponseData<string>.Failed(500, $"操作失败，原因：{string.Join(",", errorMsg)}请修改后或重新拉取操作！");
                }
            }
            return ret;
        }
        private async Task<string> GetAttachFileIds(List<string?> orderNoStrs)
        {
            var orderNos = new List<string>();
            var attachFileIds = string.Empty;
            foreach (var item in orderNoStrs)
            {
                if (!string.IsNullOrEmpty(item))
                {
                    orderNos.AddRange(item.Split(','));
                }
            }
            if (orderNos.Any())
            {
                var saleOuts = await _sellApiClient.GetSaleList(new GetSaleListInput
                {
                    BillCodes = orderNos,
                    PageNum = 1,
                    PageSize = int.MaxValue
                });
                if (saleOuts != null)
                {
                    var attachFileIdLst = new List<string>();
                    foreach (var item in saleOuts)
                    {
                        var temp = item.SettlementFiles?.Select(p => p.AttachFileId).ToList();
                        if (temp != null && temp.Any())
                        {
                            attachFileIdLst.AddRange(temp);
                        }
                    }
                    if (attachFileIdLst != null && attachFileIdLst.Any())
                    {
                        attachFileIds = string.Join(",", attachFileIdLst);
                    }
                }
            }
            return attachFileIds;
        }

        /// <summary>
        /// 删除开票单
        /// </summary>
        public async Task<(int, string)> DeleteCustomizeInvoice(DeleteCustomizeInvoiceInput input)
        {
            try
            {
                int count = 1;
                var customizeInvoiceItems = await _db.CustomizeInvoiceItem.Where(c => c.Id == input.CustomizeInvoiceItemId).AsNoTracking().ToListAsync();
                if (customizeInvoiceItems != null && customizeInvoiceItems.Any())
                {
                    var customizeInvoiceItem = customizeInvoiceItems.First();

                    var relationCodes = customizeInvoiceItems.Select(p => p.RelationCode);
                    List<CustomizeInvoiceItemPo> oldInvoiceItemPos = new List<CustomizeInvoiceItemPo>();
                    if (relationCodes != null && relationCodes.Any())
                    {
                        oldInvoiceItemPos = await _db.CustomizeInvoiceItem.Where(c => relationCodes.Contains(c.Code)).AsNoTracking().ToListAsync();
                    }
                    foreach (var item in customizeInvoiceItems)
                    {
                        if (item.Status == CustomizeInvoiceStatusEnum.WaitSubmit)
                        {
                            await _customizeInvoiceDetailRepository.DeleteByItemIdAsync(input.CustomizeInvoiceItemId);
                            await _customizeInvoiceItemRepository.DeleteAsync(input.CustomizeInvoiceItemId);
                            if (!string.IsNullOrEmpty(item.RelationCode))
                            {
                                var invoiceItemOld = oldInvoiceItemPos.Find(p => p.Code == item.RelationCode);
                                if (invoiceItemOld != null)
                                {
                                    invoiceItemOld.ChangedStatus = null;
                                    _db.CustomizeInvoiceItem.Update(invoiceItemOld);
                                }
                            }
                        }
                        else { return (0, "操作失败，原因：只有待提交的单子才能删除！"); }
                    }
                    if (!string.IsNullOrEmpty(customizeInvoiceItem.OARequestId))
                    {
                        //删除OA流程
                        await _weaverApiClient.DelWorkFlow(customizeInvoiceItem.CreatedBy, Convert.ToInt32(customizeInvoiceItem.OARequestId));
                    }
                    await _unitOfWork.CommitAsync();
                }
                else { return (0, "开票单不存在，请检查！"); }

                return (count, "");
            }
            catch (Exception ex)
            {
                return (0, ex.Message);
            }
        }

        /// <summary>
        /// 编辑开票单
        /// </summary>
        public async Task<(int, string)> EditCustomizeInvoice(EditCustomizeInvoiceInput input)
        {
            try
            {
                int count = 1;
                var customizeInvoiceItem = await _db.CustomizeInvoiceItem.Where(p => p.Id == input.CustomizeInvoiceItemId).FirstOrDefaultAsync();
                if (customizeInvoiceItem != null)
                {
                    if (customizeInvoiceItem.RelationType == CustomizeInvoiceRelationTypeEunm.PartRedOffset && input.RedOffsetReason == 2)
                    {
                        return (-1, "操作失败，原因：部分红冲不允许修改状态开票有误");
                    }
                    if (customizeInvoiceItem.Status != CustomizeInvoiceStatusEnum.WaitSubmit)
                    {
                        return (-1, "操作失败，原因：只有待提交状态才允许修改");
                    }
                    var noRequiredType = new List<InvoiceTypeEnum> { InvoiceTypeEnum.DZUniversal,
                        InvoiceTypeEnum.RollTicket,
                        InvoiceTypeEnum.ZZUniversal 
                        //InvoiceTypeEnum.DigitalCircuitUniversal, 
                        //InvoiceTypeEnum.DigitalCircuitTiket
                        };
                    if (customizeInvoiceItem.IsNoRedConfirm != 1)
                    {

                        if (input.RedOffsetReason == 4 && string.IsNullOrEmpty(input.RedOffsetCode) && !noRequiredType.Contains(input.InvoiceType))
                            return (-1, "操作失败，原因：销售折让红字信息表编号必填");


                        if (customizeInvoiceItem.RedOffsetReason.HasValue && customizeInvoiceItem.InvoiceTotalAmount < 0)
                        {
                            var digitalCircuitType = new List<InvoiceTypeEnum> {
                            InvoiceTypeEnum.DigitalCircuitUniversal,
                            InvoiceTypeEnum.DigitalCircuitTiket
                         };

                            if (string.IsNullOrEmpty(input.RedOffsetCode) && digitalCircuitType.Contains(input.InvoiceType))
                                return (-1, "操作失败，原因：数电票红字信息表编号必填");
                        }
                    }
                    //判断蓝字发票对应InvoiceCredit.Credit.CreditSaleSubType 的应收单CreditSaleSubType，是否和当前提交开票 CustomizeInvoiceClassify.CreditSaleSubType的类型一致
                    if (customizeInvoiceItem != null && !string.IsNullOrEmpty(customizeInvoiceItem.InvoiceCode))
                    {
                        var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.FirstOrDefaultAsync(c => c.Id == customizeInvoiceItem.CustomizeInvoiceClassifyId);
                        if (customizeInvoiceClassify != null && customizeInvoiceClassify.CreditSaleSubType.HasValue)
                        {
                            var (checkResult, checkMessage) = await CheckCreditSaleSubTypeConsistency(customizeInvoiceItem.InvoiceCode, customizeInvoiceClassify.CreditSaleSubType);
                            if (checkResult == -1)
                            {
                                return (checkResult, checkMessage);
                            }
                        }
                    }
                    var parentItem = await _db.CustomizeInvoiceItem.Where(p => p.Code == customizeInvoiceItem.RelationCode).FirstOrDefaultAsync();
                    if (!customizeInvoiceItem.IsPush)
                    {
                        customizeInvoiceItem.InvoiceType = input.InvoiceType;//开票类型
                        customizeInvoiceItem.Remark = input.Remark;
                        customizeInvoiceItem.ApproveRemark = input.ApproveRemark;
                        if (customizeInvoiceItem.RedOffsetReason.HasValue || customizeInvoiceItem.InvoiceTotalAmount < 0) //修改的红冲票
                        {
                            customizeInvoiceItem.RedOffsetCode = input.RedOffsetCode;
                            customizeInvoiceItem.InvoiceNo = input.InvoiceNo;
                            customizeInvoiceItem.InvoiceCode = input.InvoiceCode;
                            if (parentItem != null && parentItem.ChangedStatus != CustomizeInvoiceChangedStatusEnum.PartRedOffset)
                            {
                                customizeInvoiceItem.BlueRedInvoiceAmount = input.BlueRedInvoiceAmount;
                            }
                            else if (customizeInvoiceItem.InvoiceTotalAmount < 0)
                            {
                                customizeInvoiceItem.BlueRedInvoiceAmount = input.BlueRedInvoiceAmount;
                            }
                            customizeInvoiceItem.RedOffsetOpter = input.RedOffsetOpter;
                            customizeInvoiceItem.RedOffsetReason = input.RedOffsetReason;

                        }
                        await _unitOfWork.CommitAsync();
                    }
                    else { return (-1, "已提交的开票单不能编辑！"); }
                }
                else { return (-1, "开票单不存在，请检查！"); }

                return (count, "");
            }
            catch (Exception ex)
            {
                return (-1, ex.Message);
            }
        }
        /// <summary>
        /// 判断蓝字发票对应InvoiceCredit.Credit.CreditSaleSubType的应收单CreditSaleSubType，
        /// 是否和当前提交开票CustomizeInvoiceClassify.CreditSaleSubType的类型一致
        /// </summary>
        /// <param name="blueInvoiceCode">蓝字发票编号</param>
        /// <param name="currentCreditSaleSubType">当前提交开票的销售应收子类型</param>
        /// <returns>返回一个元组，包含检查结果（成功为1，失败为-1）和消息</returns>
        private async Task<(int, string)> CheckCreditSaleSubTypeConsistency(string blueInvoiceCode, CreditSaleSubTypeEnum? currentCreditSaleSubType)
        {
            if (string.IsNullOrEmpty(blueInvoiceCode) || !currentCreditSaleSubType.HasValue)
            {
                return (1, string.Empty); // 如果没有蓝字发票编号或当前类型为空，则不进行检查
            }

            // 查询蓝字发票对应的InvoiceCredit
            var blueInvoiceCredit = await _db.InvoiceCredits
                .Include(ic => ic.Credit)
                .FirstOrDefaultAsync(ic => ic.InvoiceCode == blueInvoiceCode);

            if (blueInvoiceCredit == null || blueInvoiceCredit.Credit == null)
            {
                return (1, string.Empty); // 如果找不到蓝字发票或对应的应收单，则不进行检查
            }

            // 获取蓝字发票对应的应收单CreditSaleSubType
            var blueCreditSaleSubType = blueInvoiceCredit.Credit.CreditSaleSubType;

            // 如果蓝字发票对应的应收单没有CreditSaleSubType，则不进行检查
            if (!blueCreditSaleSubType.HasValue)
            {
                return (1, string.Empty);
            }

            // 判断两者是否一致
            if (blueCreditSaleSubType.Value != currentCreditSaleSubType.Value)
            {
                string blueTypeName = blueCreditSaleSubType.Value == CreditSaleSubTypeEnum.personal ? "个人消费者" : "平台";
                string currentTypeName = currentCreditSaleSubType.Value == CreditSaleSubTypeEnum.personal ? "个人消费者" : "平台";
                var msg = $"操作失败，原因：蓝字发票对应的应收单类型为{blueTypeName}，而当前提交开票的类型为{currentTypeName}，两者不一致";
                return (-1, msg);
            }

            return (1, string.Empty); // 类型一致，检查通过
        }

        /// <summary>
        /// 批量编辑备注
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> EditBatchCustomizeInvoice(EditBatchCustomizeInvoiceInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            try
            {
                var customizeInvoiceItems = await _db.CustomizeInvoiceItem.Where(p => input.CustomizeInvoiceItemIds.Contains(p.Id)).ToListAsync();
                if (customizeInvoiceItems != null)
                {
                    if (customizeInvoiceItems.Count() != customizeInvoiceItems.Count(p => p.CustomizeInvoiceClassifyId == customizeInvoiceItems.First().CustomizeInvoiceClassifyId))
                    {
                        ret = BaseResponseData<string>.Failed(500, "操作失败，原因：只有同一个开票分类下的数据才允许批量修改");
                        return ret;
                    }
                    foreach (var item in customizeInvoiceItems)
                    {
                        if (item.Status != CustomizeInvoiceStatusEnum.WaitSubmit)
                        {
                            ret = BaseResponseData<string>.Failed(500, "操作失败，原因：只有待提交状态才允许批量修改");
                            return ret;
                        }
                        item.ApproveRemark = input.ApproveRemark;
                        item.Remark = input.Remark;
                    }
                    await _unitOfWork.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<string>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 提交开票单 (推送到金碟)
        /// </summary>
        public async Task<(int, string)> SubmitCustomizeInvoice(SubmitCustomizeInvoiceInput input)
        {
            try
            {
                int count = 1;
                var customizeInvoiceItem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(input.CustomizeInvoiceItemId);
                if (customizeInvoiceItem != null)
                {
                    if (customizeInvoiceItem.CustomizeInvoiceClassifyId.HasValue)
                    {
                        (count, string msg) = await CheckedClassifyAsync(customizeInvoiceItem.CustomizeInvoiceClassifyId);
                        if (count <= 0)
                        {
                            return (count, msg);
                        }
                    }
                    var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == customizeInvoiceItem.CustomizeInvoiceClassifyId).AsNoTracking().FirstOrDefaultAsync();
                    string customerEmail = customizeInvoiceClassify == null ? string.Empty : customizeInvoiceClassify.CustomerEmail;
                    if (!customizeInvoiceItem.IsPush)
                    {
                        var customizeInvoiceDetails = await _db.CustomizeInvoiceDetail
                                                     //.Include(p => p.CustomizeInvoiceSubDetails)
                                                     .Where(c => c.CustomizeInvoiceItemId == input.CustomizeInvoiceItemId).OrderBy(p => p.CreatedTime).ThenByDescending(p => p.Price).AsNoTracking().ToListAsync();

                        var details = new List<OriginalBillEntryItem>();
                        var customizeInvoiceCredits = new List<CustomizeInvoiceCredit>();
                        if (customizeInvoiceDetails.Count(cd => string.IsNullOrWhiteSpace(cd.TaxTypeNo)) > 0)
                        {
                            return (0, $"开票明细中，税收分类编码不能为空！");
                        }
                        if (string.IsNullOrEmpty(customizeInvoiceItem.ApproveRemark))
                        {
                            if (customizeInvoiceDetails.Count(p => p.OriginProductName != p.ProductName) > 0)
                            {
                                return (0, $"存在开票品名与系统平台不一致的数据，请填写审批备注后提交！");
                            }

                        }

                        (count, string msg) = await SubmitToKingdeeAsync(customizeInvoiceDetails, customizeInvoiceItem, input.CreateBy, customizeInvoiceClassify.IsPushDefaultEmail, "Y", customerEmail, customizeInvoiceClassify.Invoiceofclassfiy);
                        if (count > 1)
                        {
                            customizeInvoiceItem.IsPush = true;
                            customizeInvoiceItem.UpdateBy(input.CreateBy);
                            if (customizeInvoiceCredits != null)
                            {
                                await _customizeInvoiceCreditRepository.AddManyAsync(customizeInvoiceCredits);
                            }
                            await _customizeInvoiceItemRepository.UpdateAsync(customizeInvoiceItem);
                            await _unitOfWork.CommitAsync();
                        }
                        else
                        {
                            return (0, msg);
                        }
                    }
                    else { return (0, "已提交的开票单不能提交！"); }
                }
                else { return (0, "开票单不存在，请检查！"); }

                return (count, "");
            }
            catch (Exception ex)
            {
                return (0, ex.Message);
            }
        }

        private async Task<(int, string)> CheckedClassifyAsync(Guid? customizeInvoiceClassifyId)
        {
            var classify = await _db.CustomizeInvoiceClassify.FirstOrDefaultAsync(p => p.Id == customizeInvoiceClassifyId);
            if (classify != null)
            {
                return (0, $"操作失败，原因：该发票不能单独提交，请选在发票类【{classify.BillCode}】,按分类提交操作！");
            }
            return (1, "校验通过");
        }

        private async Task<(int, string)> SubmitToKingdeeAsync(List<CustomizeInvoiceDetailPo> customizeInvoiceDetails, CustomizeInvoiceItem customizeInvoiceItem, string createBy, bool? isPushDefaultEmail, string flow = "Y", string customerEmail = "", string customerInvoice = "")
        {
            var details = new List<OriginalBillEntryItem>();
            var originalDetails = new List<originalBillArEntryItem>();
            var customizeInvoiceCredits = new List<CustomizeInvoiceCredit>();
            var customizeInvoiceSubs = await _db.CustomizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).ToListAsync();

            foreach (var d in customizeInvoiceDetails)
            {
                var TaxRatedec = d.TaxRate / 100.00M;
                var detail = new OriginalBillEntryItem()
                {
                    gift = false,
                    taxrate = decimal.Parse((d.TaxRate / 100).ToString()),
                    num = d.Price < 0 ? d.Quantity * -1 : d.Quantity,
                    specification = d.ProductNo,
                    goodsname = d.ProductName,
                    taxamount = decimal.Parse((d.Price * d.Quantity).ToString("F4")),
                    remark = "",
                    unitprice = decimal.Parse(Math.Abs(d.Price / (1 + TaxRatedec)).ToString("F2")),
                    taxunitprice = Math.Abs(decimal.Parse(d.Price.ToString("F4"))),
                    taxratecodeid = d.TaxTypeNo,
                    policylogo = "0",
                    unit = d.PackUnit,
                    //jfzx_receivableno = d.CreditBillCode,
                    rowtype = d.Tag == "折扣行" ? "1" : "2"
                };
                detail.tax = Math.Abs(decimal.Parse(detail.taxunitprice.ToString("F2"))) - Math.Abs(detail.unitprice);
                details.Add(detail);
                var creditBillCodeArr = d.CreditBillCode.Split(',', StringSplitOptions.RemoveEmptyEntries).Distinct();
            }
            var creditCodes = customizeInvoiceDetails.Select(p => p.CreditBillCode).ToList();
            var creditCodeStr = string.Join(',', creditCodes);
            var creditCodeArry = creditCodeStr.Split(',', StringSplitOptions.RemoveEmptyEntries).Distinct();

            foreach (var creditCode in creditCodeArry)
            {
                if (customizeInvoiceSubs.Any())
                {
                    var amount = decimal.Parse(customizeInvoiceSubs.Where(p => p.CreditBillCode == creditCode).Sum(p => p.Quantity * p.Price).ToString("F2"));
                    originalDetails.Add(new originalBillArEntryItem
                    {
                        jfzx_arbillnum = creditCode,
                        jfzx_syamount = amount,
                    });
                    customizeInvoiceCredits.Add(new CustomizeInvoiceCredit
                    {
                        CreditCode = creditCode,
                        CreatedBy = customizeInvoiceItem.CreatedBy ?? "none",
                        CreatedTime = DateTimeOffset.Now,
                        CustomizeInvoiceItemCode = customizeInvoiceItem.Code,
                        CustomizeInvoiceItemId = customizeInvoiceItem.Id,
                        Id = Guid.NewGuid(),
                        Value = amount
                    });
                }
            }


            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { createBy }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                return (0, $"“{createBy}”制单人在用户中心不存在！");
            }
            // 是否推送默认开票人邮箱
            if (!isPushDefaultEmail.HasValue)
            {
                // 默认推送
                isPushDefaultEmail = true;
            }
            string buyeremail = string.Empty;
            if (isPushDefaultEmail.Value)
            {
                buyeremail = user == null ? customerEmail : string.Concat(user.Data.List.First().Email, ";", customerEmail);
            }
            else
            {
                buyeremail = customerEmail;
            }
            var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                id = customizeInvoiceItem.CustomerId
            });
            if (customer == null)
            {
                return (0, $"“{customizeInvoiceItem.CustomerName}”客户不存在！");
            }
            var invoice = customer.customerInvoices.FirstOrDefault(p => p.isInvoiceUnit == 1);
            if (!string.IsNullOrEmpty(customerInvoice))
            {
                invoice = JsonConvert.DeserializeObject<customerInvoice>(customerInvoice);
            }
            if (invoice == null)
            {
                return (0, $"“{customizeInvoiceItem.CustomerName}”客户不存在开票主体！");
            }

            var invoiceSaveInput = new PushCustomizeInvoiceSaveInput()
            {
                orgid = customizeInvoiceItem.NameCode,
                billdate = customizeInvoiceItem.BillDate.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                billsourcetype = customizeInvoiceItem.RedOffsetReason.HasValue ? "B" : "A",
                applicant = customizeInvoiceItem.RedOffsetOpter.ToString(), //申请方
                redreason = customizeInvoiceItem.RedOffsetReason.ToString(),//红冲原因
                infocode = string.IsNullOrEmpty(customizeInvoiceItem.RedOffsetCode) ? new List<string> { } : new List<string> { customizeInvoiceItem.RedOffsetCode },//红字信息表编号
                originalinvoicecode = customizeInvoiceItem.InvoiceCode,
                originalinvoiceno = !string.IsNullOrEmpty(customizeInvoiceItem.InvoiceNo) ? customizeInvoiceItem.InvoiceNo.TrimStart().TrimEnd() : string.Empty,
                //originalissuetime
                billno = customizeInvoiceItem.Code,
                billproperties = customizeInvoiceItem.InvoiceTotalAmount < 0 ? "-1" : "1",
                invoicetype = GetKingdeeInvoicetype(customizeInvoiceItem.InvoiceType.GetDescription()),
                taxationstyle = "0", //目前固定0，普通征税=0,差额征税=2,减按计征=1,差额征税-全额开票=01,差额征税-差额开票=02
                specialtype = "00",  //目前固定00，非特殊票种=00，收购=02，抵扣通行费=06，不抵扣通行费  07，成品油 08，卷烟  11，机动车 18，不动产租赁=E06，建筑服务=E03，不动产销售=E05，货物运输=E04，成品油=E01，卷烟=E18
                buyeremail = buyeremail,
                buyerphone = user == null ? "" : user.Data.List.First().MfaPhoneNumber,
                buyername = customizeInvoiceItem.CustomerName,
                buyertaxno = invoice == null ? "" : invoice.invoiceCode,
                buyerbank = invoice == null ? "" : invoice.invoiceBank + invoice.invoiceBankNo,
                buyeraddr = invoice == null ? "" : invoice.invoiceAddr + " " + invoice.invoiceTel,
                invoiceremark = customizeInvoiceItem.Remark,
                jfzx_auditsuggestion = customizeInvoiceItem.ApproveRemark,
                originalBillEntry = details,
                originalBillArEntry = originalDetails
            };
            if (customizeInvoiceItem.InvoiceType.GetDescription().Contains("普通") && customizeInvoiceItem.InvoiceType != InvoiceTypeEnum.DigitalCircuitTiket)//如果是普票的时候，申请方、红字信息表置灰，且为空
            {
                invoiceSaveInput.applicant = "";
                invoiceSaveInput.infocode = new List<string> { };
            }

            List<PushCustomizeInvoiceSaveInput> inputs = new List<PushCustomizeInvoiceSaveInput>
            {
                invoiceSaveInput
            };
            var king = await _kingdeeApiClient.PushCustomizeInvoice(inputs, flow);
            if (king != null && king.Code == CodeStatusEnum.Success)
            {
                return (1, $"操作成功");
            }
            else
            {
                return (0, king.Message);
            }
        }

        /// <summary>
        /// 提交开票单 (推送到金碟)
        /// </summary>
        public async Task<BaseResponseData<string>> SubmitCustomizeInvoiceToOA(SubmitCustomizeInvoiceInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功");
            var customizeInvoiceItem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(input.CustomizeInvoiceItemId);

            if (customizeInvoiceItem != null)
            {
                if (input.CreateBy != customizeInvoiceItem.CreatedBy)
                {
                    ret = BaseResponseData<string>.Failed(500, "操作失败，原因：只能提交本人数据。");
                    return ret;
                }
                var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == customizeInvoiceItem.CustomizeInvoiceClassifyId).AsNoTracking().FirstOrDefaultAsync();
                string customerEmail = customizeInvoiceClassify == null ? string.Empty : customizeInvoiceClassify.CustomerEmail;
                var customizeInvoiceDetails = await _db.CustomizeInvoiceDetail
                                                    //.Include(p => p.CustomizeInvoiceSubDetails)
                                                    .Where(c => c.CustomizeInvoiceItemId == input.CustomizeInvoiceItemId)
                                                    .OrderBy(p => p.CreatedTime)
                                                    .ThenByDescending(p => p.Price).AsNoTracking().ToListAsync();

                var details = new List<OriginalBillEntryItem>();
                var customizeInvoiceCredits = new List<CustomizeInvoiceCredit>();
                if (customizeInvoiceDetails.Count(cd => string.IsNullOrWhiteSpace(cd.TaxTypeNo)) > 0)
                {
                    ret = BaseResponseData<string>.Failed(500, $"开票明细中，税收分类编码不能为空！");
                    return ret;
                }
                if (string.IsNullOrEmpty(customizeInvoiceItem.ApproveRemark))
                {
                    if (customizeInvoiceDetails.Count(p => p.OriginProductName != p.ProductName) > 0)
                    {
                        ret = BaseResponseData<string>.Failed(500, $"存在开票品名与系统平台不一致的数据，请填写审批备注后提交！");
                        return ret;
                    }
                }
                (int count, string msg) = await SubmitToKingdeeAsync(customizeInvoiceDetails, customizeInvoiceItem, input.CreateBy, customizeInvoiceClassify.IsPushDefaultEmail, "N", customerEmail, customizeInvoiceClassify.Invoiceofclassfiy);
                if (count <= 0)
                {
                    ret = BaseResponseData<string>.Failed(500, msg);
                    return ret;
                }
                var company = await _bDSApiClient.GetCompanyMetaInfosAsync(new CompetenceCenter.BDSCenter.Inputs.CompanyMetaInfosInput
                {
                    nameCodeEq = customizeInvoiceItem.NameCode
                });
                var isTreasurerAudit = 0;//是否会计审核
                var isConsistentBySaleSystem = 0; //是否与销售子系统一致(1:一致，0:不一致)
                var saleCustomerProducts = new List<CustomerProductBillingOutput>();
                List<Guid>? productIds = customizeInvoiceDetails.Where(x => x.ProductId.HasValue).Select(x => x.ProductId.Value).ToList();
                if (productIds != null && productIds.Any())
                {
                    //saleCustomerProducts = await _sellApiClient.GetCustomerProductBillingAsync(new CustomerProductBillingInput
                    //{
                    //    CustomerId = !string.IsNullOrEmpty(customizeInvoiceItem.CustomerId) ? Guid.Parse(customizeInvoiceItem.CustomerId) : Guid.Empty,
                    //    CompanyId = customizeInvoiceItem.CompanyId,
                    //    ProductIds = productIds
                    //});
                    var selectProductInvoicesInputData = new List<SelectProductInvoicesInputData>();
                    foreach (var productId in productIds)
                    {
                        selectProductInvoicesInputData.Add(new SelectProductInvoicesInputData
                        {
                            ProductId = productId,
                            CustomerId = !string.IsNullOrEmpty(customizeInvoiceItem.CustomerId) ? Guid.Parse(customizeInvoiceItem.CustomerId) : Guid.Empty,
                        });
                    }
                    var selectProductInvoicesOutput = await _bDSApiClient.SelectProductInvoices(new SelectProductInvoicesInput
                    {
                        CompanyId = customizeInvoiceItem.CompanyId,
                        List = selectProductInvoicesInputData
                    });
                    foreach (var scProduct in selectProductInvoicesOutput)
                    {
                        var customizeInvoiceDetail = customizeInvoiceDetails.FirstOrDefault(x => x.ProductId == scProduct.ProductId);
                        //判断开票名称与销售子系统的是一致还是不一致
                        if (customizeInvoiceDetail != null && customizeInvoiceDetail.ProductName == scProduct.InvoiceName)
                        {
                            isConsistentBySaleSystem = 1;
                            break;
                        }
                    }
                }
                if (customizeInvoiceItem.InvoiceTotalAmount <= 0) //申开单是红票
                {
                    isTreasurerAudit = 1;
                }
                else if (customizeInvoiceDetails.Count(p => p.OriginProductName != p.ProductName) > 0) //开票名称制作开票和提交开票时改过
                {
                    isTreasurerAudit = 1;
                }
                else if (customizeInvoiceDetails.Count(p => p.ProductNo != p.Specification) > 0) //规格制作开票和提交开票时改过
                {
                    isTreasurerAudit = 1;
                }
                if (string.IsNullOrEmpty(company.First().taxRate))
                {
                    isTreasurerAudit = 1;
                }
                else
                {
                    var taxRate = decimal.Parse(company.First().taxRate);
                    if (customizeInvoiceDetails.Count(p => p.TaxRate == taxRate) != customizeInvoiceDetails.Count())//税率在销售订单改过（与公司税率不同）
                    {
                        isTreasurerAudit = 1;
                    }
                }
                var oaInput = new Gateway.Common.WeaverOA.WeaverInput
                {
                    BaseInfo = new Gateway.Common.WeaverOA.BaseInfo
                    {
                        Operator = input.CreateBy,
                        RequestName = $"【财务-运营开票】[{customizeInvoiceItem.Code}]-{customizeInvoiceItem.CompanyName}-{customizeInvoiceItem.CustomerName}",
                    },
                    MainData = new Gateway.Common.WeaverOA.MainData
                    {
                        FCreatorID = input.CreateBy,
                        Iframe_link = $"{_configuration["BaseUri"]}/fam/financeManagement/CustomizeInvoiceSubmitOA?id={customizeInvoiceItem.Id}", //PC的Iframe地址,
                        Height_m = 480,
                        Iframe_link_m = $"{_configuration["BaseUri"].Replace("/v1", "")}/oamobile/#/finance/invoicingEmbedPage?id={customizeInvoiceItem.Id}",
                        CpDepartment = "",
                        CPcompanyCode = customizeInvoiceItem.NameCode,
                        Condition = "{{\"amount\":500,\"isChange\":1}}",
                        Business_id = customizeInvoiceItem.Id.ToString(),
                        IsTreasurerAudit = isTreasurerAudit,
                        IsConsistentBySaleSystem = isConsistentBySaleSystem
                    },
                    OtherParams = new Gateway.Common.WeaverOA.OtherParams
                    {
                        IsNextFlow = 1,
                    }
                };
                if (string.IsNullOrEmpty(customizeInvoiceItem.OARequestId))
                {
                    var oaRet = await _weaverApiClient.CreateWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.CustomizeInvoiceForm);
                    if (!oaRet.Status)
                    {
                        await _kingdeeApiClient.ReturnCustomizeInvoice(new ReturnCustomizeInvoiceSaveInput
                        {
                            billno = customizeInvoiceItem.Code
                        });
                        ret = BaseResponseData<string>.Failed(500, oaRet.Msg);
                        return ret;

                    }
                    customizeInvoiceItem.OARequestId = oaRet.Data.Requestid.ToString();
                }
                else
                {
                    oaInput.BaseInfo.RequestId = int.Parse(customizeInvoiceItem.OARequestId);
                    var oaRet = await _weaverApiClient.SubmitWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.CustomizeInvoiceForm);
                    if (!oaRet.Status)
                    {
                        await _kingdeeApiClient.ReturnCustomizeInvoice(new ReturnCustomizeInvoiceSaveInput
                        {
                            billno = customizeInvoiceItem.Code
                        });
                        ret = BaseResponseData<string>.Failed(500, oaRet.Msg);
                        return ret;
                    }
                }
                customizeInvoiceItem.Status = (int)CustomizeInvoiceStatusEnum.Auditing;
                await _customizeInvoiceItemRepository.UpdateAsync(customizeInvoiceItem.Adapt<CustomizeInvoiceItem>());
                await _unitOfWork.CommitAsync();
            }
            return ret;
        }

        /// <summary>
        /// 得到发票类型
        /// </summary>
        /// <param name="invoicetypeName"></param>
        /// <returns></returns>
        private string GetKingdeeInvoicetype(string invoicetypeName)
        {
            var ret = string.Empty;
            switch (invoicetypeName)
            {
                case "电子普通发票":
                    ret = "026";
                    break;
                case "电子专用发票":
                    ret = "028";
                    break;
                case "纸质普通发票":
                    ret = "007";
                    break;
                case "纸质专用发票":
                    ret = "004";
                    break;
                case "增值税普通发票(卷票)":
                    ret = "025";
                    break;
                case "数电票(增值税专用发票)":
                    ret = "08xdp";
                    break;
                case "数电票(普通发票)":
                    ret = "10xdp";
                    break;
                default:
                    break;
            }
            return ret;

        }


        /// <summary>
        /// 撤回开票单
        /// </summary>
        public async Task<(int, string)> RecallCustomizeInvoice(RecallCustomizeInvoiceInput input)
        {
            try
            {
                var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == input.CustomizeInvoiceClassifyId).AsNoTracking().FirstOrDefaultAsync();
                if (customizeInvoiceClassify != null)
                {
                    if (customizeInvoiceClassify.CreatedBy != input.CreateBy)
                    {
                        return (0, "操作失败，原因：只能操作本人数据");
                    }
                    var ret = await _weaverApiClient.Withdraw(input.CreateBy, long.Parse(customizeInvoiceClassify.OARequestId), "");
                    if (ret.Status)
                    {
                        customizeInvoiceClassify.Status = CustomizeInvoiceStatusEnum.WaitSubmit;
                        _db.CustomizeInvoiceClassify.Update(customizeInvoiceClassify);
                        var customizes = await _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == input.CustomizeInvoiceClassifyId).AsNoTracking().ToListAsync();
                        foreach (var item in customizes)
                        {
                            item.Status = CustomizeInvoiceStatusEnum.WaitSubmit;
                            item.IsPush = false;
                            item.IsInvoiced = false;
                            _db.CustomizeInvoiceItem.Update(item);
                        }
                        await _unitOfWork.CommitAsync();
                        return (1, "操作成功！");
                    }
                    else { return (0, ret.Msg ?? "操作失败！"); }
                }
                else
                {
                    return (0, "操作失败，原因：未找到数据");
                }
            }
            catch (Exception ex)
            {
                return (0, ex.Message);
            }
        }

        /// <summary>
        /// 上传附件
        /// </summary>
        public async Task<BaseResponseData<int>> AttachFileIds(CustomizeInvoiceAttachFileInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            try
            {
                var invoiceItem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(input.CustomizeInvoiceItemId);
                invoiceItem.AttachFileIds = invoiceItem.AttachFileIds + "," + input.AttachFileIds;
                await _customizeInvoiceItemRepository.UpdateAsync(invoiceItem);
                await _unitOfWork.CommitAsync();
            }
            catch (Exception ex)
            {
                BaseResponseData<int>.Failed(500, "操作失败！" + ex.Message);
            }
            return ret;
        }
        /// <summary>
        /// 删除附件
        /// </summary>
        public async Task<BaseResponseData<string>> DeleteAttachFileIds(CustomizeInvoiceAttachFileInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var invoiceItem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(input.CustomizeInvoiceItemId);
            var newAttachFileIds = "";
            if (!string.IsNullOrEmpty(invoiceItem.AttachFileIds))
            {
                foreach (var fildId in invoiceItem.AttachFileIds.Split(","))
                {
                    if (!string.IsNullOrEmpty(fildId))
                    {
                        if (fildId.ToLower() != input.AttachFileId.ToLower())
                        {
                            newAttachFileIds += fildId + ",";
                        }
                    }
                }
            }
            newAttachFileIds = newAttachFileIds.TrimEnd(',');
            invoiceItem.AttachFileIds = newAttachFileIds;
            await _customizeInvoiceItemRepository.UpdateAsync(invoiceItem);
            await _unitOfWork.CommitAsync();
            ret.Data = newAttachFileIds;
            return ret;
        }
        /// <summary>
        /// 删除结算清单附件
        /// </summary>
        public async Task<BaseResponseData<string>> DeleteAttachFileIds_jsqd(CustomizeInvoiceAttachFileInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(c => c.Id == input.CustomizeInvoiceItemId).AsNoTracking().FirstOrDefaultAsync();

            var checkSettlementList = await _bDSApiClient.GetDataDictionaryListByType("CheckSettlementList");
            if (checkSettlementList != null && checkSettlementList.Any())
            {
                var companyIds = checkSettlementList.Where(p => !string.IsNullOrEmpty(p.Attribute)).Select(p => Guid.Parse(p.Attribute)).ToList();
                if (companyIds.Contains(customizeInvoiceClassify.CompanyId) && customizeInvoiceClassify.Status != CustomizeInvoiceStatusEnum.WaitSubmit)
                {
                    ret = BaseResponseData<string>.Failed(500, "操作失败，原因：公司配置了需要上传结算清单，审批中/已审核状态不能删除结算单。");
                    return ret;
                }
            }
            var newAttachFileIds = "";
            if (!string.IsNullOrEmpty(customizeInvoiceClassify.AttachFileIds))
            {
                foreach (var fildId in customizeInvoiceClassify.AttachFileIds.Split(","))
                {
                    if (!string.IsNullOrEmpty(fildId))
                    {
                        if (fildId.ToLower() != input.AttachFileId.ToLower())
                        {
                            newAttachFileIds += fildId + ",";
                        }
                    }
                }
            }
            newAttachFileIds = newAttachFileIds.TrimEnd(',');
            customizeInvoiceClassify.AttachFileIds = newAttachFileIds;
            _db.CustomizeInvoiceClassify.Update(customizeInvoiceClassify);
            await _unitOfWork.CommitAsync();
            ret.Data = newAttachFileIds;
            return ret;
        }

        /// <summary>
        /// 上传结算清单附件
        /// </summary>
        public async Task<BaseResponseData<int>> AttachFileIds_jsqd(CustomizeInvoiceAttachFileInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input.CustomizeInvoiceItemIds == null || !input.CustomizeInvoiceItemIds.Any())
            {
                return BaseResponseData<int>.Failed(500, "操作失败！请勾选需要上传的单据");
            }
            try
            {
                var customizeInvoiceClassifys = await _db.CustomizeInvoiceClassify.Where(c => input.CustomizeInvoiceItemIds.Contains(c.Id)).AsNoTracking().ToListAsync();
                var groupedByCompanyAndCustomer = customizeInvoiceClassifys
                .GroupBy(c => new { c.CompanyId, c.CustomerId })
                .ToDictionary(g => g.Key, g => g.ToList());
                if (groupedByCompanyAndCustomer.Count > 1)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败！请勾选相同公司和客户的单据进行操作");
                }
                if (customizeInvoiceClassifys != null && customizeInvoiceClassifys.Any())
                {
                    var list = new List<CustomizeInvoiceClassifyPo>();
                    foreach (var customizeInvoiceClassify in customizeInvoiceClassifys)
                    {
                        customizeInvoiceClassify.AttachFileIds = customizeInvoiceClassify.AttachFileIds + "," + input.AttachFileIds;
                        list.Add(customizeInvoiceClassify);
                    }
                    _db.CustomizeInvoiceClassify.UpdateRange(list);
                    await _unitOfWork.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败！" + ex.Message);
            }
            return ret;
        }
        /// <summary>
        /// 保存开票单明细
        /// </summary>
        public async Task<(int, string)> SaveCustomizeInvoiceDetail(SaveCustomizeInvoiceDetailInput input)
        {
            try
            {
                int count = 1;
                if (input != null && input.DetailList.Any())
                {
                    var customizeInvoiceItemId = input.ItemList[0].Id;
                    var ret = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
                    if (ret.Code != CodeStatusEnum.Success)
                    {
                        return (-1, ret.Message);
                    }
                    var customizeInvoiceItem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(customizeInvoiceItemId);
                    if (customizeInvoiceItem != null)
                    {
                        customizeInvoiceItem.UpdateBy(input.CreateBy);
                        await _customizeInvoiceItemRepository.UpdateAsync(customizeInvoiceItem);

                        var oldCustomizeInvoiceDetails = await _customizeInvoiceDetailRepository.GetByItemIdAsync(customizeInvoiceItemId);
                        var oldCustomizeInvoiceDetailIds = oldCustomizeInvoiceDetails.Select(c => c.Id).ToList();

                        //更新开票单明细
                        var newCustomizeInvoiceDetails = (input.DetailList.Adapt<List<CustomizeInvoiceDetail>>()).OrderBy(p => p.Sort).ToList();
                        if (newCustomizeInvoiceDetails.Where(p => p.Sort <= 0).Count() > 0)
                        {
                            return (-1, "开票单明细排序号存在非正整数，请检查！");
                        }
                        if (oldCustomizeInvoiceDetails != null && oldCustomizeInvoiceDetails.Any())
                        {
                            int i = 1;
                            foreach (var newDetail in newCustomizeInvoiceDetails)
                            {
                                newDetail.Sort = i;
                                //新的数据不在旧的里面就添加，在就更新
                                if (oldCustomizeInvoiceDetails.Where(c => c.Id == newDetail.Id).Count() > 0)
                                {
                                    await _customizeInvoiceDetailRepository.UpdateAsync(newDetail);
                                }
                                else
                                {
                                    await _customizeInvoiceDetailRepository.AddAsync(newDetail);
                                }
                                i++;
                            }
                            foreach (var oldDetail in oldCustomizeInvoiceDetails)
                            {
                                //旧的数据不在新的里面就删除
                                if (newCustomizeInvoiceDetails.Where(c => c.Id == oldDetail.Id).Count() == 0)
                                {
                                    await _customizeInvoiceDetailRepository.DeleteByIdAsync(oldDetail.Id);
                                }
                            }
                        }
                        else
                        {
                            int i = 1;
                            foreach (var newDetail in newCustomizeInvoiceDetails)
                            {
                                newDetail.Sort = i;
                                i++;
                            }
                            await _customizeInvoiceDetailRepository.AddManyAsync(newCustomizeInvoiceDetails);
                        }
                        await _unitOfWork.CommitAsync();
                    }
                }
                else { return (0, "开票单明细不存在，请检查！"); }

                return (count, "");
            }
            catch (Exception ex)
            {
                return (-1, ex.Message);
            }
        }
        /// <summary>
        /// 操作开票明细校验
        /// </summary>
        /// <returns></returns>
        private async Task<BaseResponseData<string>> CheckCustomizeInvoiceDetail(Guid customizeInvoiceItemId)
        {
            var item = await _db.CustomizeInvoiceItem.Where(p => p.Id == customizeInvoiceItemId).AsNoTracking().FirstOrDefaultAsync();
            if (item == null)
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，原因：没有找到{customizeInvoiceItemId}的开票申请单");
            }
            if (item.Status != CustomizeInvoiceStatusEnum.WaitSubmit)
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，原因：只有开票申请单的状态未提交的才能操作");
            }
            if (!string.IsNullOrEmpty(item.RelationCode))
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，原因：只有蓝票申请单才能操作");
            }
            return BaseResponseData<string>.Success("操作成功");

        }

        /// <summary>
        /// 设置为另一个开票单
        /// </summary>
        public async Task<(int, string)> SetAsAnotherInvoice(SetAsAnotherInvoiceInput input)
        {
            try
            {
                var creditNoLst = input.DetailList.Select(p => p.CreditBillCode).Distinct().ToList();
                var creditNoLstTemp = input.DetailList.Select(p => p.CreditBillCode).Distinct().ToList();

                var ret2 = await CheckCustomizeInvoiceDetail(input.ItemList.FirstOrDefault().Id);
                if (ret2.Code != CodeStatusEnum.Success)
                {
                    return (0, ret2.Message);
                }
                var creditCodeStr = string.Join(',', creditNoLst);
                var creditCodeArry = creditCodeStr.Split(',', StringSplitOptions.RemoveEmptyEntries).Distinct().ToList();

                if (input.SelDetailList.Count() == input.DetailList.Count())
                {
                    return (0, "操作失败，原因：不能将全部明细设置到另一个申开单中！");
                }
                var credit = await _db.Credits.FirstOrDefaultAsync(x => x.BillCode == creditCodeArry[0]);
                if (credit == null)
                {
                    return (0, "操作失败，原因：未找到对应应收单！");
                }
                if (credit != null && credit.SaleSource == SaleSourceEnum.SunPurchase)
                {
                    return (0, "操作失败，原因：阳采同一个订单，不能设置多个开票单！");
                }
                int count = 1;
                if (input != null && input.ItemList.Any() && input.DetailList.Any() && input.SelDetailList.Any())
                {
                    var customizeInvoiceDetailIds = input.SelDetailList.Select(p => p.Id).ToList();
                    var customizeInvoiceDetailSubs = await _db.CustomizeInvoiceDetail.Where(p => customizeInvoiceDetailIds.Contains(p.Id) && p.ParentId.HasValue).AsNoTracking().ToListAsync();
                    var customizeInvoiceDetailParents = await _db.CustomizeInvoiceDetail.Where(p => customizeInvoiceDetailIds.Contains(p.ParentId.Value)).AsNoTracking().ToListAsync();

                    if ((customizeInvoiceDetailParents != null && customizeInvoiceDetailParents.Count(p => p.ParentId.HasValue) > 0) || (customizeInvoiceDetailSubs != null && customizeInvoiceDetailSubs.Count() > 0))
                    {
                        return (0, "操作失败，原因：开票明细中有拆分折扣行的商品行/折扣行不能设置为另一个开票单，请先合并商品行和折扣行再进行操作！");
                    }
                    //设置为另一个开票单
                    var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        ids = new List<string> { input.ItemList.FirstOrDefault().CompanyId.ToString() }
                    })).FirstOrDefault();
                    var code = input.SelDetailList.First().RelateCode;
                    var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = code.Split('-')[0],
                        BillType = "CI",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1,
                        CompanyCode = companyInfo.nameCode
                    });
                    if (outPut.Status)
                    {
                        var olditem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(input.ItemList.FirstOrDefault().Id);
                        var newitem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(input.ItemList.FirstOrDefault().Id);
                        var newitemId = Guid.NewGuid();
                        if (newitem != null)
                        {
                            newitem.Id = newitemId;
                            if (input.CustomizeInvoiceClassifyId.HasValue)
                            {
                                newitem.CustomizeInvoiceClassifyId = input.CustomizeInvoiceClassifyId;
                            }
                            newitem.Code = outPut.Codes.First();
                            newitem.InvoiceTotalAmount = input.SelDetailList.Sum(d => d.Value);
                            newitem.Status = 0;
                            newitem.OARequestId = string.Empty;
                            newitem.CreateBy(input.CreateBy);
                            await _customizeInvoiceItemRepository.AddAsync(newitem);
                            var oldDetailIds = input.SelDetailList.Select(p => p.Id).ToList();
                            foreach (var detail in input.SelDetailList)
                            {
                                detail.CustomizeInvoiceItemId = newitem.Id;
                            }
                            await _customizeInvoiceDetailRepository.UpdateManyAsync(input.SelDetailList);
                            var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => oldDetailIds.Contains(p.CustomizeInvoiceDetailId)).ToListAsync();
                            if (customizeInvoiceCredits.Any())
                            {
                                foreach (var item in customizeInvoiceCredits)
                                {
                                    item.CustomizeInvoiceItemCode = newitem.Code;
                                    item.CustomizeInvoiceItemId = newitem.Id;
                                }
                            }

                            if (!input.IsBatchSetAsAnotherInvoice)
                            {
                                //更新原开票发票总金额  
                                var customizeInvoiceDetails = _db.CustomizeInvoiceDetail.Where(c => c.CustomizeInvoiceItemId == olditem.Id).AsNoTracking().ToList();
                                olditem.InvoiceTotalAmount -= newitem.InvoiceTotalAmount;
                                olditem.Status = 0;
                                await _customizeInvoiceItemRepository.UpdateAsync(olditem);
                                await _unitOfWork.CommitAsync();
                            }

                        }
                    }
                    else
                    {
                        throw new Exception(outPut.Msg);
                    }
                }
                else { return (0, "开票单明细不存在，请检查！"); }

                return (count, "");
            }
            catch (Exception ex)
            {
                return (0, ex.Message);
            }
        }

        /// <summary>
        /// 批量设置为另一个开票单
        /// </summary>
        public async Task<(int, string)> BatchSetAsAnotherInvoice(SetAsAnotherInvoiceInput input)
        {
            try
            {
                if (input.DetailList == null || !input.DetailList.Any())
                {
                    return (0, "操作失败，原因：没有明细数据！");
                }
                else if (input.DetailList.Exists(p => string.IsNullOrEmpty(p.CustomizeInvoiceIndex)))
                {
                    return (0, "操作失败，原因：明细中存在没有填写开票序号的数据！");
                }
                else
                {
                    var ret2 = await CheckCustomizeInvoiceDetail((input.ItemList.FirstOrDefault().Id));
                    if (ret2.Code != CodeStatusEnum.Success)
                    {
                        return (0, ret2.Message);
                    }

                    var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        ids = new List<string> { input.ItemList.FirstOrDefault().CompanyId.ToString() }
                    })).FirstOrDefault();
                    //先生成开票归类
                    if (companyInfo != null)
                    {
                        var customizeInvoiceIndexs = input.DetailList.Select(p => p.CustomizeInvoiceIndex).Distinct().ToList();
                        var hasCustomizeInvoiceIndexs = new List<string>();
                        for (int i = 0; i < customizeInvoiceIndexs.Count - 1; i++)
                        {
                            var index = customizeInvoiceIndexs[i];
                            var tempInput = new SetAsAnotherInvoiceInput
                            {
                                CreateBy = input.CreateBy,
                                DetailList = input.DetailList.Where(p => !hasCustomizeInvoiceIndexs.Contains(p.CustomizeInvoiceIndex)).ToList(),
                                ItemList = input.ItemList,
                                SelDetailList = input.DetailList.Where(p => p.CustomizeInvoiceIndex == index).ToList(),
                                IsBatchSetAsAnotherInvoice = true,
                            };

                            (var ret, var message) = await SetAsAnotherInvoice(tempInput);
                            if (ret <= 0)
                            {
                                return (ret, message);
                            }
                            hasCustomizeInvoiceIndexs.Add(index);
                        }
                        var olditem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(input.ItemList.FirstOrDefault().Id);
                        olditem.InvoiceTotalAmount = input.DetailList.Where(p => !hasCustomizeInvoiceIndexs.Contains(p.CustomizeInvoiceIndex)).Sum(p => p.Value);
                        await _customizeInvoiceItemRepository.UpdateAsync(olditem);

                        var tmp = await _db.SaveChangesAsync();
                        return (tmp, tmp > 0 ? "操作成功" : "操作失败");
                    }

                    return (0, "操作失败");
                }
            }
            catch (Exception ex)
            {
                return (0, ex.Message);
            }
        }

        /// <summary>
        /// 审核通过
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> Approve(KingdeeCustomizeInvoiceInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            var customize = await _db.CustomizeInvoiceItem.Where(c => c.Code == input.billNo).AsNoTracking().FirstOrDefaultAsync();

            if (customize == null)
            {
                ret = BaseResponseData<int>.Failed(500, $"操作失败：原因{input.billNo}，单号不存在！");
            }
            else
            {
                if (!input.isPass)
                {
                    ret = await ReturnCustomizeInvoice(new KindeeCustomizeInvoiceInput { Codes = new List<string> { input.billNo } });
                }
                else
                {
                    var classifyPo = await _db.CustomizeInvoiceClassify.Where(p => p.Id == customize.CustomizeInvoiceClassifyId).FirstOrDefaultAsync();
                    customize.IsInvoiced = input.isPass;
                    customize.IsPush = input.isPass;
                    customize.UpdatedTime = DateTime.Now;
                    customize.Status = CustomizeInvoiceStatusEnum.Completed;
                    if (classifyPo != null)
                    {
                        classifyPo.Status = CustomizeInvoiceStatusEnum.WaitInvoice;
                    }
                    await _customizeInvoiceItemRepository.UpdateAsync(customize.Adapt<CustomizeInvoiceItem>());
                    await _unitOfWork.CommitAsync();
                }
            }
            return ret;
        }

        /// <summary>
        /// 新增折扣行
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> Adddiscount(CustomizeInvoiceDetailInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功");
            var detail = await _customizeInvoiceDetailRepository.GetAsync(input.CustomizeInvoiceDetailId);
            if (detail != null)
            {
                if (detail.Tag == "折扣行")
                {
                    return BaseResponseData<string>.Failed(500, "操作失败，原因：折扣行行不能拆分折扣行");
                }
                if (detail.Value < 0 && detail.Tag != "折扣行")
                {
                    return BaseResponseData<string>.Failed(500, "操作失败，原因：负数的商品行不能拆分折扣行");
                }
                if (detail.OriginalPrice < detail.Price)
                {
                    return BaseResponseData<string>.Failed(500, "操作失败，原因：原价小于单价不能拆分折扣行");
                }

                var ret2 = await CheckCustomizeInvoiceDetail(detail.CustomizeInvoiceItemId);
                if (ret2.Code != CodeStatusEnum.Success)
                {
                    return ret2;
                }
                var originValue = detail.Value;
                var originId = detail.Id;
                detail.OriginalPrice = detail.OriginalPrice.HasValue ? detail.OriginalPrice.Value : 0;
                var originalPrice = detail.OriginalPrice.Value;
                var needDetailAdd = new List<CustomizeInvoiceDetail>();
                await _customizeInvoiceDetailRepository.DeleteByIdAsync(detail.Id);
                detail.Price = detail.OriginalPrice.Value - detail.Price;
                detail.Quantity = -Math.Abs(detail.Quantity);
                detail.Value = detail.Price * detail.Quantity;
                detail.Tag = "折扣行";
                detail.OriginalPrice = detail.Price;
                detail.TaxAmount = detail.Value - (detail.Value / (1 + (detail.TaxRate / 100)));
                detail.Id = Guid.NewGuid();

                var detail2 = detail.Adapt<CustomizeInvoiceDetail>();
                detail2.Price = originalPrice;
                detail2.Quantity = Math.Abs(detail.Quantity);
                detail2.Value = detail2.Price * Math.Abs(detail2.Quantity);
                detail2.OriginalPrice = detail2.Price;
                detail2.TaxAmount = detail2.Value - (detail2.Value / (1 + (detail2.TaxRate / 100)));
                detail2.Id = originId;
                detail.ParentId = detail2.Id;
                detail2.Tag = string.Empty;
                needDetailAdd.Add(detail2);
                needDetailAdd.Add(detail);

                detail.Value = (detail2.Value - originValue) * -1; //平尾差
                await _customizeInvoiceDetailRepository.AddManyAsync(needDetailAdd);
                await _unitOfWork.CommitAsync();

            }
            return ret;
        }

        /// <summary>
        /// 修改状态（OA驳回）
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statusEnum"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<string>> UpdateStatus(Guid id, CustomizeInvoiceStatusEnum statusEnum)
        {

            var ret = BaseResponseData<string>.Success("操作成功！");
            var customize = await _db.CustomizeInvoiceItem.Where(p => p.Id == id).AsNoTracking().FirstOrDefaultAsync();
            if (customize != null)
            {
                if (statusEnum == CustomizeInvoiceStatusEnum.WaitSubmit)
                {
                    if (customize.Status != CustomizeInvoiceStatusEnum.Auditing)
                    {
                        return BaseResponseData<string>.Failed(500, "操作失败，原因：状态异常，只有审批中的才能驳回");
                    }
                    customize.IsPush = false;
                }
                customize.Status = statusEnum;
                await _customizeInvoiceItemRepository.UpdateAsync(customize.Adapt<CustomizeInvoiceItem>());

            }
            else
            {
                var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == id).AsNoTracking().FirstOrDefaultAsync();
                customizeInvoiceClassify.Status = statusEnum;
                var customizeInvoices = await _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == id).AsNoTracking().ToListAsync();
                foreach (var item in customizeInvoices)
                {
                    if (statusEnum == CustomizeInvoiceStatusEnum.WaitSubmit && item.Status != CustomizeInvoiceStatusEnum.Auditing)
                    {
                        return BaseResponseData<string>.Failed(500, "操作失败，原因：状态异常，只有审批中的才能驳回");
                    }
                    item.IsPush = false;
                    item.Status = statusEnum;
                }
                _db.CustomizeInvoiceClassify.Update(customizeInvoiceClassify);
                _db.CustomizeInvoiceItem.UpdateRange(customizeInvoices);
            }
            await _unitOfWork.CommitAsync();
            return ret;
        }

        /// <summary>
        /// 开红冲发票
        /// </summary>
        /// <param name="input"></param> 
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<string>> CreateOffsetInvoice(CreateOffsetInvoiceInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var oldCustomizeInvoiceItem = await _db.CustomizeInvoiceItem
                .Where(p => p.Id == input.Id)
                .AsNoTracking().FirstOrDefaultAsync();
            var oldInvoiceClassifyPo = await _db.CustomizeInvoiceClassify.FirstOrDefaultAsync(x => x.Id == oldCustomizeInvoiceItem.CustomizeInvoiceClassifyId);

            #region 校验
            if (oldCustomizeInvoiceItem == null)
                return BaseResponseData<string>.Failed(500, "操作失败，原因：未找到开票申请单号，请检查！");
            if (!string.IsNullOrEmpty(oldCustomizeInvoiceItem.RelationCode) || oldCustomizeInvoiceItem.InvoiceTotalAmount < 0)
                return BaseResponseData<string>.Failed(500, "操作失败，原因：红票不能被发起红冲，请检查！");
            if (oldCustomizeInvoiceItem.Status != CustomizeInvoiceStatusEnum.Completed)
                return BaseResponseData<string>.Failed(500, "操作失败，原因：只有状态为已开票的数据才能创建红冲发票，请检查！");
            if (oldCustomizeInvoiceItem.RelationType == CustomizeInvoiceRelationTypeEunm.RedOffset)
                return BaseResponseData<string>.Failed(500, "操作失败，原因：红票不能被发起红冲，请检查！");
            if (oldCustomizeInvoiceItem.ChangedStatus == CustomizeInvoiceChangedStatusEnum.RedOffset)
                return BaseResponseData<string>.Failed(500, "操作失败，原因：该发票已经红冲，不允许再次红冲，请检查！");
            if (oldCustomizeInvoiceItem.ChangedStatus == CustomizeInvoiceChangedStatusEnum.PartRedOffset && input.redWay == 0)
                return BaseResponseData<string>.Failed(500, "操作失败，原因：该发票存已经部分红冲，不允许整单红冲，请检查！");
            if (!input.RedOffsetReason.HasValue)
                return BaseResponseData<string>.Failed(500, "操作失败，原因：冲红原因必填，请检查！");
            var digitalCircuitType = new List<InvoiceTypeEnum> {
                        InvoiceTypeEnum.DigitalCircuitUniversal,
                        InvoiceTypeEnum.DigitalCircuitTiket
                };
            if (input.IsNoRedConfirm != 1)
            {
                var noRequiredType = new List<InvoiceTypeEnum> {
                    InvoiceTypeEnum.DZUniversal,
                    InvoiceTypeEnum.RollTicket,
                    InvoiceTypeEnum.ZZUniversal,
                };
                if (!noRequiredType.Contains(oldCustomizeInvoiceItem.InvoiceType))
                {
                    if (input.RedOffsetReason == 4 && string.IsNullOrEmpty(input.RedOffsetCode))
                        return BaseResponseData<string>.Failed(500, "操作失败，原因：销售折让红字信息表编号必填");
                    if (input.RedOffsetOpter == 3 && string.IsNullOrEmpty(input.RedOffsetCode))
                        return BaseResponseData<string>.Failed(500, "操作失败，原因：购方申请-已抵扣红字信息表编号必填");
                }

                if (string.IsNullOrEmpty(input.RedOffsetCode) && digitalCircuitType.Contains(oldCustomizeInvoiceItem.InvoiceType))
                    return BaseResponseData<string>.Failed(500, "操作失败，原因：数电票红字信息表编号必填");
            }
            else
            {
                if (!digitalCircuitType.Contains(oldCustomizeInvoiceItem.InvoiceType))
                    return BaseResponseData<string>.Failed(500, "操作失败：红冲发票（暂无红字确认单号）只支持数电票");
            }
            if (oldInvoiceClassifyPo != null)
            {
                if (oldInvoiceClassifyPo.Classify == CustomizeInvoiceClassifyEnum.Pre)
                {
                    return BaseResponseData<string>.Failed(500, "操作失败，原因：预开票单不支持红冲，请在金蝶平台操作！");
                }
            }
            var relateCustomizeInvoiceItem = await _db.CustomizeInvoiceItem.Where(p => p.RelationCode == oldCustomizeInvoiceItem.Code &&
                                                                                        p.Status != CustomizeInvoiceStatusEnum.Completed &&
                                                                                        p.Status != CustomizeInvoiceStatusEnum.Cancel
            ).FirstOrDefaultAsync();
            if (relateCustomizeInvoiceItem != null)
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，原因：红冲开票单号{relateCustomizeInvoiceItem.Code}开票未完成，请开票完成后操作！");
            }
            #endregion
            var redoffsetInvoiceItem = oldCustomizeInvoiceItem.DeepClone();
            //设置为另一个开票单
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { oldCustomizeInvoiceItem.CompanyId.ToString() }
            })).FirstOrDefault();
            var outPutClassify = await _codeGenClient.ApplyCode(new ApplyCodeInput
            {
                BusinessArea = oldCustomizeInvoiceItem.Code.Split('-')[0],
                BillType = "CIC",
                SysMonth = companyInfo.sysMonth,
                DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                Num = 1,
                CompanyCode = companyInfo.nameCode
            });
            var classifId = Guid.NewGuid();
            var SaleSystemName = await _db.CustomizeInvoiceClassify.Where(p => p.Id == oldCustomizeInvoiceItem.CustomizeInvoiceClassifyId).Select(x => x.SaleSystemName).FirstOrDefaultAsync();

            CustomizeInvoiceClassifyPo invoiceClassifyPo = new CustomizeInvoiceClassifyPo
            {
                Id = classifId,
                BillCode = outPutClassify.Codes.First(),
                CompanyId = oldCustomizeInvoiceItem.CompanyId.Value,
                CompanyName = companyInfo.companyName,
                CreatedBy = input.CreatedBy,
                CreatedTime = DateTimeOffset.Now,
                SaleSystemName = SaleSystemName,
                CustomerId = Guid.Parse(oldCustomizeInvoiceItem.CustomerId),
                CustomerName = oldCustomizeInvoiceItem.CustomerName,
                Classify = oldInvoiceClassifyPo != null ? oldInvoiceClassifyPo.Classify : CustomizeInvoiceClassifyEnum.Credit,
                Status = CustomizeInvoiceStatusEnum.WaitSubmit,
                // 复制客户邮箱地址
                CustomerEmail = oldInvoiceClassifyPo != null ? oldInvoiceClassifyPo.CustomerEmail : string.Empty,
                CreditSaleSubType = oldInvoiceClassifyPo != null ? oldInvoiceClassifyPo?.CreditSaleSubType : CreditSaleSubTypeEnum.platform//赋值销售应收子类型
            };
            _db.CustomizeInvoiceClassify.Add(invoiceClassifyPo);
            var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
            {
                BusinessArea = oldCustomizeInvoiceItem.Code.Split('-')[0],
                BillType = "CI",
                SysMonth = companyInfo.sysMonth,
                DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                Num = 1,
                CompanyCode = companyInfo.nameCode
            });
            string? blueInvoiceNo = string.Empty;
            if (string.IsNullOrEmpty(input.InvoiceNo))
            {
                // 未填写追踪原始红冲发票号
                var invoice = await _db.Invoices.FirstAsync(x => x.CustomizeInvoiceCode == oldCustomizeInvoiceItem.Code);
                blueInvoiceNo = invoice != null ? invoice.InvoiceNo : string.Empty;
            }
            else
            {
                blueInvoiceNo = input.InvoiceNo;
            }
            string? invoiceCode = string.Empty;
            if (string.IsNullOrEmpty(input.InvoiceCode))
            {
                // 未填写追踪原始红冲发票号
                var invoice = await _db.Invoices.FirstAsync(x => x.CustomizeInvoiceCode == oldCustomizeInvoiceItem.Code);
                invoiceCode = invoice != null ? invoice.InvoiceCode : string.Empty;
            }
            else
            {
                invoiceCode = input.InvoiceCode;
            }
            var outputInvoices = await _db.OutputInvoices.Where(p => p.CustomizeInvoiceCode == oldCustomizeInvoiceItem.Code).OrderBy(p => p.RowNo).ToListAsync();
            redoffsetInvoiceItem.CustomizeInvoiceClassifyId = classifId;
            redoffsetInvoiceItem.Id = Guid.NewGuid();
            redoffsetInvoiceItem.RelationCode = oldCustomizeInvoiceItem.Code;
            redoffsetInvoiceItem.Status = CustomizeInvoiceStatusEnum.WaitSubmit;
            redoffsetInvoiceItem.OARequestId = string.Empty;
            redoffsetInvoiceItem.IsInvoiced = false;
            redoffsetInvoiceItem.IsPush = false;
            redoffsetInvoiceItem.Code = outPut.Codes.First();
            redoffsetInvoiceItem.AttachFileIds = string.Empty;
            redoffsetInvoiceItem.BillDate = DateTime.Now;
            redoffsetInvoiceItem.CreatedTime = DateTime.Now;
            redoffsetInvoiceItem.RedOffsetCode = input.RedOffsetCode;
            redoffsetInvoiceItem.InvoiceCode = invoiceCode;
            redoffsetInvoiceItem.InvoiceNo = blueInvoiceNo;
            redoffsetInvoiceItem.BlueRedInvoiceAmount = input.BlueRedInvoiceAmount;
            redoffsetInvoiceItem.RedOffsetOpter = input.RedOffsetOpter;
            redoffsetInvoiceItem.RedOffsetReason = input.RedOffsetReason;
            redoffsetInvoiceItem.IsNoRedConfirm = input.IsNoRedConfirm;
            var customizeInvoiceDetails = await _db.CustomizeInvoiceDetail
                             .Where(p => p.CustomizeInvoiceItemId == oldCustomizeInvoiceItem.Id)
                             .OrderBy(z => z.Sort)
                             .ThenByDescending(z => z.CreatedTime)
                             .ThenByDescending(p => p.Price * p.Quantity)
                             .AsNoTracking().ToListAsync();
            var oldCustomizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemId == oldCustomizeInvoiceItem.Id).AsNoTracking().ToListAsync();
            var orginCustomizeInvoiceCredits = oldCustomizeInvoiceCredits.DeepClone();
            var childcustomizeInvoiceDetails = customizeInvoiceDetails.Where(p => p.ParentId.HasValue).OrderBy(p => p.Sort).ToList();
            if (input.redWay == 0) //整单红冲
            {
                if (input.IsNoRedConfirm == 1)
                {
                    if (!oldCustomizeInvoiceCredits.Any() && oldInvoiceClassifyPo.Classify == CustomizeInvoiceClassifyEnum.Credit)
                    {
                        return BaseResponseData<string>.Failed(500, $"操作失败，原因：没有找到开票明细与应收明细关系,请走红冲发票-有红字确认单流程！");
                    }
                    var childcustomizeInvoiceDetailsTemp = customizeInvoiceDetails.Where(p => p.Tag == "折扣行" && !p.ParentId.HasValue).OrderBy(p => p.Sort).ToList();
                    if (childcustomizeInvoiceDetailsTemp.Any())
                    {
                        return BaseResponseData<string>.Failed(500, $"操作失败，原因：折扣行和商品行没有关联关系，需要走红冲发票-有红字确认单流程！");
                    }
                }
                redoffsetInvoiceItem.RelationType = CustomizeInvoiceRelationTypeEunm.RedOffset;
                oldCustomizeInvoiceItem.ChangedStatus = CustomizeInvoiceChangedStatusEnum.RedOffset;
                redoffsetInvoiceItem.InvoiceTotalAmount = -oldCustomizeInvoiceItem.InvoiceTotalAmount;
                var addCustomizeInvoiceDetails = new List<CustomizeInvoiceDetailPo>();
                if (customizeInvoiceDetails != null && customizeInvoiceDetails.Any())
                {
                    if (childcustomizeInvoiceDetails.Any())
                    {
                        foreach (var childItem in childcustomizeInvoiceDetails)
                        {
                            var parentDetail = customizeInvoiceDetails.FirstOrDefault(p => p.Id == childItem.ParentId);
                            if (parentDetail != null)
                            {
                                //customizeInvoiceDetails.Remove(childItem);
                                var taxRate = parentDetail.TaxRate / 100;
                                parentDetail.Price -= childItem.Price;
                                parentDetail.Value = parentDetail.Quantity * parentDetail.Price;
                                parentDetail.TaxAmount = (parentDetail.Value / (1 + taxRate) * taxRate);
                            }
                        }
                    }
                    var parentCustomizeInvoiceDetails = customizeInvoiceDetails.Where(p => !p.ParentId.HasValue)
                                                                               .OrderBy(z => z.Sort)
                                                                               .ThenByDescending(z => z.CreatedTime)
                                                                               .ThenByDescending(p => p.Price * p.Quantity).ToList();
                    var index = 0;
                    foreach (var detail in customizeInvoiceDetails)
                    {
                        var parentCustomizeInvoiceDetail = parentCustomizeInvoiceDetails.FirstOrDefault(p => p.Id == detail.Id);
                        if (parentCustomizeInvoiceDetail != null)
                        {
                            var originQuantity = detail.Quantity;
                            var oldCustomizeInvoiceCreditsTemp = oldCustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == detail.Id).ToList();
                            detail.CustomizeInvoiceItemId = redoffsetInvoiceItem.Id;
                            detail.RelateId = detail.Id;
                            detail.Id = Guid.NewGuid();
                            detail.Quantity = -detail.Quantity;
                            detail.Value = -detail.Value;
                            detail.TaxAmount = -detail.TaxAmount;
                            if (outputInvoices.Count() - 1 >= index)
                            {
                                detail.Sort = outputInvoices[index].RowNo + 1;
                            }
                            else
                            {
                                detail.Sort = index;
                            }
                            if (oldCustomizeInvoiceCredits.Any())
                            {
                                foreach (var item in oldCustomizeInvoiceCreditsTemp)
                                {
                                    item.Id = Guid.NewGuid();
                                    item.CustomizeInvoiceItemId = redoffsetInvoiceItem.Id;
                                    item.CustomizeInvoiceItemCode = redoffsetInvoiceItem.Code;
                                    item.CustomizeInvoiceDetailId = detail.Id;
                                    item.Quantity = -item.Quantity;
                                    item.CustomizeInvoiceDetailAmount = item.CustomizeInvoiceDetailAmount * -1;
                                }
                            }
                            addCustomizeInvoiceDetails.Add(detail);
                        }
                        var childcustomizeInvoiceDetail = childcustomizeInvoiceDetails.FirstOrDefault(p => p.ParentId == detail.RelateId);
                        if (parentCustomizeInvoiceDetail != null && childcustomizeInvoiceDetail != null)
                        {
                            index++;
                        }
                        else if (detail.Value != 0)
                        {
                            index++;
                        }
                    }
                    if (oldCustomizeInvoiceCredits.Any())
                    {
                        await _db.CustomizeInvoiceCredits.AddRangeAsync(oldCustomizeInvoiceCredits);
                    }
                }
                redoffsetInvoiceItem.CustomizeInvoiceDetail = addCustomizeInvoiceDetails;
            }
            else //部分红冲
            {
                if (!oldCustomizeInvoiceCredits.Any())
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，原因：开票单号{oldCustomizeInvoiceItem.Code}没有找到开票明细与应收明细关系，请联系运维操作！");
                }
                if (input.RedDetails != null && input.RedDetails.Any())
                {
                    var addCustomizeInvoiceCredits = new List<CustomizeInvoiceCreditPo>();
                    var redDetails = input.RedDetails.Where(p => p.Quantity > 0 && p.Value >= 0).OrderBy(p => p.Sort).ToList();
                    if (redDetails != null && redDetails.Any())
                    {
                        redoffsetInvoiceItem.RelationType = CustomizeInvoiceRelationTypeEunm.PartRedOffset;
                        oldCustomizeInvoiceItem.ChangedStatus = CustomizeInvoiceChangedStatusEnum.PartRedOffset;
                        if (customizeInvoiceDetails != null && customizeInvoiceDetails.Any())
                        {
                            var addCustomizeInvoiceDetailPos = new List<CustomizeInvoiceDetailPo>();
                            var redDetailIds = customizeInvoiceDetails.Select(p => p.Id).ToHashSet();
                            var customizeInvoiceDetailReds = await _db.CustomizeInvoiceDetail.Include(p => p.CustomizeInvoiceItem).Where(p =>
                                                                                                            p.RelateId.HasValue &&
                                                                                                            p.CustomizeInvoiceItem.Status != CustomizeInvoiceStatusEnum.Cancel &&
                                                                                                            redDetailIds.Contains(p.RelateId.Value)).ToListAsync();
                            var customizeInvoiceDetailRedIds = customizeInvoiceDetailReds.Select(p => p.Id).ToList();
                            var redCustomizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => customizeInvoiceDetailRedIds.Contains(p.CustomizeInvoiceDetailId)).ToListAsync();

                            var checkAmountMsg = new List<string>();
                            int index = 0;
                            foreach (var detail in customizeInvoiceDetails)
                            {
                                var redDetail = redDetails.Where(p => p.Id == detail.Id).FirstOrDefault();
                                if (redDetail != null)
                                {
                                    var childcustomizeInvoiceDetail = childcustomizeInvoiceDetails.FirstOrDefault(p => p.ParentId == detail.Id);
                                    if (detail.Value != 0 && redDetail.Value == 0) //原始明细中的金额不为0，红冲明金额等于0，则跳过
                                    {
                                        index++;
                                        if (childcustomizeInvoiceDetail == null)
                                        {
                                            continue;
                                        }
                                    }
                                    var originDetailId = detail.Id;
                                    var originValue = detail.Value;
                                    detail.Id = Guid.NewGuid();
                                    detail.RelateId = originDetailId;
                                    if (input.IsNoRedConfirm == 1 && redDetail.Price != 0)
                                    {
                                        detail.Quantity = -Math.Abs(Math.Abs(Math.Round(redDetail.Value / redDetail.Price, 10)));
                                        detail.Value = -Math.Abs(Math.Round(redDetail.Value, 2));
                                    }
                                    else
                                    {
                                        detail.Quantity = -redDetail.Quantity;
                                        detail.Value = Math.Round(detail.Quantity * redDetail.Price, 2);
                                    }
                                    if (outputInvoices.Count() - 1 >= index)
                                    {
                                        detail.Sort = outputInvoices[index].RowNo + 1;
                                    }
                                    else
                                    {
                                        detail.Sort = index;
                                    }

                                    detail.Price = redDetail.Price;
                                    detail.TaxAmount = -(Math.Abs(detail.Value) - Math.Abs(detail.Value) / (1 + detail.TaxRate / 100));
                                    addCustomizeInvoiceDetailPos.Add(detail);
                                    var customizeInvoiceDetailRedsTemp = customizeInvoiceDetailReds.Where(p => p.RelateId == redDetail.Id).ToList();
                                    var alreadyRedAmount = customizeInvoiceDetailRedsTemp.Sum(p => Math.Abs(p.Value));//历史红冲金额
                                    if (Math.Abs(detail.Value) + alreadyRedAmount > originValue)
                                    {
                                        if (input.IsNoRedConfirm != 1)
                                        {
                                            checkAmountMsg.Add($"序号[{redDetail.Sort}]，开票名称({detail.ProductName})，数量({redDetail.Quantity})*单价({redDetail.Price})>{originValue - alreadyRedAmount}(可红冲金额)");
                                        }
                                        else
                                        {
                                            checkAmountMsg.Add($"序号[{redDetail.Sort}]，开票名称({detail.ProductName})，红冲金额>{originValue - alreadyRedAmount}(可红冲金额)");
                                        }
                                    }
                                    if (oldCustomizeInvoiceCredits.Any())
                                    {
                                        var isAllRed = false;

                                        if (originValue <= Math.Abs(detail.Value) + alreadyRedAmount)//全部红冲，把剩下负数的开票明细与应收明细关系数据，写入
                                        {
                                            var lessZoreCustomizeInvoiceCredits = orginCustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == originDetailId && p.CustomizeInvoiceDetailAmount < 0).ToList();

                                            //负数取反
                                            foreach (var lessZoreCustomizeInvoiceCredit in lessZoreCustomizeInvoiceCredits)
                                            {
                                                lessZoreCustomizeInvoiceCredit.Id = Guid.NewGuid();
                                                lessZoreCustomizeInvoiceCredit.CustomizeInvoiceItemId = redoffsetInvoiceItem.Id;
                                                lessZoreCustomizeInvoiceCredit.CustomizeInvoiceItemCode = redoffsetInvoiceItem.Code;
                                                lessZoreCustomizeInvoiceCredit.CustomizeInvoiceDetailId = detail.Id;
                                                lessZoreCustomizeInvoiceCredit.Quantity = lessZoreCustomizeInvoiceCredit.Quantity;
                                                lessZoreCustomizeInvoiceCredit.CustomizeInvoiceDetailAmount = lessZoreCustomizeInvoiceCredit.CustomizeInvoiceDetailAmount * -1;
                                                addCustomizeInvoiceCredits.Add(lessZoreCustomizeInvoiceCredit);
                                            }
                                            isAllRed = true;
                                        }

                                        var oldCustomizeInvoiceCreditsTemp = oldCustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == originDetailId &&
                                                                                                                   p.CustomizeInvoiceDetailAmount >= 0).OrderBy(p => p.CustomizeInvoiceDetailAmount).ToList();


                                        var remainAmount = Math.Abs(detail.Value);
                                        if (isAllRed)
                                        {
                                            remainAmount = remainAmount + Math.Abs(addCustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == detail.Id).Sum(p => p.CustomizeInvoiceDetailAmount));
                                        }
                                        foreach (var item in oldCustomizeInvoiceCreditsTemp)
                                        {

                                            var redCustomizeInvoiceCreditAmount = 0m;
                                            if (redCustomizeInvoiceCredits.Any())
                                            {
                                                var redCustomizeInvoiceCreditsTemp = redCustomizeInvoiceCredits.Where(p => p.CreditDetailId == item.CreditDetailId).ToList();
                                                redCustomizeInvoiceCreditAmount = redCustomizeInvoiceCreditsTemp.Sum(p => Math.Abs(p.CustomizeInvoiceDetailAmount));
                                                item.CustomizeInvoiceDetailAmount = item.CustomizeInvoiceDetailAmount - redCustomizeInvoiceCreditAmount;
                                            }
                                            var orignCustomizeInvoiceDetailAmount = item.CustomizeInvoiceDetailAmount;

                                            if (remainAmount >= 0)
                                            {
                                                if (remainAmount >= item.CustomizeInvoiceDetailAmount)
                                                {
                                                    item.Id = Guid.NewGuid();
                                                    item.CustomizeInvoiceItemId = redoffsetInvoiceItem.Id;
                                                    item.CustomizeInvoiceItemCode = redoffsetInvoiceItem.Code;
                                                    item.CustomizeInvoiceDetailId = detail.Id;
                                                    item.Quantity = detail.Quantity;
                                                    item.CustomizeInvoiceDetailAmount = item.CustomizeInvoiceDetailAmount * -1;
                                                    addCustomizeInvoiceCredits.Add(item);
                                                }
                                                else
                                                {
                                                    item.Id = Guid.NewGuid();
                                                    item.CustomizeInvoiceItemId = redoffsetInvoiceItem.Id;
                                                    item.CustomizeInvoiceItemCode = redoffsetInvoiceItem.Code;
                                                    item.CustomizeInvoiceDetailId = detail.Id;
                                                    item.Quantity = remainAmount / item.Price * -1;
                                                    item.CustomizeInvoiceDetailAmount = remainAmount * -1;
                                                    addCustomizeInvoiceCredits.Add(item);
                                                }

                                                //扣除剩下金额
                                                remainAmount = remainAmount - orignCustomizeInvoiceDetailAmount;
                                            }
                                        }

                                    }
                                }
                                if (detail.Value != 0)
                                {
                                    index++;
                                }
                            }

                            if (checkAmountMsg.Count() > 0)
                            {
                                return BaseResponseData<string>.Failed(500, $"操作失败，原因：{string.Join(",", checkAmountMsg)}！");
                            }


                            var redTotalAmount = addCustomizeInvoiceCredits.Sum(p => p.CustomizeInvoiceDetailAmount);//本次红冲金额
                            var alreadyRedTotalAmount = customizeInvoiceDetailReds.Sum(p => Math.Abs(p.Value));//历史红冲金额
                            if (oldCustomizeInvoiceItem.InvoiceTotalAmount <= Math.Abs(redTotalAmount) + alreadyRedTotalAmount)//全部红冲，把剩下负数的开票明细与应收明细关系数据，写入
                            {
                                oldCustomizeInvoiceItem.ChangedStatus = CustomizeInvoiceChangedStatusEnum.RedOffset;

                            }
                            var addCustomizeInvoiceDetailIds = addCustomizeInvoiceDetailPos.Select(p => p.Id).ToHashSet();
                            if (addCustomizeInvoiceCredits.Any())
                            {
                                addCustomizeInvoiceCredits = addCustomizeInvoiceCredits.Where(p => addCustomizeInvoiceDetailIds.Contains(p.CustomizeInvoiceDetailId)).ToList();
                                await _db.CustomizeInvoiceCredits.AddRangeAsync(addCustomizeInvoiceCredits);
                            }

                            if (addCustomizeInvoiceDetailPos.Any())
                            {
                                redoffsetInvoiceItem.CustomizeInvoiceDetail = addCustomizeInvoiceDetailPos;
                                redoffsetInvoiceItem.InvoiceTotalAmount = addCustomizeInvoiceDetailPos.Sum(p => Math.Round(p.Value, 2));
                                redoffsetInvoiceItem.BlueRedInvoiceAmount = addCustomizeInvoiceDetailPos.Sum(p => Math.Round(p.Value, 2));
                                if (redoffsetInvoiceItem.InvoiceTotalAmount == 0)
                                {
                                    return BaseResponseData<string>.Failed(500, "操作失败：红冲总金额不能等于0！");

                                }
                            }
                            else
                            {
                                return BaseResponseData<string>.Failed(500, "操作失败，原因：部分红冲，明细数据没有匹配到原始数据！");
                            }
                        }
                    }
                    else
                    {
                        return BaseResponseData<string>.Failed(500, "操作失败，原因：部分红冲，请选择需要红冲的明细后操作！");
                    }
                }
                else
                {
                    return BaseResponseData<string>.Failed(500, "操作失败，原因：部分红冲，请选择需要红冲的明细后操作！");
                }
            }
            _db.CustomizeInvoiceItem.Update(oldCustomizeInvoiceItem);
            _db.CustomizeInvoiceItem.Add(redoffsetInvoiceItem);
            await _db.SaveChangesAsync();
            return ret;
        }

        /// <summary>
        /// 按个数拆分明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SplitSubmit(SplitSubmitInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");

            var customizeInvoiceDetailPo = await _db.CustomizeInvoiceDetail.
                                                Where(p => p.Id == input.CustomizeInvoiceDetailId).FirstOrDefaultAsync();

            if (customizeInvoiceDetailPo != null)
            {
                #region 校验

                if (string.IsNullOrEmpty(input.ProductName))
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：开票名称不能为空");
                }
                if (Math.Abs(input.Quantity) < 0.01M)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：拆分数量最小为0.01");
                }

                if (customizeInvoiceDetailPo.Quantity == 0 || customizeInvoiceDetailPo.Price == 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：数量为0或者单价为0不支持拆分");
                }
                var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceDetailPo.CustomizeInvoiceItemId);
                if (ret2.Code != CodeStatusEnum.Success)
                {
                    return BaseResponseData<int>.Failed(500, ret2.Message);
                }
                if (customizeInvoiceDetailPo.Tag == "折扣行")
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：折扣行开票明细不支持拆分");
                }
                var customizeInvoiceDetailPo2 = await _db.CustomizeInvoiceDetail.
                                            Where(p => p.ParentId == customizeInvoiceDetailPo.Id).FirstOrDefaultAsync();
                if (customizeInvoiceDetailPo2 != null)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：该商品行存在折扣行不允许拆分");
                }
                #endregion
                if (!input.Classify.HasValue || input.Classify.Value == 0) //按数量拆分
                {
                    var quantityStr = ((double)input.Quantity).ToString();
                    if (quantityStr.Contains("."))
                    {
                        var tempquantity = quantityStr.Split('.');
                        if (tempquantity.Length == 2 && tempquantity[1].Length > 10)
                        {
                            return BaseResponseData<int>.Failed(500, "操作失败，原因：输入数量小数位最多10位");
                        }
                    }
                    await SplitNumber(input);
                }
                else if (input.Classify.Value == 1) //按份数拆分
                {
                    var number = ((double)(Math.Abs(customizeInvoiceDetailPo.Quantity) / input.Quantity)).ToString();
                    if (number.Contains("."))
                    {
                        var tempquantity = number.Split('.');
                        if (tempquantity.Length == 2 && tempquantity[1].Length > 2)// 校验小数位最多两位
                        {
                            return BaseResponseData<int>.Failed(500, "操作失败，原因：明细数量/份数=小数位最多2位");
                        }
                    }
                    await SplitMultiple(input, customizeInvoiceDetailPo);
                }
            }
            else
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败，原因：没有找到开票明细数据！");
            }
            return ret;
        }

        /// <summary>
        /// 合并明细(开票名称，单价，原价合并)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> MargeByProductNamePriceOriginPrice(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input == null || !input.Any())
            {
                return BaseResponseData<int>.Failed(500, "操作失败：参数异常");
            }

            var customizeInvoiceItemId = input.First().CustomizeInvoiceItemId;
            var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
            if (ret2.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<int>.Failed(500, ret2.Message);
            }
            else
            {
                //商品行合并
                var disCount = input.Where(p => p.Tag == "折扣行").Count();
                if (disCount > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：折扣行不能和其他的商品行合并");
                }
                var detailIds = input.Select(p => p.Id).ToList();
                var customizeInvoiceDetailsParent = await _db.CustomizeInvoiceDetail.
                                              Where(p =>
                                              p.ParentId.HasValue &&
                                              detailIds.Contains(p.ParentId.Value) &&
                                              p.CustomizeInvoiceItemId == input[0].CustomizeInvoiceItemId).ToListAsync();
                if (customizeInvoiceDetailsParent.Count() > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：选择的商品行明细中存在派生的折扣行");
                }
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => detailIds.Contains(p.CustomizeInvoiceDetailId)).ToListAsync();
                var creditCodes = customizeInvoiceCredits.Select(p => p.CreditCode).ToList();
                var credits = await _db.Credits.Where(p => creditCodes.Contains(p.BillCode) && p.SaleSource == SaleSourceEnum.Spd && p.CreditType != CreditTypeEnum.servicefee).ToListAsync();
                if (credits.Any())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：SPD应收单不支持合并操作!");
                }
                var newDetails = new List<CustomizeInvoiceDetailPo>();
                var newDetailId = Guid.NewGuid();
                input.GroupBy(p => new { p.Price, p.ProductName, p.CustomizeInvoiceItemId, p.OriginalPrice }).ForEach(p =>
                {
                    var products = input.Where(x => x.ProductName == p.Key.ProductName && x.Price == p.Key.Price && x.OriginalPrice == p.Key.OriginalPrice).ToList();
                    var detailIdsTemp = products.Select(p => p.Id).ToList();
                    var product = products.First();
                    var newDetailId = Guid.NewGuid();
                    var newDetail = new CustomizeInvoiceDetailPo
                    {
                        Id = newDetailId,
                        Price = p.Key.Price,
                        CreatedBy = "none",
                        CreatedTime = DateTimeOffset.Now,
                        CustomerId = product.CustomerId,
                        CustomerName = product.CustomerName,
                        CustomizeInvoiceItemId = p.Key.CustomizeInvoiceItemId,
                        ProductName = product.ProductName,
                        ProductId = product.ProductId,
                        ProductNo = product.ProductNo,
                        TaxRate = product.TaxRate,
                        TaxTypeNo = product.TaxTypeNo,
                        Quantity = p.Sum(p => p.Quantity),
                        Value = p.Sum(p => p.Quantity) * p.Key.Price,
                        CreditBillCode = string.Join(",", p.Select(d => d.CreditBillCode).Distinct()),
                        OrderNo = string.Join(",", p.Select(d => d.OrderNo).Distinct()),
                        RelateCode = string.Join(",", p.Select(d => d.RelateCode).Distinct()),
                        OriginDetailId = product.OriginDetailId,
                        OriginProductName = product.OriginProductName,
                        PackUnit = product.PackUnit,
                        AgentId = string.Join(",", p.Select(d => d.AgentId).Distinct()),
                        Specification = product.Specification,
                        OriginalPrice = product.OriginalPrice,
                        OriginSpecification = product.OriginSpecification,
                        OriginPackUnit = product.OriginPackUnit,
                        Tag = product.Tag,
                        CustomizeInvoiceIndex = product.CustomizeInvoiceIndex + "",
                        Sort = product.Sort,
                        IFHighValue = products.OrderByDescending(p => p.IFHighValue).First().IFHighValue,
                        PriceSource = products.OrderByDescending(p => p.PriceSource).First().PriceSource,
                    };
                    newDetail.TaxAmount = newDetail.Value - (newDetail.Value / (1 + (newDetail.TaxRate / 100)));
                    newDetails.Add(newDetail);
                    var customizeInvoiceCreditsTemp = customizeInvoiceCredits.Where(p => detailIdsTemp.Contains(p.CustomizeInvoiceDetailId)).ToList();
                    foreach (var item in customizeInvoiceCreditsTemp)
                    {
                        item.CustomizeInvoiceDetailId = newDetailId;
                    }
                });
                if (input.Count() == newDetails.Count())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：按开票名称，单价，原价合并失败!");
                }
                var oldDetails = await _db.CustomizeInvoiceDetail.Where(p => detailIds.Contains(p.Id)).ToListAsync();
                _db.CustomizeInvoiceDetail.RemoveRange(oldDetails);
                await _db.CustomizeInvoiceDetail.AddRangeAsync(newDetails);
                ret.Data = await _db.SaveChangesAsync();
            }
            return ret;
        }

        /// <summary>
        /// 折扣行合并
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> MergeByDiscount(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input == null || !input.Any())
            {
                return BaseResponseData<int>.Failed(500, "操作失败：参数异常");
            }
            else if (input.Count() != 2)
            {
                return BaseResponseData<int>.Failed(500, "操作失败：折扣行合并选择明细数必须等于2条明细");
            }


            var customizeInvoiceItemId = input.First().CustomizeInvoiceItemId;
            var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
            if (ret2.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<int>.Failed(500, ret2.Message);
            }
            var tempIds = input.Select(p => p.Id);
            var customizeInvoiceDetails = await _db.CustomizeInvoiceDetail.Where(p => tempIds.Contains(p.Id)).OrderBy(p => p.ParentId).ToListAsync();
            if (customizeInvoiceDetails[0].Id == customizeInvoiceDetails[1].ParentId)
            {
                var taxRate = customizeInvoiceDetails[0].TaxRate / 100;
                _db.CustomizeInvoiceDetail.Remove(customizeInvoiceDetails[1]);
                customizeInvoiceDetails[0].Price -= customizeInvoiceDetails[1].Price;
                customizeInvoiceDetails[0].Value = customizeInvoiceDetails[0].Quantity * customizeInvoiceDetails[0].Price;
                customizeInvoiceDetails[0].TaxAmount = (customizeInvoiceDetails[0].Value / (1 + taxRate) * taxRate);
                ret.Data = await _db.SaveChangesAsync();
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "操作失败：折扣行合并选择明细异常");
            }
            return ret;
        }

        /// <summary>
        /// 合并明细(开票名称，单价，开票规格)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> MargeByProductNamePriceSpecification(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input == null || !input.Any())
            {
                return BaseResponseData<int>.Failed(500, "操作失败：参数异常");
            }

            var customizeInvoiceItemId = input.First().CustomizeInvoiceItemId;
            var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
            if (ret2.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<int>.Failed(500, ret2.Message);
            }
            else
            {
                //商品行合并
                var disCount = input.Where(p => p.Tag == "折扣行").Count();
                if (disCount > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：折扣行不能和其他的商品行合并");
                }
                var detailIds = input.Select(p => p.Id).ToList();
                var customizeInvoiceDetailsParent = await _db.CustomizeInvoiceDetail.
                                              Where(p =>
                                              p.ParentId.HasValue &&
                                              detailIds.Contains(p.ParentId.Value) &&
                                              p.CustomizeInvoiceItemId == input[0].CustomizeInvoiceItemId).ToListAsync();
                if (customizeInvoiceDetailsParent.Count() > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：选择的商品行明细中存在派生的折扣行");
                }
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => detailIds.Contains(p.CustomizeInvoiceDetailId)).ToListAsync();
                var creditCodes = customizeInvoiceCredits.Select(p => p.CreditCode).ToList();
                var credits = await _db.Credits.Where(p => creditCodes.Contains(p.BillCode) && p.SaleSource == SaleSourceEnum.Spd && p.CreditType != CreditTypeEnum.servicefee).ToListAsync();
                if (credits.Any())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：SPD应收单不支持合并操作!");
                }
                var newDetails = new List<CustomizeInvoiceDetailPo>();
                var newDetailId = Guid.NewGuid();
                input.GroupBy(p => new { p.Price, p.ProductName, p.CustomizeInvoiceItemId, p.Specification }).ForEach(p =>
                {
                    var products = input.Where(x => x.ProductName == p.Key.ProductName && x.Price == p.Key.Price && x.Specification == p.Key.Specification).ToList();
                    var detailIdsTemp = products.Select(p => p.Id).ToList();
                    var product = products.First();
                    var newDetailId = Guid.NewGuid();
                    var newDetail = new CustomizeInvoiceDetailPo
                    {
                        Id = newDetailId,
                        Price = p.Key.Price,
                        CreatedBy = "none",
                        CreatedTime = DateTimeOffset.Now,
                        CustomerId = product.CustomerId,
                        CustomerName = product.CustomerName,
                        CustomizeInvoiceItemId = p.Key.CustomizeInvoiceItemId,
                        ProductName = product.ProductName,
                        ProductId = product.ProductId,
                        ProductNo = product.ProductNo,
                        TaxRate = product.TaxRate,
                        TaxTypeNo = product.TaxTypeNo,
                        Quantity = p.Sum(p => p.Quantity),
                        Value = p.Sum(p => p.Quantity) * p.Key.Price,
                        CreditBillCode = string.Join(",", p.Select(d => d.CreditBillCode).Distinct()),
                        OrderNo = string.Join(",", p.Select(d => d.OrderNo).Distinct()),
                        RelateCode = string.Join(",", p.Select(d => d.RelateCode).Distinct()),
                        OriginDetailId = product.OriginDetailId,
                        OriginProductName = product.OriginProductName,
                        PackUnit = product.PackUnit,
                        AgentId = string.Join(",", p.Select(d => d.AgentId).Distinct()),
                        Specification = product.Specification,
                        OriginalPrice = product.OriginalPrice,
                        OriginSpecification = product.OriginSpecification,
                        OriginPackUnit = product.OriginPackUnit,
                        Tag = product.Tag,
                        CustomizeInvoiceIndex = product.CustomizeInvoiceIndex + "",
                        Sort = product.Sort,
                        IFHighValue = products.OrderByDescending(p => p.IFHighValue).First().IFHighValue,
                        PriceSource = products.OrderByDescending(p => p.PriceSource).First().PriceSource,
                    };
                    newDetail.TaxAmount = newDetail.Value - (newDetail.Value / (1 + (newDetail.TaxRate / 100)));
                    newDetails.Add(newDetail);
                    var customizeInvoiceCreditsTemp = customizeInvoiceCredits.Where(p => detailIdsTemp.Contains(p.CustomizeInvoiceDetailId)).ToList();
                    foreach (var item in customizeInvoiceCreditsTemp)
                    {
                        item.CustomizeInvoiceDetailId = newDetailId;
                    }
                });
                if (input.Count() == newDetails.Count())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：按开票名称，单价，开票规格合并失败!");
                }
                var oldDetails = await _db.CustomizeInvoiceDetail.Where(p => detailIds.Contains(p.Id)).ToListAsync();
                _db.CustomizeInvoiceDetail.RemoveRange(oldDetails);
                await _db.CustomizeInvoiceDetail.AddRangeAsync(newDetails);
                ret.Data = await _db.SaveChangesAsync();
            }
            return ret;
        }

        /// <summary>
        /// 合并明细(原始规格，单价，原价)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> MargeByOriginSpecificationPriceOriginPrice(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input == null || !input.Any())
            {
                return BaseResponseData<int>.Failed(500, "操作失败：参数异常");
            }
            var customizeInvoiceItemId = input.First().CustomizeInvoiceItemId;
            var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
            if (ret2.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<int>.Failed(500, ret2.Message);
            }
            else
            {
                //商品行合并
                var disCount = input.Where(p => p.Tag == "折扣行").Count();
                if (disCount > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：折扣行不能和其他的商品行合并");
                }
                var detailIds = input.Select(p => p.Id).ToList();
                var customizeInvoiceDetailsParent = await _db.CustomizeInvoiceDetail.
                                              Where(p =>
                                              p.ParentId.HasValue &&
                                              detailIds.Contains(p.ParentId.Value) &&
                                              p.CustomizeInvoiceItemId == input[0].CustomizeInvoiceItemId).ToListAsync();
                if (customizeInvoiceDetailsParent.Count() > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：选择的商品行明细中存在派生的折扣行");
                }
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => detailIds.Contains(p.CustomizeInvoiceDetailId)).ToListAsync();
                var creditCodes = customizeInvoiceCredits.Select(p => p.CreditCode).ToList();
                //var credits = await _db.Credits.Where(p => creditCodes.Contains(p.BillCode) && p.SaleSource == SaleSourceEnum.Spd && p.CreditType != CreditTypeEnum.servicefee).ToListAsync();
                //if (credits.Any())
                //{
                //    return BaseResponseData<int>.Failed(500, "操作失败：SPD应收单不支持合并操作!");
                //}
                var newDetails = new List<CustomizeInvoiceDetailPo>();
                var newDetailId = Guid.NewGuid();
                input.GroupBy(p => new { p.Price, p.OriginSpecification, p.CustomizeInvoiceItemId, p.OriginalPrice }).ForEach(p =>
                {
                    var products = input.Where(x => x.OriginSpecification == p.Key.OriginSpecification && x.Price == p.Key.Price && x.OriginalPrice == p.Key.OriginalPrice).ToList();
                    var detailIdsTemp = products.Select(p => p.Id).ToList();
                    var product = products.First();
                    var newDetailId = Guid.NewGuid();
                    var newDetail = new CustomizeInvoiceDetailPo
                    {
                        Id = newDetailId,
                        Price = p.Key.Price,
                        CreatedBy = "none",
                        CreatedTime = DateTimeOffset.Now,
                        CustomerId = product.CustomerId,
                        CustomerName = product.CustomerName,
                        CustomizeInvoiceItemId = p.Key.CustomizeInvoiceItemId,
                        ProductName = product.ProductName,
                        ProductId = product.ProductId,
                        ProductNo = product.ProductNo,
                        TaxRate = product.TaxRate,
                        TaxTypeNo = product.TaxTypeNo,
                        Quantity = p.Sum(p => p.Quantity),
                        Value = p.Sum(p => p.Quantity) * p.Key.Price,
                        CreditBillCode = string.Join(",", p.Select(d => d.CreditBillCode).Distinct()),
                        OrderNo = string.Join(",", p.Select(d => d.OrderNo).Distinct()),
                        RelateCode = string.Join(",", p.Select(d => d.RelateCode).Distinct()),
                        OriginDetailId = product.OriginDetailId,
                        OriginProductName = product.OriginProductName,
                        PackUnit = product.PackUnit,
                        AgentId = string.Join(",", p.Select(d => d.AgentId).Distinct()),
                        Specification = product.Specification,
                        OriginalPrice = product.OriginalPrice,
                        OriginSpecification = product.OriginSpecification,
                        OriginPackUnit = product.OriginPackUnit,
                        Tag = product.Tag,
                        CustomizeInvoiceIndex = product.CustomizeInvoiceIndex + "",
                        Sort = product.Sort,
                        IFHighValue = products.OrderByDescending(p => p.IFHighValue).First().IFHighValue,
                        PriceSource = products.OrderByDescending(p => p.PriceSource).First().PriceSource,
                    };
                    newDetail.TaxAmount = newDetail.Value - (newDetail.Value / (1 + (newDetail.TaxRate / 100)));
                    newDetails.Add(newDetail);
                    var customizeInvoiceCreditsTemp = customizeInvoiceCredits.Where(p => detailIdsTemp.Contains(p.CustomizeInvoiceDetailId)).ToList();
                    foreach (var item in customizeInvoiceCreditsTemp)
                    {
                        item.CustomizeInvoiceDetailId = newDetailId;
                    }
                });
                if (input.Count() == newDetails.Count())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：按原始规格，单价，原价合并失败!");
                }
                var oldDetails = await _db.CustomizeInvoiceDetail.Where(p => detailIds.Contains(p.Id)).ToListAsync();
                _db.CustomizeInvoiceDetail.RemoveRange(oldDetails);
                await _db.CustomizeInvoiceDetail.AddRangeAsync(newDetails);
                ret.Data = await _db.SaveChangesAsync();
            }
            return ret;
        }
        /// <summary>
        /// 按规格合并(开票名称，单价)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> MargeByProductNamePrice(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input == null || !input.Any())
            {
                return BaseResponseData<int>.Failed(500, "操作失败：参数异常");
            }

            var customizeInvoiceItemId = input.First().CustomizeInvoiceItemId;
            var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
            if (ret2.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<int>.Failed(500, ret2.Message);
            }
            else
            {
                //商品行合并
                var disCount = input.Where(p => p.Tag == "折扣行").Count();
                if (disCount > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：折扣行不能和其他的商品行合并");
                }
                var detailIds = input.Select(p => p.Id).ToList();
                var customizeInvoiceDetailsParent = await _db.CustomizeInvoiceDetail.
                                              Where(p =>
                                              p.ParentId.HasValue &&
                                              detailIds.Contains(p.ParentId.Value) &&
                                              p.CustomizeInvoiceItemId == input[0].CustomizeInvoiceItemId).ToListAsync();
                if (customizeInvoiceDetailsParent.Count() > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：选择的商品行明细中存在派生的折扣行");
                }
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => detailIds.Contains(p.CustomizeInvoiceDetailId)).ToListAsync();
                var creditCodes = customizeInvoiceCredits.Select(p => p.CreditCode).ToList();
                var credits = await _db.Credits.Where(p => creditCodes.Contains(p.BillCode) && p.SaleSource == SaleSourceEnum.Spd && p.CreditType != CreditTypeEnum.servicefee).ToListAsync();
                if (credits.Any())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：SPD应收单不支持合并操作!");
                }
                var newDetails = new List<CustomizeInvoiceDetailPo>();
                var newDetailId = Guid.NewGuid();
                input.GroupBy(p => new { p.Price, p.ProductName, p.CustomizeInvoiceItemId }).ForEach(p =>
                {
                    var products = input.Where(x => x.ProductName == p.Key.ProductName && x.Price == p.Key.Price).ToList();
                    var detailIdsTemp = products.Select(p => p.Id).ToList();
                    var product = products.First();
                    var newDetailId = Guid.NewGuid();
                    var newDetail = new CustomizeInvoiceDetailPo
                    {
                        Id = newDetailId,
                        Price = p.Key.Price,
                        CreatedBy = "none",
                        CreatedTime = DateTimeOffset.Now,
                        CustomerId = product.CustomerId,
                        CustomerName = product.CustomerName,
                        CustomizeInvoiceItemId = p.Key.CustomizeInvoiceItemId,
                        ProductName = product.ProductName,
                        ProductId = product.ProductId,
                        ProductNo = product.ProductNo,
                        TaxRate = product.TaxRate,
                        TaxTypeNo = product.TaxTypeNo,
                        Quantity = p.Sum(p => p.Quantity),
                        Value = p.Sum(p => p.Quantity) * p.Key.Price,
                        CreditBillCode = string.Join(",", p.Select(d => d.CreditBillCode).Distinct()),
                        OrderNo = string.Join(",", p.Select(d => d.OrderNo).Distinct()),
                        RelateCode = string.Join(",", p.Select(d => d.RelateCode).Distinct()),
                        OriginDetailId = product.OriginDetailId,
                        OriginProductName = product.OriginProductName,
                        PackUnit = product.PackUnit,
                        AgentId = string.Join(",", p.Select(d => d.AgentId).Distinct()),
                        Specification = product.Specification,
                        OriginalPrice = product.OriginalPrice,
                        OriginSpecification = product.OriginSpecification,
                        OriginPackUnit = product.OriginPackUnit,
                        Tag = product.Tag,
                        CustomizeInvoiceIndex = product.CustomizeInvoiceIndex + "",
                        Sort = product.Sort,
                        IFHighValue = products.OrderByDescending(p => p.IFHighValue).First().IFHighValue,
                        PriceSource = products.OrderByDescending(p => p.PriceSource).First().PriceSource,
                    };
                    newDetail.TaxAmount = newDetail.Value - (newDetail.Value / (1 + (newDetail.TaxRate / 100)));
                    newDetails.Add(newDetail);
                    var customizeInvoiceCreditsTemp = customizeInvoiceCredits.Where(p => detailIdsTemp.Contains(p.CustomizeInvoiceDetailId)).ToList();
                    foreach (var item in customizeInvoiceCreditsTemp)
                    {
                        item.CustomizeInvoiceDetailId = newDetailId;
                    }
                });
                if (input.Count() == newDetails.Count())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：按开票名称，单价合并失败!");
                }
                var oldDetails = await _db.CustomizeInvoiceDetail.Where(p => detailIds.Contains(p.Id)).ToListAsync();
                _db.CustomizeInvoiceDetail.RemoveRange(oldDetails);
                await _db.CustomizeInvoiceDetail.AddRangeAsync(newDetails);
                ret.Data = await _db.SaveChangesAsync();
            }
            return ret;
        }
        /// <summary>
        /// 输入数量和单价把选择的明细合并成一行
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> MergeByInputQuantityPrice(List<CustomizeInvoiceDetailOutput> input, decimal quantity, decimal price)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input == null || !input.Any())
            {
                return BaseResponseData<int>.Failed(500, "操作失败：参数异常");
            }
            if (Math.Abs(quantity) < 0.01M)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：拆分数量最小为0.01");
            }
            var quantityStr = ((double)quantity).ToString();
            if (quantityStr.Contains("."))
            {
                var tempquantity = quantityStr.Split('.');
                if (tempquantity.Length == 2 && tempquantity[1].Length > 10)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：输入数量小数位最多10位");
                }
            }
            if (price <= 0)
            {
                return BaseResponseData<int>.Failed(500, "操作失败：单价不能小于0");
            }
            if (price <= 0.0001M)
            {
                return BaseResponseData<int>.Failed(500, "操作失败：单价最小为4位小数");
            }
            var customizeInvoiceItemId = input.First().CustomizeInvoiceItemId;
            var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
            if (ret2.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<int>.Failed(500, ret2.Message);
            }
            else
            {
                var detailIds = input.Select(p => p.Id).ToList();
                if (detailIds.Count <= 1)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：请选择多行明细操作");
                }
                var oldDetails = await _db.CustomizeInvoiceDetail.Where(p => detailIds.Contains(p.Id)).ToListAsync();
                var sumValue = oldDetails.Sum(p => p.Value);
                if (sumValue != Math.Round(quantity * price, 2))
                {
                    return BaseResponseData<int>.Failed(500, $"操作失败：（数量）{quantity}*（单价）{price}≠{sumValue}");
                }
                //商品行合并
                var disCount = input.Where(p => p.Tag == "折扣行").Count();
                if (disCount > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：折扣行不能和其他的商品行合并");
                }
                var customizeInvoiceDetailsParent = await _db.CustomizeInvoiceDetail.
                                              Where(p =>
                                              p.ParentId.HasValue &&
                                              detailIds.Contains(p.ParentId.Value) &&
                                              p.CustomizeInvoiceItemId == input[0].CustomizeInvoiceItemId).ToListAsync();
                if (customizeInvoiceDetailsParent.Count() > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：选择的商品行明细中存在派生的折扣行");
                }
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => detailIds.Contains(p.CustomizeInvoiceDetailId)).ToListAsync();
                var creditCodes = customizeInvoiceCredits.Select(p => p.CreditCode).ToList();
                var credits = await _db.Credits.Where(p => creditCodes.Contains(p.BillCode) && p.SaleSource == SaleSourceEnum.Spd && p.CreditType != CreditTypeEnum.servicefee).ToListAsync();
                if (credits.Any())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：SPD应收单不支持合并操作!");
                }
                var newDetails = new List<CustomizeInvoiceDetailPo>();
                var newDetailId = Guid.NewGuid();


                input.GroupBy(p => new { p.CustomizeInvoiceItemId }).ForEach(p =>
                {
                    var products = input.Where(x => x.CustomizeInvoiceItemId == p.Key.CustomizeInvoiceItemId).ToList();
                    var detailIdsTemp = products.Select(p => p.Id).ToList();
                    var product = products.First();
                    var newDetailId = Guid.NewGuid();
                    var newDetail = new CustomizeInvoiceDetailPo
                    {
                        Id = newDetailId,
                        Price = price,
                        CreatedBy = "none",
                        CreatedTime = DateTimeOffset.Now,
                        CustomerId = product.CustomerId,
                        CustomerName = product.CustomerName,
                        CustomizeInvoiceItemId = p.Key.CustomizeInvoiceItemId,
                        ProductName = product.ProductName,
                        ProductId = product.ProductId,
                        ProductNo = product.ProductNo,
                        TaxRate = product.TaxRate,
                        TaxTypeNo = product.TaxTypeNo,
                        Quantity = quantity,
                        Value = quantity * price,
                        CreditBillCode = string.Join(",", p.Select(d => d.CreditBillCode).Distinct()),
                        OrderNo = string.Join(",", p.Select(d => d.OrderNo).Distinct()),
                        RelateCode = string.Join(",", p.Select(d => d.RelateCode).Distinct()),
                        OriginDetailId = product.OriginDetailId,
                        OriginProductName = product.OriginProductName,
                        PackUnit = product.PackUnit,
                        AgentId = string.Join(",", p.Select(d => d.AgentId).Distinct()),
                        Specification = product.Specification,
                        OriginalPrice = product.OriginalPrice,
                        OriginSpecification = product.OriginSpecification,
                        OriginPackUnit = product.OriginPackUnit,
                        Tag = product.Tag,
                        CustomizeInvoiceIndex = product.CustomizeInvoiceIndex + "",
                        Sort = product.Sort,
                        IFHighValue = products.OrderByDescending(p => p.IFHighValue).First().IFHighValue,
                        PriceSource = products.OrderByDescending(p => p.PriceSource).First().PriceSource,
                    };
                    newDetail.TaxAmount = newDetail.Value - (newDetail.Value / (1 + (newDetail.TaxRate / 100)));
                    newDetails.Add(newDetail);
                    var customizeInvoiceCreditsTemp = customizeInvoiceCredits.Where(p => detailIdsTemp.Contains(p.CustomizeInvoiceDetailId)).ToList();
                    foreach (var item in customizeInvoiceCreditsTemp)
                    {
                        item.CustomizeInvoiceDetailId = newDetailId;
                    }
                });
                _db.CustomizeInvoiceDetail.RemoveRange(oldDetails);
                await _db.CustomizeInvoiceDetail.AddRangeAsync(newDetails);
                ret.Data = await _db.SaveChangesAsync();
            }
            return ret;
        }

        /// <summary>
        /// 修改明细单价，数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> UpdateDetailQuantityPrice(MergeByInputQuantityPriceInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input == null || !input.customizeInvoiceDetails.Any())
            {
                return BaseResponseData<int>.Failed(500, "操作失败：参数异常");
            }
            if (Math.Abs(input.quantity) < 0.01M)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：拆分数量最小为0.01");
            }
            var quantityStr = ((double)input.quantity).ToString();
            if (quantityStr.Contains("."))
            {
                var tempquantity = quantityStr.Split('.');
                if (tempquantity.Length == 2 && tempquantity[1].Length > 10)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：输入数量小数位最多10位");
                }
            }
            if (input.price <= 0)
            {
                return BaseResponseData<int>.Failed(500, "操作失败：单价不能小于0");
            }
            if (input.price <= 0.0001M)
            {
                return BaseResponseData<int>.Failed(500, "操作失败：单价最小为4位小数");
            }
            var customizeInvoiceItemId = input.customizeInvoiceDetails.First().CustomizeInvoiceItemId;
            var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
            if (ret2.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<int>.Failed(500, ret2.Message);
            }
            else
            {
                var detailIds = input.customizeInvoiceDetails.Select(p => p.Id).ToList();
                if (detailIds.Count > 1)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：不支持多行编辑明细的数量金额");
                }
                var disCount = input.customizeInvoiceDetails.Where(p => p.Tag == "折扣行").Count();
                if (disCount > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：折扣行不能编辑数量和单价");
                }
                var oldDetails = await _db.CustomizeInvoiceDetail.Where(p => detailIds.Contains(p.Id)).ToListAsync();
                var sumValue = oldDetails.Sum(p => p.Value);
                var customizeInvoiceDetailIds = oldDetails.Select(p => p.Id).ToList();
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => customizeInvoiceDetailIds.Contains(p.CustomizeInvoiceDetailId)).ToListAsync();

                var creditCodes = customizeInvoiceCredits.Select(p => p.CreditCode).ToHashSet();
                var credits = await _db.Credits.Where(p => creditCodes.Contains(p.BillCode)).ToListAsync();
                if (credits.Count(p => p.SaleSource == SaleSourceEnum.Spd && p.CreditType != CreditTypeEnum.servicefee && p.CreditType != CreditTypeEnum.servicefeerevise) > 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败：spd应收且不是服务费应收的，不能编辑数量和单价");
                }
                if (sumValue != Math.Round(input.quantity * input.price, 2))
                {
                    return BaseResponseData<int>.Failed(500, $"操作失败：（数量）{input.quantity}*（单价）{input.price}≠{sumValue}");
                }
                foreach (var item in oldDetails)
                {
                    item.Quantity = input.quantity;
                    item.Price = input.price;
                }
                ret.Data = await _db.SaveChangesAsync();
            }
            return ret;
        }

        /// <summary>
        /// 按份数拆分
        /// </summary>
        /// <param name="input"></param>
        /// <param name="customizeInvoiceDetailPo"></param>
        /// <returns></returns>
        private async Task SplitMultiple(SplitSubmitInput input, CustomizeInvoiceDetailPo? customizeInvoiceDetailPo)
        {
            if (input.Quantity <= 0)
            {
                throw new ApplicationException("操作失败，原因：输入份数不能小于0");
            }
            int number;
            if (int.TryParse(input.Quantity.ToString(), out number))
            {
                var qauntity = customizeInvoiceDetailPo.Quantity / input.Quantity;
                var newDetailId = Guid.Empty;
                for (int i = 0; i < input.Quantity - 1; i++)
                {
                    newDetailId = await SplitNumber(new SplitSubmitInput
                    {
                        CustomizeInvoiceDetailId = newDetailId.Equals(Guid.Empty) ? input.CustomizeInvoiceDetailId : newDetailId,
                        ProductName = input.ProductName,
                        Quantity = qauntity,
                    });
                }
            }
            else
            {
                throw new ApplicationException("操作失败，原因： 不是一个有效的整数");
            }

        }

        /// <summary>
        ///按个数拆分
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<Guid> SplitNumber(SplitSubmitInput input)
        {
            var customizeInvoiceDetailPo = await _db.CustomizeInvoiceDetail.
                                                Where(p => p.Id == input.CustomizeInvoiceDetailId).FirstOrDefaultAsync();
            if (customizeInvoiceDetailPo == null)
            {
                throw new ApplicationException("操作失败，原因：没有找到数据");
            }
            if (customizeInvoiceDetailPo.Quantity > 0 && input.Quantity < 0)
            {
                throw new ApplicationException("操作失败，原因：该单不支持负数拆分");
            }
            if (customizeInvoiceDetailPo.Quantity < 0 && input.Quantity > 0)
            {
                throw new ApplicationException("操作失败，原因：该单不支持正数拆分");
            }
            if (Math.Abs(input.Quantity) >= Math.Abs(customizeInvoiceDetailPo.Quantity))
            {
                throw new ApplicationException("操作失败，原因：拆分数量不能大于实际数量");
            }
            var CustomizeInvoiceDetailOldPo = customizeInvoiceDetailPo.DeepClone();
            var CustomizeInvoiceDetailNewPo = customizeInvoiceDetailPo.DeepClone();
            CustomizeInvoiceDetailNewPo.Id = Guid.NewGuid();
            CustomizeInvoiceDetailOldPo.Id = Guid.NewGuid();
            CustomizeInvoiceDetailOldPo.OriginDetailId = customizeInvoiceDetailPo.Id.ToString();
            CustomizeInvoiceDetailNewPo.OriginDetailId = CustomizeInvoiceDetailOldPo.Id.ToString();
            //-------------------------------New--------------------------------------
            CustomizeInvoiceDetailNewPo.ProductName = input.ProductName;
            CustomizeInvoiceDetailNewPo.Quantity = input.Quantity;
            CustomizeInvoiceDetailNewPo.Value = decimal.Parse((input.Quantity * customizeInvoiceDetailPo.Price).ToString("F2"));
            var rate = 1 + (customizeInvoiceDetailPo.TaxRate / 100);
            CustomizeInvoiceDetailNewPo.TaxAmount = input.Quantity > 0 ? Math.Abs(CustomizeInvoiceDetailNewPo.Value) - (Math.Abs(CustomizeInvoiceDetailNewPo.Value) / rate) : -(Math.Abs(CustomizeInvoiceDetailNewPo.Value) - (Math.Abs(CustomizeInvoiceDetailNewPo.Value) / rate));
            await _db.CustomizeInvoiceDetail.AddAsync(CustomizeInvoiceDetailNewPo);

            var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits
                                                    .Where(p => p.CustomizeInvoiceDetailId == input.CustomizeInvoiceDetailId)
                                                    .OrderBy(p => Math.Abs(p.Quantity))
                                                    .ToListAsync();
            if (customizeInvoiceCredits.Any())
            {
                var customizeInvoiceCreditNewLst = await SplitCustomizeInvoiceCredit(input.Quantity,
                    input.CustomizeInvoiceDetailId,
                    CustomizeInvoiceDetailOldPo,
                    CustomizeInvoiceDetailNewPo
                    );
                if (customizeInvoiceCreditNewLst.Any())
                {
                    customizeInvoiceCreditNewLst.Where(p => p.CustomizeInvoiceDetailAmount != 0).ToList();
                    await _db.CustomizeInvoiceCredits.AddRangeAsync(customizeInvoiceCreditNewLst);
                }
            }

            //---------------------------------Old----------------------------------
            CustomizeInvoiceDetailOldPo.Quantity = CustomizeInvoiceDetailOldPo.Quantity > 0 ? Math.Abs(CustomizeInvoiceDetailOldPo.Quantity) - Math.Abs(input.Quantity) : -(Math.Abs(CustomizeInvoiceDetailOldPo.Quantity) - Math.Abs(input.Quantity));
            CustomizeInvoiceDetailOldPo.Value = CustomizeInvoiceDetailOldPo.Quantity > 0 ? Math.Abs(CustomizeInvoiceDetailOldPo.Value) - Math.Abs(CustomizeInvoiceDetailNewPo.Value) : -(Math.Abs(CustomizeInvoiceDetailOldPo.Value) - Math.Abs(CustomizeInvoiceDetailNewPo.Value));
            CustomizeInvoiceDetailOldPo.TaxAmount = CustomizeInvoiceDetailOldPo.Quantity > 0 ? Math.Abs(CustomizeInvoiceDetailOldPo.TaxAmount) - Math.Abs(CustomizeInvoiceDetailNewPo.TaxAmount) : -(Math.Abs(CustomizeInvoiceDetailOldPo.TaxAmount) - Math.Abs(CustomizeInvoiceDetailNewPo.TaxAmount));

            await _db.CustomizeInvoiceDetail.AddAsync(CustomizeInvoiceDetailOldPo);
            _db.CustomizeInvoiceDetail.Remove(customizeInvoiceDetailPo);
            await _db.SaveChangesAsync();
            return CustomizeInvoiceDetailOldPo.Id;
        }

        /// <summary>
        /// 拆分开票明细（同一个申开单）
        /// </summary>
        /// <param name="splitQuantity">拆分数量</param>
        /// <param name="customizeInvoiceDetailOriginId">原始开票明细Id</param>
        /// <param name="customizeInvoiceDetailOld">老开票明细Id</param>
        /// <param name="customizeInvoiceDetailNew">新开票明细Id</param>
        /// <returns></returns>
        private async Task<List<CustomizeInvoiceCreditPo>> SplitCustomizeInvoiceCredit(
            decimal splitQuantity,
            Guid customizeInvoiceDetailOriginId,
            CustomizeInvoiceDetailPo customizeInvoiceDetailOld,
            CustomizeInvoiceDetailPo customizeInvoiceDetailNew)
        {
            //获取开票申请单详细明细
            var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits
                                                .Where(p => p.CustomizeInvoiceDetailId == customizeInvoiceDetailOriginId)
                                                .OrderBy(p => p.CustomizeInvoiceDetailAmount)
                                                .ToListAsync();
            var originCustomizeInvoiceDetailPo = await _db.CustomizeInvoiceDetail
                                   .Where(p => p.Id == customizeInvoiceDetailOriginId)
                                   .AsNoTracking()
                                   .FirstOrDefaultAsync();
            var customizeInvoiceCreditNewLst = new List<CustomizeInvoiceCreditPo>();
            //1，全部都是正数
            if (customizeInvoiceCredits.Count() == customizeInvoiceCredits.Count(p => p.CustomizeInvoiceDetailAmount > 0))
            {
                var remainValue = customizeInvoiceDetailNew.Value;
                foreach (var item in customizeInvoiceCredits)
                {
                    var originValue = item.CustomizeInvoiceDetailAmount;
                    item.CustomizeInvoiceDetailId = customizeInvoiceDetailOld.Id;
                    if (remainValue <= 0)
                    {
                        continue;
                    }
                    if (remainValue >= item.CustomizeInvoiceDetailAmount)
                    {
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                        remainValue = remainValue - originValue;
                        continue;
                    }
                    else
                    {
                        var customizeInvoiceCreditNew = item.DeepClone();
                        customizeInvoiceCreditNew.Id = Guid.NewGuid();
                        customizeInvoiceCreditNew.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                        customizeInvoiceCreditNew.CustomizeInvoiceDetailAmount = remainValue;
                        customizeInvoiceCreditNew.Quantity = remainValue / item.Price;
                        item.CustomizeInvoiceDetailAmount = originValue - remainValue;
                        item.Quantity = item.CustomizeInvoiceDetailAmount / item.Price;
                        remainValue = remainValue - originValue;
                        customizeInvoiceCreditNewLst.Add(customizeInvoiceCreditNew);
                    }
                }
            }
            else if (customizeInvoiceCredits.Count() == customizeInvoiceCredits.Count(p => p.CustomizeInvoiceDetailAmount < 0))
            {
                //2,全部都是负数
                var remainValue = Math.Abs(customizeInvoiceDetailNew.Value);
                foreach (var item in customizeInvoiceCredits)
                {
                    var originValue = Math.Abs(item.CustomizeInvoiceDetailAmount);
                    item.CustomizeInvoiceDetailId = customizeInvoiceDetailOld.Id;
                    if (remainValue <= 0)
                    {
                        continue;
                    }
                    if (remainValue >= Math.Abs(item.CustomizeInvoiceDetailAmount))
                    {
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                        remainValue = remainValue - originValue;
                        continue;
                    }
                    else
                    {
                        var customizeInvoiceCreditNew = item.DeepClone();
                        customizeInvoiceCreditNew.Id = Guid.NewGuid();
                        customizeInvoiceCreditNew.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                        customizeInvoiceCreditNew.CustomizeInvoiceDetailAmount = -remainValue;
                        customizeInvoiceCreditNew.Quantity = -Math.Abs(remainValue / item.Price);
                        item.CustomizeInvoiceDetailAmount = -Math.Abs(originValue - remainValue);
                        item.Quantity = -Math.Abs(item.CustomizeInvoiceDetailAmount / item.Price);
                        remainValue = remainValue - originValue;
                        customizeInvoiceCreditNewLst.Add(customizeInvoiceCreditNew);
                    }
                }
            }
            else
            {
                //3,有正数有负数
                if (originCustomizeInvoiceDetailPo.Quantity > 0) //正数明细
                {
                    var remainValue = Math.Abs(customizeInvoiceDetailNew.Value);
                    var lessZoor = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailAmount < 0).ToList();
                    var greatZoor = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailAmount > 0).ToList();
                    foreach (var item in lessZoor)
                    {
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                        remainValue = remainValue + Math.Abs(item.CustomizeInvoiceDetailAmount);
                    }
                    foreach (var item in greatZoor)
                    {
                        var originValue = Math.Abs(item.CustomizeInvoiceDetailAmount);
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailOld.Id;
                        if (remainValue <= 0)
                        {
                            continue;
                        }
                        if (remainValue >= Math.Abs(item.CustomizeInvoiceDetailAmount))
                        {
                            item.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                            remainValue = remainValue - originValue;
                            continue;
                        }
                        else
                        {
                            var customizeInvoiceCreditNew = item.DeepClone();
                            customizeInvoiceCreditNew.Id = Guid.NewGuid();
                            customizeInvoiceCreditNew.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                            customizeInvoiceCreditNew.CustomizeInvoiceDetailAmount = remainValue;
                            customizeInvoiceCreditNew.Quantity = remainValue / item.Price;
                            item.CustomizeInvoiceDetailAmount = originValue - remainValue;
                            item.Quantity = item.CustomizeInvoiceDetailAmount / item.Price;
                            remainValue = remainValue - originValue;
                            customizeInvoiceCreditNewLst.Add(customizeInvoiceCreditNew);
                        }
                    }
                }
                else
                {
                    //负数明细
                    var remainValue = Math.Abs(customizeInvoiceDetailNew.Value);
                    var lessZoor = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailAmount < 0).ToList();
                    var greatZoor = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailAmount > 0).ToList();
                    foreach (var item in greatZoor)
                    {
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                        remainValue = remainValue + Math.Abs(item.CustomizeInvoiceDetailAmount);
                    }

                    foreach (var item in lessZoor)
                    {
                        var originValue = Math.Abs(item.CustomizeInvoiceDetailAmount);
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailOld.Id;
                        if (remainValue <= 0)
                        {
                            continue;
                        }
                        if (remainValue >= Math.Abs(item.CustomizeInvoiceDetailAmount))
                        {
                            item.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                            remainValue = remainValue - originValue;
                            continue;
                        }
                        else
                        {
                            var customizeInvoiceCreditNew = item.DeepClone();
                            customizeInvoiceCreditNew.Id = Guid.NewGuid();
                            customizeInvoiceCreditNew.CustomizeInvoiceDetailId = customizeInvoiceDetailNew.Id;
                            customizeInvoiceCreditNew.CustomizeInvoiceDetailAmount = -remainValue;
                            customizeInvoiceCreditNew.Quantity = -Math.Abs(remainValue / item.Price);
                            item.CustomizeInvoiceDetailAmount = -Math.Abs(originValue - remainValue);
                            item.Quantity = -Math.Abs(item.CustomizeInvoiceDetailAmount / item.Price);
                            remainValue = remainValue - originValue;
                            customizeInvoiceCreditNewLst.Add(customizeInvoiceCreditNew);
                        }
                    }
                }
            }
            return customizeInvoiceCreditNewLst;
        }


        /// <summary>
        /// 拆分开票明细（部分红冲）
        /// </summary>
        /// <param name="splitQuantity">创建新拆分数量</param>
        /// <param name="customizeInvoiceDetailOriginId">原始开票明细Id</param>
        /// <param name="customizeInvoiceDetailNewId">新开票明细Id</param>
        /// <returns></returns>
        private async Task<List<CustomizeInvoiceCreditPo>> SplitCustomizeInvoiceCredit(
            decimal splitQuantity,
            Guid customizeInvoiceDetailOriginId,
            Guid customizeInvoiceDetailNewId)
        {
            //获取开票申请单详细明细
            var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits
                                                .Where(p => p.CustomizeInvoiceDetailId == customizeInvoiceDetailOriginId)
                                                .OrderBy(p => Math.Abs(p.Quantity))
                                                .AsNoTracking()
                                                .ToListAsync();
            var originCustomizeInvoiceDetailPo = await _db.CustomizeInvoiceDetail
                                   .Where(p => p.Id == customizeInvoiceDetailOriginId)
                                   .AsNoTracking()
                                   .FirstOrDefaultAsync();
            var customizeInvoiceCreditNewLst = new List<CustomizeInvoiceCreditPo>();
            //1，全部都是正数
            if (customizeInvoiceCredits.Count() == customizeInvoiceCredits.Count(p => p.Quantity > 0))
            {
                var remainQuanity = splitQuantity;
                foreach (var item in customizeInvoiceCredits)
                {
                    var originQuantity = item.Quantity;
                    if (remainQuanity <= 0)
                    {
                        continue;
                    }
                    if (remainQuanity > item.Quantity)
                    {
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                        remainQuanity = remainQuanity - originQuantity;
                        continue;
                    }
                    else
                    {
                        item.Quantity = remainQuanity;
                        item.CustomizeInvoiceDetailAmount = remainQuanity * item.Price;
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                        remainQuanity = remainQuanity - originQuantity;
                    }
                }
            }
            else if (customizeInvoiceCredits.Count() == customizeInvoiceCredits.Count(p => p.Quantity < 0))
            {
                //2,全部都是负数
                var remainQuanity = Math.Abs(splitQuantity);
                foreach (var item in customizeInvoiceCredits)
                {
                    var originQuantity = Math.Abs(item.Quantity);
                    if (remainQuanity <= 0)
                    {
                        continue;
                    }
                    if (remainQuanity > Math.Abs(item.Quantity))
                    {
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                        remainQuanity = remainQuanity - originQuantity;
                        continue;
                    }
                    else
                    {
                        item.Quantity = -remainQuanity;
                        item.CustomizeInvoiceDetailAmount = -(remainQuanity * item.Price);
                        item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                        remainQuanity = remainQuanity - originQuantity;
                    }
                }
            }
            else
            {
                //3,有正数有负数
                if (originCustomizeInvoiceDetailPo.Quantity > 0) //正数明细
                {
                    var remainQuanity = splitQuantity;
                    foreach (var item in customizeInvoiceCredits)
                    {
                        var originQuantity = Math.Abs(item.Quantity);
                        if (remainQuanity <= 0)
                        {
                            continue;
                        }
                        if (item.Quantity < 0) //先拆负数
                        {
                            item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                            remainQuanity = originQuantity + remainQuanity;
                            continue;
                        }
                        if (remainQuanity > Math.Abs(item.Quantity))
                        {
                            item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                            remainQuanity = remainQuanity - originQuantity;
                            continue;
                        }
                        else
                        {
                            item.Quantity = remainQuanity;
                            item.CustomizeInvoiceDetailAmount = (remainQuanity * item.Price);
                            item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                            remainQuanity = remainQuanity - originQuantity;
                        }
                    }
                }
                else
                {
                    //负数明细
                    var remainQuanity = Math.Abs(splitQuantity);
                    foreach (var item in customizeInvoiceCredits)
                    {
                        var originQuantity = Math.Abs(item.Quantity);
                        if (remainQuanity <= 0)
                        {
                            continue;
                        }
                        if (item.Quantity > 0) //先拆正数
                        {
                            item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                            remainQuanity = remainQuanity + originQuantity;
                            continue;
                        }
                        if (remainQuanity > Math.Abs(item.Quantity))
                        {
                            item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                            remainQuanity = remainQuanity - originQuantity;
                            continue;
                        }
                        else
                        {
                            item.Quantity = -remainQuanity;
                            item.CustomizeInvoiceDetailAmount = -(remainQuanity * item.Price);
                            item.CustomizeInvoiceDetailId = customizeInvoiceDetailNewId;
                            remainQuanity = remainQuanity - originQuantity;
                        }
                    }
                }
            }
            customizeInvoiceCreditNewLst = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == customizeInvoiceDetailNewId).ToList();

            return customizeInvoiceCreditNewLst;
        }

        /// <summary>
        /// 删除开票分类单列
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Obsolete]
        public async Task<BaseResponseData<int>> DeleteCustomizeInvoiceClassifyOld(CustomizeInvoiceClassifyInput input)
        {
            // 要删除的分类单集合
            var delCic = new List<CustomizeInvoiceClassifyPo>();
            var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == input.classifyOutput.Id && p.Status == CustomizeInvoiceStatusEnum.WaitSubmit).AsNoTracking().FirstOrDefaultAsync();
            if (customizeInvoiceClassify != null)
            {
                if (customizeInvoiceClassify.Classify == CustomizeInvoiceClassifyEnum.Credit && !string.IsNullOrEmpty(customizeInvoiceClassify.RelationCode))
                {
                    // 删除拆分的另一个分类
                    var other = await _db.CustomizeInvoiceClassify.Where(p => p.BillCode == customizeInvoiceClassify.RelationCode).AsNoTracking().FirstOrDefaultAsync();
                    if (other != null)
                    {
                        if (other.Status == CustomizeInvoiceStatusEnum.WaitSubmit)
                        {
                            delCic.Add(other);
                            _db.CustomizeInvoiceClassify.Remove(other);
                        }
                        else
                        {
                            return BaseResponseData<int>.Failed(500, "操作失败，拆分的另一个开票分类状态不为待提交，无法删除！");
                        }
                    }
                    var another = await _db.CustomizeInvoiceClassify.Where(p => p.RelationCode == customizeInvoiceClassify.RelationCode).AsNoTracking().ToListAsync();
                    if (another != null && another.Any())
                    {
                        foreach (var item in another)
                        {
                            if (item.Status == CustomizeInvoiceStatusEnum.WaitSubmit)
                            {
                                delCic.Add(item);
                                _db.CustomizeInvoiceClassify.Remove(item);
                            }
                            else
                            {
                                return BaseResponseData<int>.Failed(500, "操作失败，拆分的另一个开票分类状态不为待提交，无法删除！");
                            }
                        }
                    }
                }
                else
                {
                    var another = await _db.CustomizeInvoiceClassify.Where(p => p.RelationCode == customizeInvoiceClassify.BillCode).AsNoTracking().ToListAsync();
                    if (another != null && another.Any())
                    {
                        foreach (var item in another)
                        {
                            if (item.Status == CustomizeInvoiceStatusEnum.WaitSubmit)
                            {
                                delCic.Add(item);
                                _db.CustomizeInvoiceClassify.Remove(item);
                            }
                            else
                            {
                                return BaseResponseData<int>.Failed(500, "操作失败，拆分的另一个开票分类状态不为待提交，无法删除！");
                            }
                        }
                    }
                }
                _db.CustomizeInvoiceClassify.Remove(customizeInvoiceClassify);
                delCic.Add(customizeInvoiceClassify);
                // 要删除的id集合
                var delCicIds = delCic.Select(x => x.Id).ToHashSet();
                var customizeInvoiceItems = await _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId.HasValue && delCicIds.Contains(p.CustomizeInvoiceClassifyId.Value)).AsNoTracking().ToListAsync();
                var customizeInvoices_updates = new List<CustomizeInvoiceItemPo>();
                var creditBillCodes = new List<string>();
                var removeInvoiceRedDetailPos = new List<CustomizeInvoiceRedDetailPo>();
                if (customizeInvoiceItems != null && customizeInvoiceItems.Any())
                {
                    _db.CustomizeInvoiceItem.RemoveRange(customizeInvoiceItems);
                    foreach (var customizeInvoiceItem in customizeInvoiceItems)
                    {
                        if (!string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                        {
                            var invoiceItemOld = await _db.CustomizeInvoiceItem.Where(c => customizeInvoiceItem.RelationCode.Equals(c.Code)).AsNoTracking().FirstOrDefaultAsync();
                            if (invoiceItemOld != null)
                            {
                                if (invoiceItemOld.ChangedStatus.HasValue)
                                {
                                    invoiceItemOld.ChangedStatus = null;
                                    customizeInvoices_updates.Add(invoiceItemOld);
                                }
                            }
                            var invoiceRedDetailPos = await _db.CustomizeInvoiceRedDetail.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                            if (invoiceRedDetailPos != null && invoiceRedDetailPos.Any())
                            {
                                removeInvoiceRedDetailPos.AddRange(invoiceRedDetailPos);
                            }
                        }
                        var customizeInvoiceDetails = await _db.CustomizeInvoiceDetail.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                        if (customizeInvoiceDetails != null && customizeInvoiceDetails.Any())
                        {
                            _db.CustomizeInvoiceDetail.RemoveRange(customizeInvoiceDetails);
                        }
                        var customizeInvoiceSubDetails = await _db.CustomizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                        var originDetailIds = customizeInvoiceSubDetails.Select(p => p.OriginDetailId).ToList();
                        if (customizeInvoiceSubDetails != null && customizeInvoiceSubDetails.Any())
                        {
                            _db.CustomizeInvoiceSubDetails.RemoveRange(customizeInvoiceSubDetails);
                            creditBillCodes.AddRange(customizeInvoiceSubDetails.Select(p => p.CreditBillCode).ToList());
                        }
                    }
                }


                if (customizeInvoiceClassify.Classify == CustomizeInvoiceClassifyEnum.Pre)
                {
                    var preCustomize = await _db.PreCustomizeInvoiceItem.FirstOrDefaultAsync(p => p.Code == customizeInvoiceClassify.RelationCode);
                    if (preCustomize != null)
                    {
                        preCustomize.Status = PreCustomizeInvoiceItemStatusEnum.waitSubmit;
                    }
                }
                if (customizeInvoices_updates.Any())
                {
                    customizeInvoices_updates = customizeInvoices_updates.DistinctBy(p => p.Code).ToList();
                    _db.CustomizeInvoiceItem.UpdateRange(customizeInvoices_updates);
                }
                if (creditBillCodes.Any())
                {
                    creditBillCodes = creditBillCodes.Distinct().ToList();
                    var credits = await _db.Credits.Where(p => creditBillCodes.Contains(p.BillCode)).ToListAsync();
                    foreach (var credit in credits)
                    {
                        credit.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                    }
                    _db.Credits.UpdateRange(credits);
                    var invoiceRedDetailPos = await _db.CustomizeInvoiceRedDetail.Where(p => creditBillCodes.Contains(p.CreditBillCode)).ToListAsync();
                    foreach (var item in invoiceRedDetailPos)
                    {
                        item.IsInvoiced = false;
                    }
                }
                if (removeInvoiceRedDetailPos.Any())
                {
                    _db.CustomizeInvoiceRedDetail.RemoveRange(removeInvoiceRedDetailPos);
                }
                await _unitOfWork.CommitAsync();
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "操作失败，没有找到该数据！");
            }
            return BaseResponseData<int>.Success("操作成功");
        }
        /// <summary>
        /// 删除开票分类单列
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> DeleteCustomizeInvoiceClassify(CustomizeInvoiceClassifyInput input)
        {
            var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == input.classifyOutput.Id && p.Status == CustomizeInvoiceStatusEnum.WaitSubmit).AsNoTracking().FirstOrDefaultAsync();
            if (customizeInvoiceClassify != null)
            {
                _db.CustomizeInvoiceClassify.Remove(customizeInvoiceClassify);

                var customizeInvoiceItems = await _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId.HasValue &&
                                                                                     p.CustomizeInvoiceClassifyId == customizeInvoiceClassify.Id).AsNoTracking().ToListAsync();

                var customizeInvoices_updates = new List<CustomizeInvoiceItemPo>();
                var creditIds = new List<Guid>();
                var removeInvoiceRedDetailPos = new List<CustomizeInvoiceRedDetailPo>();

                if (customizeInvoiceItems != null && customizeInvoiceItems.Any())
                {
                    _db.CustomizeInvoiceItem.RemoveRange(customizeInvoiceItems);
                    foreach (var customizeInvoiceItem in customizeInvoiceItems)
                    {
                        if (!string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                        {
                            var invoiceItemOld = await _db.CustomizeInvoiceItem.Where(c => customizeInvoiceItem.RelationCode.Equals(c.Code)).AsNoTracking().FirstOrDefaultAsync();
                            if (invoiceItemOld != null)
                            {
                                var otherRedCustomizeInvoiceItem = await _db.CustomizeInvoiceItem.Where(p => p.RelationCode == customizeInvoiceItem.RelationCode && p.Id != customizeInvoiceItem.Id && p.Status != CustomizeInvoiceStatusEnum.Cancel).ToListAsync();
                                if (invoiceItemOld.ChangedStatus.HasValue)
                                {
                                    if (otherRedCustomizeInvoiceItem.Any())
                                    {
                                        invoiceItemOld.ChangedStatus = CustomizeInvoiceChangedStatusEnum.PartRedOffset;
                                    }
                                    else
                                    {
                                        invoiceItemOld.ChangedStatus = null;
                                    }
                                    customizeInvoices_updates.Add(invoiceItemOld);
                                }
                            }
                            var invoiceRedDetailPos = await _db.CustomizeInvoiceRedDetail.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                            if (invoiceRedDetailPos != null && invoiceRedDetailPos.Any())
                            {
                                removeInvoiceRedDetailPos.AddRange(invoiceRedDetailPos);
                            }
                        }
                        var customizeInvoiceDetails = await _db.CustomizeInvoiceDetail.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                        if (customizeInvoiceDetails != null && customizeInvoiceDetails.Any())
                        {
                            _db.CustomizeInvoiceDetail.RemoveRange(customizeInvoiceDetails);
                        }
                        var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                        if (customizeInvoiceCredits != null && customizeInvoiceCredits.Any())
                        {
                            _db.CustomizeInvoiceCredits.RemoveRange(customizeInvoiceCredits);
                        }
                        var creditDetailIds = customizeInvoiceCredits.Select(p => p.CreditDetailId).ToList();
                        var creditDetails = await _db.CreditDetails.Where(p => creditDetailIds.Contains(p.Id)).ToListAsync();
                        if (creditDetails.Any() && string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))//只有删除蓝票才恢复应收明细
                        {
                            foreach (var item in creditDetails)
                            {
                                var invoiceAmount = customizeInvoiceCredits.Where(p => p.CreditDetailId == item.Id).Sum(p => p.CustomizeInvoiceDetailAmount);
                                var totalInvoiceAmount = item.InvoiceAmount.HasValue ? Math.Abs(item.InvoiceAmount.Value) - Math.Abs(invoiceAmount) : Math.Abs(invoiceAmount);
                                if (totalInvoiceAmount < 0)
                                {
                                    totalInvoiceAmount = 0;
                                }
                                if (invoiceAmount == 0 && item.Amount != 0M)
                                {
                                    totalInvoiceAmount = 0;
                                }
                                item.InvoiceAmount = item.Amount.Value > 0 ? totalInvoiceAmount : -totalInvoiceAmount;
                                item.NoInvoiceAmount = item.Amount > 0 ? Math.Abs(item.Amount.Value) - Math.Abs(item.InvoiceAmount.Value) : -(Math.Abs(item.Amount.Value) - Math.Abs(item.InvoiceAmount.Value));

                            }
                            var creditIdTemps = creditDetails.Select(p => p.CreditId).ToList();
                            creditIds.AddRange(creditIdTemps);
                        }
                    }
                }

                if (customizeInvoiceClassify.Classify == CustomizeInvoiceClassifyEnum.Pre)
                {
                    var preCustomize = await _db.PreCustomizeInvoiceItem.FirstOrDefaultAsync(p => p.Code == customizeInvoiceClassify.RelationCode);
                    if (preCustomize != null)
                    {
                        preCustomize.Status = PreCustomizeInvoiceItemStatusEnum.waitSubmit;
                    }
                }
                if (customizeInvoices_updates.Any())
                {
                    customizeInvoices_updates = customizeInvoices_updates.DistinctBy(p => p.Code).ToList();
                    _db.CustomizeInvoiceItem.UpdateRange(customizeInvoices_updates);
                }
                if (creditIds.Any())
                {
                    creditIds = creditIds.Distinct().ToList();
                    var credits = await _db.Credits.Where(p => creditIds.ToHashSet().Contains(p.Id)).AsNoTracking().ToListAsync();
                    foreach (var credit in credits)
                    {
                        credit.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                    }
                    _db.Credits.UpdateRange(credits);
                    var creditBillCodes = credits.Select(p => p.BillCode).ToList();
                    var invoiceRedDetailPos = await _db.CustomizeInvoiceRedDetail.Where(p => creditBillCodes.Contains(p.CreditBillCode)).ToListAsync();
                    foreach (var item in invoiceRedDetailPos)
                    {
                        item.IsInvoiced = false;
                    }
                }
                if (removeInvoiceRedDetailPos.Any())
                {
                    _db.CustomizeInvoiceRedDetail.RemoveRange(removeInvoiceRedDetailPos);
                }
                if (!string.IsNullOrEmpty(customizeInvoiceClassify.OARequestId))
                {
                    await _weaverApiClient.DelWorkFlow(customizeInvoiceClassify.CreatedBy, Convert.ToInt32(customizeInvoiceClassify.OARequestId));
                }
                await _unitOfWork.CommitAsync();
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "操作失败，没有找到该数据！");
            }
            return BaseResponseData<int>.Success("操作成功");
        }

        /// <summary>
        /// 提交前提示
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<customerInvoice>>> BeforeSubmitClassfiy(CustomizeInvoiceClassifyInput input)
        {
            var ret = BaseResponseData<List<customerInvoice>>.Success("查询成功");
            if (input.CreditSaleSubType.HasValue && input.CreditSaleSubType.Value == CreditSaleSubTypeEnum.personal)
            {
                // TODO zfl:旺店通数据,从集成中心获取 
                return ret;
            }
            else
            {
                var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    id = input.classifyOutput.CustomerId.ToString()
                });
                if (customer != null)
                {
                    var clas = _db.CustomizeInvoiceClassify.FirstOrDefault(x => x.BillCode == input.classifyOutput.BillCode);
                    var item = _db.CustomizeInvoiceItem.FirstOrDefault(x => x.CustomizeInvoiceClassifyId == (clas != null ? clas.Id : Guid.Empty));
                    var invoices = customer.customerInvoices.ToList(); //FirstOrDefault(p => p.isInvoiceUnit == 1)
                    foreach (var invoice in invoices)
                    {
                        invoice.customerId = customer.customerId;
                    }
                    ret.Data = invoices;
                }
                return ret;
            }

        }

        /// <summary>
        /// 按分类提交开票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SubmitByClassfiy(CustomizeInvoiceClassifyInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功");
            // 选择开票主体
            string customerInvoice = input.CustomerInvoice != null ? JsonConvert.SerializeObject(input.CustomerInvoice) : string.Empty;
            var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == input.classifyOutput.Id && p.Status == CustomizeInvoiceStatusEnum.WaitSubmit).AsNoTracking().FirstOrDefaultAsync();
            if (customizeInvoiceClassify == null)
            {
                ret = BaseResponseData<string>.Failed(500, "操作失败，原因：没有找到分类数据。");
                return ret;
            }
            if (input.CustomerInvoice != null && string.IsNullOrEmpty(input.CustomerInvoice.salesInvoiceDetails))
            {
                ret = BaseResponseData<string>.Failed(500, "操作失败，原因：开票类型为空。");
                return ret;
            }
            if (customizeInvoiceClassify.CreditSaleSubType != CreditSaleSubTypeEnum.personal && Guid.Parse(input.CustomerInvoice.customerId) != customizeInvoiceClassify.CustomerId)
            {
                ret = BaseResponseData<string>.Failed(500, "操作失败，原因：您选择的开票主体有误，请刷新后操作。");
                return ret;
            }
            input.CreateBy = customizeInvoiceClassify.CreatedBy;
            var customizeInvoiceItems = await _db.CustomizeInvoiceItem.
                                        Include(p => p.CustomizeInvoiceDetail).
                                        Where(p => p.CustomizeInvoiceClassifyId == input.classifyOutput.Id).
                                        AsNoTracking().ToListAsync();

            // 检查分类下开票类型是否一致
            if (customizeInvoiceItems != null && customizeInvoiceItems.Any())
            {
                var status = customizeInvoiceItems.FirstOrDefault().InvoiceType;
                foreach (var item in customizeInvoiceItems)
                {
                    if (item.InvoiceType != status)
                    {
                        ret = BaseResponseData<string>.Failed(500, "操作失败，原因：同一个开票分类下开票类型不一致。");
                        return ret;
                    }
                }
            }
            var checkSettlementList = await _bDSApiClient.GetDataDictionaryListByType("CheckSettlementList");
            if (checkSettlementList != null && checkSettlementList.Any())
            {
                var companyIds = checkSettlementList.Where(p => !string.IsNullOrEmpty(p.Attribute)).Select(p => Guid.Parse(p.Attribute)).ToList();
                if (companyIds.Contains(customizeInvoiceClassify.CompanyId) && string.IsNullOrEmpty(customizeInvoiceClassify.AttachFileIds))
                {
                    ret = BaseResponseData<string>.Failed(500, "操作失败，原因：请上传结算清单后提交。");
                    return ret;
                }
            }
            int count = 0;
            string msg = string.Empty;
            if (customizeInvoiceItems.Count == 1 && customizeInvoiceItems.Single().IsNoRedConfirm == 1)
            {
                (count, msg) = await SubmitToKingdeeForRedAsync(customizeInvoiceItems, input.CreateBy, input.IsPushDefaultEmail, "N", input.CustomerEmail, customerInvoice); //flow=="N" 只做检查
            }
            else
            {
                (count, msg) = await SubmitToKingdeeAsync(customizeInvoiceItems, input.CreateBy, input.IsPushDefaultEmail, "N", input.CustomerEmail, customerInvoice); //flow=="N" 只做检查
            }
            if (count <= 0)
            {
                ret = BaseResponseData<string>.Failed(500, msg);
                return ret;
            }
            var redStr = customizeInvoiceItems.Count(p => p.InvoiceTotalAmount < 0) > 0 ? "【红票】" : "";

            var company = await _bDSApiClient.GetCompanyMetaInfosAsync(new CompetenceCenter.BDSCenter.Inputs.CompanyMetaInfosInput
            {
                nameCodeEq = customizeInvoiceItems[0].NameCode
            });
            var isTreasurerAudit = 0;//是否会计审核
            var isConsistentBySaleSystem = 1; //是否与销售子系统一致(1:一致，0:不一致)
            var saleCustomerProducts = new List<CustomerProductBillingOutput>();
            var customizeInvoiceDetails = customizeInvoiceItems.SelectMany(p => p.CustomizeInvoiceDetail).ToList();
            List<Guid>? productIds = customizeInvoiceDetails.Where(x => x.ProductId.HasValue).Select(x => x.ProductId.Value).ToList();
            if (productIds != null && productIds.Any())
            {
                var customizeInvoiceItem = customizeInvoiceItems.FirstOrDefault();
                var selectProductInvoicesInputData = new List<SelectProductInvoicesInputData>();
                foreach (var productId in productIds)
                {
                    selectProductInvoicesInputData.Add(new SelectProductInvoicesInputData
                    {
                        ProductId = productId,
                        CustomerId = !string.IsNullOrEmpty(customizeInvoiceItem.CustomerId) ? Guid.Parse(customizeInvoiceItem.CustomerId) : Guid.Empty,
                    });
                }
                var selectProductInvoicesOutput = await _bDSApiClient.SelectProductInvoices(new SelectProductInvoicesInput
                {
                    CompanyId = customizeInvoiceItem.CompanyId,
                    List = selectProductInvoicesInputData
                });
                if (selectProductInvoicesOutput != null && selectProductInvoicesOutput.Any())
                {
                    foreach (var scProduct in selectProductInvoicesOutput)
                    {
                        var customizeInvoiceDetail = customizeInvoiceDetails.FirstOrDefault(x => x.ProductId == scProduct.ProductId);
                        //判断开票名称与销售子系统的是一致还是不一致
                        if (customizeInvoiceDetail != null && customizeInvoiceDetail.ProductName != scProduct.InvoiceName)
                        {
                            isConsistentBySaleSystem = 0;
                            break;
                        }
                    }
                }
                else
                {
                    isConsistentBySaleSystem = 0;
                }
            }
            if (customizeInvoiceItems.Count(p => p.InvoiceTotalAmount < 0) > 0) //申开单是红票
            {
                isTreasurerAudit = 1;
            }
            else if (customizeInvoiceDetails.Count(p => p.OriginProductName != p.ProductName) > 0) //开票名称制作开票和提交开票时改过
            {
                isTreasurerAudit = 1;
            }
            else if (customizeInvoiceDetails.Count(p => p.ProductNo != p.Specification) > 0) //规格制作开票和提交开票时改过
            {
                isTreasurerAudit = 1;
            }
            if (string.IsNullOrEmpty(company.First().taxRate))
            {
                isTreasurerAudit = 1;
            }
            else
            {
                var taxRate = decimal.Parse(company.First().taxRate);
                if (customizeInvoiceDetails.Count(p => p.TaxRate == taxRate) != customizeInvoiceDetails.Count())//税率在销售订单改过（与公司税率不同）
                {
                    isTreasurerAudit = 1;
                }
            }
            //OA审核
            if (isTreasurerAudit == 1) //需要审核的就走OA
            {
                var oaInput = new Gateway.Common.WeaverOA.WeaverInput
                {
                    BaseInfo = new Gateway.Common.WeaverOA.BaseInfo
                    {
                        Operator = input.CreateBy,
                        RequestName = $"【财务-运营开票】{redStr}-[{input.classifyOutput.BillCode}]-{customizeInvoiceClassify.CompanyName}-{customizeInvoiceClassify.CustomerName}",
                    },
                    MainData = new Gateway.Common.WeaverOA.MainData
                    {
                        FCreatorID = input.CreateBy,
                        Iframe_link = $"{_configuration["BaseUri"]}/fam/financeManagement/CustomizeInvoiceSubmitOA?customizeInvoiceClassifyId={customizeInvoiceItems[0].CustomizeInvoiceClassifyId}", //PC的Iframe地址,
                        Height_m = 480,
                        Iframe_link_m = $"{_configuration["BaseUri"].Replace("/v1", "")}/oamobile/#/finance/invoicingEmbedPage?customizeInvoiceClassifyId={customizeInvoiceItems[0].CustomizeInvoiceClassifyId}",
                        CpDepartment = "",
                        CPcompanyCode = customizeInvoiceItems[0].NameCode,
                        Condition = DateTime.Now.ToString("yyyy-MM-dd hh:mm:ssss"),
                        Business_id = customizeInvoiceItems[0].CustomizeInvoiceClassifyId.ToString(),
                        IsTreasurerAudit = isTreasurerAudit,
                        IsConsistentBySaleSystem = isConsistentBySaleSystem
                    },
                    OtherParams = new Gateway.Common.WeaverOA.OtherParams
                    {
                        IsNextFlow = 1,
                    }
                };
                if (string.IsNullOrEmpty(customizeInvoiceClassify.OARequestId))
                {
                    var oaRet = await _weaverApiClient.CreateWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.CustomizeInvoiceForm);
                    if (!oaRet.Status)
                    {
                        ret = BaseResponseData<string>.Failed(500, oaRet.Msg);
                        return ret;
                    }
                    customizeInvoiceClassify.OARequestId = oaRet.Data.Requestid.ToString();
                }
                else
                {
                    oaInput.BaseInfo.RequestId = int.Parse(customizeInvoiceClassify.OARequestId);
                    var oaRet = await _weaverApiClient.SubmitWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.CustomizeInvoiceForm);
                    if (!oaRet.Status)
                    {
                        ret = BaseResponseData<string>.Failed(500, oaRet.Msg);
                        return ret;
                    }
                }
            }
            else //免审直接提交
            {
                if (customizeInvoiceItems.Count == 1 && customizeInvoiceItems.Single().IsNoRedConfirm == 1)
                {

                    (count, msg) = await SubmitToKingdeeForRedAsync(customizeInvoiceItems, input.CreateBy, input.IsPushDefaultEmail, "Y", input.CustomerEmail, customerInvoice); //flow=="N" 只做检查
                }
                else
                {
                    (count, msg) = await SubmitToKingdeeAsync(customizeInvoiceItems, input.CreateBy, input.IsPushDefaultEmail, "Y", input.CustomerEmail, customerInvoice); //flow=="N" 只做检查
                }
            }
            // 保存选择的开票主体
            customizeInvoiceClassify.Invoiceofclassfiy = customerInvoice;
            customizeInvoiceClassify.IsPushDefaultEmail = input.IsPushDefaultEmail;
            // 保存客户邮箱
            customizeInvoiceClassify.CustomerEmail = input.CustomerEmail;
            if (isTreasurerAudit == 0)
            {
                customizeInvoiceClassify.Status = CustomizeInvoiceStatusEnum.WaitInvoice;
            }
            else
            {
                customizeInvoiceClassify.Status = CustomizeInvoiceStatusEnum.Auditing;
            }
            foreach (var item in customizeInvoiceItems)
            {
                if (isTreasurerAudit == 0)
                {
                    item.Status = CustomizeInvoiceStatusEnum.WaitInvoice;
                    item.IsPush = true;
                }
                else
                {
                    item.Status = CustomizeInvoiceStatusEnum.Auditing;
                }
                item.OARequestId = customizeInvoiceClassify.OARequestId;
            }
            _db.CustomizeInvoiceClassify.Update(customizeInvoiceClassify);
            _db.CustomizeInvoiceItem.UpdateRange(customizeInvoiceItems);
            await _unitOfWork.CommitAsync();
            return ret;
        }

        private async Task<(int, string)> SubmitToKingdeeAsync(
            List<CustomizeInvoiceItemPo> customizeInvoiceItems,
            string createBy,
            bool? isPushDefaultEmail,
            string flow = "Y",
            string customerEmail = "",
            string customerInvoice = "",
            bool useCustomerDefaultEmail = false
            )
        {
            // 邮箱字符串处理
            if (!string.IsNullOrEmpty(customerEmail))
            {
                // 替换逗号
                customerEmail = customerEmail.Replace(",", ";");
                // 去尾
                if (customerEmail[customerEmail.Length - 1] == ';')
                {
                    customerEmail = customerEmail.Substring(0, customerEmail.Length - 1);
                }
                // 掐头
                if (customerEmail[0] == ';')
                {
                    customerEmail = customerEmail.Substring(1, customerEmail.Length - 1);
                }
            }
            List<PushCustomizeInvoiceSaveInput> inputs = new List<PushCustomizeInvoiceSaveInput>();
            var classifyPo = await _db.CustomizeInvoiceClassify.Where(p => p.Id == customizeInvoiceItems[0].CustomizeInvoiceClassifyId).AsNoTracking().FirstAsync();
            foreach (var customizeInvoiceItem in customizeInvoiceItems)
            {
                //负数发票校验
                if (customizeInvoiceItem.InvoiceTotalAmount < 0)
                {
                    if (!customizeInvoiceItem.RedOffsetReason.HasValue)
                        return (0, "操作失败，原因：冲红原因必填，请检查！");
                    var noRequiredType = new List<InvoiceTypeEnum> {
                            InvoiceTypeEnum.DZUniversal,
                            InvoiceTypeEnum.RollTicket,
                            InvoiceTypeEnum.ZZUniversal
                    };
                    if (!noRequiredType.Contains(customizeInvoiceItem.InvoiceType))
                    {
                        if (customizeInvoiceItem.RedOffsetReason == 4 && string.IsNullOrEmpty(customizeInvoiceItem.RedOffsetCode))
                            return (0, "操作失败，原因：销售折让红字信息表编号必填");
                        if (customizeInvoiceItem.RedOffsetOpter == 3 && string.IsNullOrEmpty(customizeInvoiceItem.RedOffsetCode))
                            return (0, "操作失败，原因：购方申请-已抵扣红字信息表编号必填");
                    }
                    var digitalCircuitType = new List<InvoiceTypeEnum> {
                        InvoiceTypeEnum.DigitalCircuitUniversal,
                        InvoiceTypeEnum.DigitalCircuitTiket
                    };

                    if (string.IsNullOrEmpty(customizeInvoiceItem.RedOffsetCode) && digitalCircuitType.Contains(customizeInvoiceItem.InvoiceType))
                        return (0, "操作失败，原因：数电票红字信息表编号必填");
                }
                if (!string.IsNullOrEmpty(customizeInvoiceItem.RelationCode) &&
                    (customizeInvoiceItem.InvoiceType == InvoiceTypeEnum.DigitalCircuitTiket || customizeInvoiceItem.InvoiceType == InvoiceTypeEnum.DigitalCircuitUniversal))
                {
                    customizeInvoiceItem.RedOffsetOpter = null;
                }

                var details = new List<OriginalBillEntryItem>(); //商品详情
                var originalDetails = new List<originalBillArEntryItem>(); //应收明细
                var customizeInvoiceSubs = new List<CustomizeInvoiceSubDetailPo>();

                var sortDetailLst = customizeInvoiceItem.CustomizeInvoiceDetail.OrderBy(z => z.Sort).ThenByDescending(z => z.CreatedTime).ThenByDescending(p => p.Price * p.Quantity);
                if (string.IsNullOrEmpty(customizeInvoiceItem.RedOffsetCode))
                {
                    foreach (var d in sortDetailLst)
                    {
                        var taxRate = d.TaxRate / 100.00M;
                        var detail = new OriginalBillEntryItem
                        {
                            gift = false,
                            taxrate = taxRate,
                            num = d.Price < 0 ? d.Quantity * -1 : d.Quantity,
                            specification = d.Specification,
                            goodsname = d.ProductName,
                            taxamount = d.Value,
                            remark = "",
                            unitprice = decimal.Parse(Math.Abs(d.Price / (1 + taxRate)).ToString("F4")),
                            taxunitprice = Math.Abs(decimal.Parse(d.Price.ToString("F4"))),
                            taxratecodeid = d.TaxTypeNo,
                            policylogo = "0",
                            unit = d.PackUnit,
                            rowtype = d.Tag == "折扣行" ? "1" : "2",
                        };
                        if (!string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                        {
                            detail.rowtype = "2";
                        }
                        detail.tax = Math.Abs(decimal.Parse(detail.taxunitprice.ToString("F2"))) - Math.Abs(detail.unitprice);
                        details.Add(detail);
                    }
                }
                else //红字编号合并一行
                {
                    var detail = new OriginalBillEntryItem
                    {
                        gift = false,
                        taxrate = sortDetailLst.First().TaxRate / 100,
                        num = 1,
                        specification = sortDetailLst.First().Specification,
                        goodsname = sortDetailLst.First().ProductName,
                        taxamount = sortDetailLst.Sum(p => p.Value),
                        remark = "",
                        taxratecodeid = sortDetailLst.First().TaxTypeNo,
                        policylogo = "0",
                        unit = sortDetailLst.First().PackUnit,
                        rowtype = "2",
                    };
                    var tax = decimal.Parse((Math.Abs(detail.taxamount) - Math.Abs(detail.taxamount / (1 + detail.taxrate))).ToString("F2"));

                    detail.taxunitprice = detail.taxamount;
                    detail.taxunitprice = Math.Round(detail.taxunitprice, 4);
                    detail.tax = detail.taxamount > 0 && sortDetailLst.Sum(p => p.TaxAmount) > 0 ? tax : -tax;
                    detail.unitprice = detail.taxamount > 0 ? detail.taxamount - Math.Abs(detail.tax) : -(Math.Abs(detail.taxamount) - Math.Abs(detail.tax));
                    details.Add(detail);
                }
                var creditCodes = customizeInvoiceItem.CustomizeInvoiceDetail.Select(p => p.CreditBillCode).Distinct().ToList();
                var creditCodeStr = string.Join(',', creditCodes);
                var creditCodeArry = creditCodeStr.Split(',', StringSplitOptions.RemoveEmptyEntries).Distinct().ToHashSet();
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                if (customizeInvoiceCredits.Any()) //如果开票明细关系表中应收单号
                {
                    creditCodeArry = customizeInvoiceCredits.Select(p => p.CreditCode).Distinct().ToHashSet();
                }
                var getContractDelayInfoListInput = new GetContractDelayInfoListInput();
                var credits = await _db.Credits.Include(p => p.CreditDetails).Where(p => creditCodeArry.Contains(p.BillCode)).AsNoTracking().ToListAsync();
                var creditAllGroupArry = credits.GroupBy(p => p.CustomerId).ToList();

                foreach (var creditAllGroup in creditAllGroupArry)
                {
                    getContractDelayInfoListInput.ContractDelayList.Add(new ContractDelayList
                    {
                        CustomerId = creditAllGroup.Key.Value,
                        ProjectIds = creditAllGroup.ToList().Where(p => p.ProjectId.HasValue).Select(p => p.ProjectId).Distinct().ToList()
                    });
                }
                getContractDelayInfoListInput.CompanyId = customizeInvoiceItem.CompanyId;
                if (getContractDelayInfoListInput.ContractDelayList.Count() > 0)
                {
                    var pmret = await _projectApiExcuteClient.GetContractDelayInfoList(getContractDelayInfoListInput);
                    if (!pmret.Data)
                    {
                        return (0, $"操作失败，原因：{pmret.Message}项目协议超期未回签，请尽快完成项目协议回签任务！");
                    }
                }

                #region 查询应收分录

                if (customizeInvoiceCredits.Any())
                {
                    foreach (var creditCode in creditCodeArry)
                    {
                        var credit = credits.Where(p => p.BillCode == creditCode).FirstOrDefault();
                        if (credit != null)
                        {
                            var amount = decimal.Parse(customizeInvoiceCredits.Where(p => p.CreditCode == creditCode).
                                             Sum(p => p.CustomizeInvoiceDetailAmount).ToString("F2"));
                            var originalBillArEntryItem = new originalBillArEntryItem
                            {
                                jfzx_arbillnum = creditCode,
                                jfzx_syamount = amount,
                                jfzx_bizorg = credit.BusinessDeptId,
                                jfzx_customer = credit.CustomerId.ToString().ToUpper(),
                                jfzx_order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                jfzx_project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                user = createBy
                            };
                            var taxRateDetails = new List<taxRateDetail>();
                            var creditDetailGroup = credit.CreditDetails.GroupBy(p => p.TaxRate);
                            foreach (var g in creditDetailGroup)
                            {
                                var usedAmount = 0m;
                                foreach (var item in g.ToList())
                                {
                                    usedAmount += customizeInvoiceCredits.Where(p => p.CreditDetailId == item.Id).Sum(p => p.CustomizeInvoiceDetailAmount);
                                }
                                taxRateDetails.Add(new taxRateDetail
                                {
                                    taxrate = g.Key.Value,
                                    usedAmount = usedAmount
                                });

                            }
                            originalBillArEntryItem.taxRateDetails = taxRateDetails;
                            originalDetails.Add(originalBillArEntryItem);
                        }
                    }
                }
                else
                {
                    if (classifyPo.Classify != CustomizeInvoiceClassifyEnum.Pre)
                    {
                        if (string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                        {
                            customizeInvoiceSubs = await _db.CustomizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                        }
                        else
                        {
                            var customize = await _db.CustomizeInvoiceItem.Where(p => p.Code == customizeInvoiceItem.RelationCode).AsNoTracking().FirstOrDefaultAsync();
                            if (customize != null)
                            {
                                customizeInvoiceSubs = await _db.CustomizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId == customize.Id).AsNoTracking().ToListAsync();
                            }
                        }
                        //查询分组下面的申开单
                        var customizes = await _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == customizeInvoiceItem.CustomizeInvoiceClassifyId).AsNoTracking().ToListAsync();
                        var classifyOhter = await _db.CustomizeInvoiceClassify.Where(p => !string.IsNullOrEmpty(p.RelationCode) && p.RelationCode == classifyPo.RelationCode).AsNoTracking().FirstOrDefaultAsync();

                        if (classifyOhter == null)
                        {
                            classifyOhter = await _db.CustomizeInvoiceClassify.Where(p => p.RelationCode == classifyPo.BillCode).AsNoTracking().FirstOrDefaultAsync();
                        }
                        if (customizes.Count > 1 || creditCodeArry.Count() == 1 || !customizeInvoiceSubs.Any() || classifyOhter != null) //拆单过的就直接给申开的的金额
                        {
                            classifyOhter = await _db.CustomizeInvoiceClassify.Where(p => p.RelationCode == classifyPo.BillCode).AsNoTracking().FirstOrDefaultAsync();
                        }
                        if (customizes.Count > 1 || creditCodeArry.Count() == 1 || !customizeInvoiceSubs.Any() || classifyOhter != null) //拆单过的就直接给申开的的金额
                        {
                            foreach (var creditCode in creditCodeArry)
                            {
                                var credit = credits.Where(p => p.BillCode == creditCode).FirstOrDefault();
                                if (credit != null)
                                {
                                    var originalBillArEntry = new originalBillArEntryItem
                                    {
                                        jfzx_arbillnum = credit.BillCode,
                                        jfzx_syamount = customizeInvoiceItem.CustomizeInvoiceDetail.Where(p => p.CreditBillCode.Contains(creditCode)).Sum(p => p.Value),
                                        jfzx_bizorg = credit.BusinessDeptId,
                                        jfzx_customer = credit.CustomerId.ToString().ToUpper(),
                                        jfzx_order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                        jfzx_project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                        user = createBy,

                                    };
                                    if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.InitCredit)
                                    {
                                        var invoiceDetailPos = sortDetailLst.Where(p => p.CreditBillCode == creditCode).GroupBy(p => p.TaxRate).ToList();
                                        foreach (var item in invoiceDetailPos)
                                        {
                                            originalBillArEntry.taxRateDetails.Add(new taxRateDetail
                                            {
                                                taxrate = item.Key,
                                                usedAmount = item.ToList().Sum(p => p.Value)
                                            });
                                        }
                                    }
                                    else
                                    {
                                        originalBillArEntry.taxRateDetails = new List<taxRateDetail> {
                                            new taxRateDetail{
                                                taxrate= sortDetailLst.First().TaxRate,
                                                usedAmount=customizeInvoiceItem.CustomizeInvoiceDetail.Where(p => p.CreditBillCode.Contains(creditCode)).Sum(p => p.Value)
                                            }
                                        };
                                    }
                                    originalDetails.Add(originalBillArEntry);
                                }
                                else
                                {
                                    return (0, $"“【{customizeInvoiceItem.Code}】,{creditCode}”没有找到应收单！");
                                }
                            }
                        }
                        else
                        {
                            var ismarge = customizeInvoiceItem.CustomizeInvoiceDetail.Count(p => p.CreditBillCode.Contains(","));
                            if (customizeInvoiceItem.RelationType == CustomizeInvoiceRelationTypeEunm.PartRedOffset || (ismarge == 0 && customizeInvoiceItem.RelationType != null))
                            {
                                foreach (var creditCode in creditCodeArry)
                                {
                                    var credit = credits.Where(p => p.BillCode == creditCode).FirstOrDefault();
                                    if (credit != null)
                                    {
                                        var originalBillArEntry = new originalBillArEntryItem
                                        {
                                            jfzx_arbillnum = credit.BillCode,
                                            jfzx_syamount = customizeInvoiceItem.CustomizeInvoiceDetail.Where(p => p.CreditBillCode.Contains(creditCode)).Sum(p => p.Value),
                                            jfzx_bizorg = credit.BusinessDeptId,
                                            jfzx_customer = credit.CustomerId.ToString().ToUpper(),
                                            jfzx_order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                            jfzx_project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                            user = createBy,
                                        };
                                        if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.InitCredit)
                                        {
                                            var invoiceDetailPos = sortDetailLst.Where(p => p.CreditBillCode == creditCode).GroupBy(p => p.TaxRate).ToList();
                                            foreach (var item in invoiceDetailPos)
                                            {
                                                originalBillArEntry.taxRateDetails.Add(new taxRateDetail
                                                {
                                                    taxrate = item.Key,
                                                    usedAmount = item.ToList().Sum(p => p.Value)
                                                });
                                            }
                                        }
                                        else
                                        {
                                            originalBillArEntry.taxRateDetails = new List<taxRateDetail> {
                                                new taxRateDetail{
                                                    taxrate= sortDetailLst.First().TaxRate,
                                                    usedAmount=customizeInvoiceItem.CustomizeInvoiceDetail.Where(p => p.CreditBillCode.Contains(creditCode)).Sum(p => p.Value)
                                                }
                                            };
                                        }
                                        originalDetails.Add(originalBillArEntry);
                                    }
                                    else
                                    {
                                        return (0, $"“【{customizeInvoiceItem.Code}】,{creditCode}”没有找到应收单！");
                                    }
                                }
                            }
                            else
                            {
                                foreach (var creditCode in creditCodeArry)
                                {
                                    var credit = credits.Where(p => p.BillCode == creditCode).FirstOrDefault();
                                    if (customizeInvoiceSubs.Any())
                                    {
                                        var amount = decimal.Parse(customizeInvoiceSubs.Where(p => p.CreditBillCode == creditCode).
                                            Sum(p => p.Quantity * p.Price).ToString("F2"));
                                        if (string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                                        {
                                            var originalBillArEntry = new originalBillArEntryItem
                                            {
                                                jfzx_arbillnum = creditCode,
                                                jfzx_syamount = amount,
                                                jfzx_bizorg = credit.BusinessDeptId,
                                                jfzx_customer = credit.CustomerId.ToString().ToUpper(),
                                                jfzx_order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                                jfzx_project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                                user = createBy,
                                            };
                                            if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.InitCredit)
                                            {
                                                var invoiceDetailPos = sortDetailLst.Where(p => p.CreditBillCode == creditCode).GroupBy(p => p.TaxRate).ToList();
                                                foreach (var item in invoiceDetailPos)
                                                {
                                                    originalBillArEntry.taxRateDetails.Add(new taxRateDetail
                                                    {
                                                        taxrate = item.Key,
                                                        usedAmount = item.ToList().Sum(p => p.Value)
                                                    });
                                                }
                                            }
                                            else
                                            {
                                                originalBillArEntry.taxRateDetails = new List<taxRateDetail> {
                                                    new taxRateDetail{
                                                        taxrate= sortDetailLst.First().TaxRate,
                                                        usedAmount= amount
                                                    }
                                                };
                                            }
                                            originalDetails.Add(originalBillArEntry);
                                        }
                                        else
                                        {
                                            var originalBillArEntry = new originalBillArEntryItem
                                            {
                                                jfzx_arbillnum = creditCode,
                                                jfzx_syamount = -amount,
                                                jfzx_bizorg = credit.BusinessDeptId,
                                                jfzx_customer = credit.CustomerId.ToString().ToUpper(),
                                                jfzx_order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                                jfzx_project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                                user = createBy,
                                            };
                                            if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.InitCredit)
                                            {
                                                var invoiceDetailPos = sortDetailLst.Where(p => p.CreditBillCode == creditCode).GroupBy(p => p.TaxRate).ToList();
                                                foreach (var item in invoiceDetailPos)
                                                {
                                                    originalBillArEntry.taxRateDetails.Add(new taxRateDetail
                                                    {
                                                        taxrate = item.Key,
                                                        usedAmount = item.ToList().Sum(p => p.Value)
                                                    });
                                                }
                                            }
                                            else
                                            {
                                                originalBillArEntry.taxRateDetails = new List<taxRateDetail> {
                                                    new taxRateDetail{
                                                        taxrate= sortDetailLst.First().TaxRate,
                                                        usedAmount=-amount,
                                                    }
                                                };
                                            }
                                            originalDetails.Add(originalBillArEntry);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion
                var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                {
                    Names = new List<string> { createBy }
                });
                if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
                {
                    return (0, $"“【{customizeInvoiceItem.Code}】,{createBy}”制单人在用户中心不存在！");
                }
                // 是否推送默认开票人邮箱
                if (!isPushDefaultEmail.HasValue)
                {
                    // 默认推送
                    isPushDefaultEmail = true;
                }
                string buyeremail = string.Empty;
                var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    id = customizeInvoiceItem.CustomerId
                });
                if (customer == null)
                {
                    return (0, $"“【{customizeInvoiceItem.Code}】,{customizeInvoiceItem.CustomerName}”客户不存在！");
                }
                if (customer.customerType1.Equals("0103") && !customizeInvoiceItem.InvoiceType.GetDescription().Contains("普通"))
                {
                    return (0, $"客户类型为【部队医院】只支持开普通发票！");
                }
                if (useCustomerDefaultEmail)
                {
                    customerEmail = customer.email ?? "";
                }
                if (isPushDefaultEmail.Value)
                {
                    buyeremail = user == null ? customerEmail : string.Concat(user.Data.List.First().Email, ";", customerEmail);
                }
                else
                {
                    buyeremail = customerEmail;
                }


                var invoice = customer.customerInvoices.FirstOrDefault(p => p.isInvoiceUnit == 1);
                if (!string.IsNullOrEmpty(customerInvoice))
                {
                    // 替换成用户选择的开票主体
                    invoice = JsonConvert.DeserializeObject<customerInvoice>(customerInvoice);
                }
                if (invoice == null)
                {
                    return (0, $"“【{customizeInvoiceItem.Code}】,{customizeInvoiceItem.CustomerName}”客户不存在开票主体！");
                }
                if (!customer.customerType1.Equals("0103") && string.IsNullOrEmpty(invoice.invoiceCode))
                {
                    if (invoice != null && invoice.invoiceTitle.HasValue && invoice.invoiceTitle.Value != 1)
                    {
                        return (0, $"“【{customizeInvoiceItem.Code}】,{customizeInvoiceItem.CustomerName}”客户开票主体税号为空！");
                    }
                }
                #region 校验
                if ((int)customizeInvoiceItem.InvoiceType == 0)
                {
                    return (0, $"[{customizeInvoiceItem.Code}]，{customizeInvoiceItem.CustomerName}开票中，开票类型不能为空！");
                }
                if (customizeInvoiceItem.CustomizeInvoiceDetail == null || customizeInvoiceItem.CustomizeInvoiceDetail.Count(cd => string.IsNullOrWhiteSpace(cd.TaxTypeNo)) > 0)
                {
                    return (0, $"[{customizeInvoiceItem.Code}]，{customizeInvoiceItem.CustomerName}开票明细中，税收分类编码不能为空！");
                }
                if (string.IsNullOrEmpty(customizeInvoiceItem.ApproveRemark))
                {
                    var differents = customizeInvoiceItem.CustomizeInvoiceDetail?.Where(p => p.OriginProductName != p.ProductName).ToList();
                    if (differents != null && differents.Count > 0)
                    {
                        string differentsStr = string.Join(",", differents.Select(p => $"排序号{p.Sort},原始品名:[{p.OriginProductName}]与开票品名:[{p.ProductName}]"));
                        return (0, $"[{customizeInvoiceItem.Code}]存在原始品名与开票名称不一致的数据，请填写审批备注后提交，不一致数据如下：{differentsStr}");
                    }
                }
                //if (customizeInvoiceItem.CustomizeInvoiceDetail.Select(p => p.TaxRate).Distinct().Count() > 1)
                //{
                //    return (0, "开票明细必须税率一致!");
                //}
                #endregion
                var invoiceSaveInput = new PushCustomizeInvoiceSaveInput()
                {
                    orgid = customizeInvoiceItem.NameCode,
                    billdate = customizeInvoiceItem.BillDate.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                    billsourcetype = customizeInvoiceItem.InvoiceTotalAmount < 0 ? "B" : "A",
                    applicant = customizeInvoiceItem.RedOffsetOpter == null ? "" : customizeInvoiceItem.RedOffsetOpter.ToString(), //申请方
                    redreason = customizeInvoiceItem.RedOffsetReason.ToString(),//红冲原因
                    infocode = string.IsNullOrEmpty(customizeInvoiceItem.RedOffsetCode) ? new List<string> { } : new List<string> { customizeInvoiceItem.RedOffsetCode },//红字信息表编号
                    originalinvoicecode = customizeInvoiceItem.InvoiceCode,
                    originalinvoiceno = !string.IsNullOrEmpty(customizeInvoiceItem.InvoiceNo) ? customizeInvoiceItem.InvoiceNo.TrimStart().TrimEnd() : string.Empty,
                    //originalissuetime
                    billno = customizeInvoiceItem.Code,
                    billproperties = customizeInvoiceItem.InvoiceTotalAmount < 0 ? "-1" : "1",
                    invoicetype = GetKingdeeInvoicetype(customizeInvoiceItem.InvoiceType.GetDescription()),
                    taxationstyle = "0", //目前固定0，普通征税=0,差额征税=2,减按计征=1,差额征税-全额开票=01,差额征税-差额开票=02
                    specialtype = "00",  //目前固定00，非特殊票种=00，收购=02，抵扣通行费=06，不抵扣通行费  07，成品油 08，卷烟  11，机动车 18，不动产租赁=E06，建筑服务=E03，不动产销售=E05，货物运输=E04，成品油=E01，卷烟=E18
                    buyeremail = buyeremail.TrimEnd(';'),
                    username = createBy,
                    buyerphone = user == null ? "" : user.Data.List.First().MfaPhoneNumber,
                    buyername = invoice == null ? "" : invoice.invoiceName,
                    buyertaxno = invoice == null ? "" : invoice.invoiceCode,
                    buyerbank = invoice == null ? "" : invoice.invoiceBank + invoice.invoiceBankNo,
                    buyeraddr = invoice == null ? "" : invoice.invoiceAddr + " " + invoice.invoiceTel,
                    invoiceremark = customizeInvoiceItem.Remark,
                    jfzx_auditsuggestion = customizeInvoiceItem.ApproveRemark,
                    originalBillEntry = details,
                    originalBillArEntry = originalDetails,
                    jfzx_customerf = customizeInvoiceItem.CustomerId.ToUpper(),
                };
                if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.Pre)
                {
                    invoiceSaveInput.jfzx_preinvoicing = true;
                    var preCustomizeInvoiceItem = await _db.PreCustomizeInvoiceItem.Where(p => p.Code == classifyPo.RelationCode).FirstAsync();
                    if (preCustomizeInvoiceItem != null)
                    {
                        invoiceSaveInput.jfzx_itemnumber = preCustomizeInvoiceItem.ProjectCode;
                        invoiceSaveInput.jfzx_ordernumber = preCustomizeInvoiceItem.Code;
                        invoiceSaveInput.jfzx_businessorg = preCustomizeInvoiceItem.BusinessDeptId;
                    }
                }
                if (invoice != null && invoice.invoiceTitle.HasValue && invoice.invoiceTitle.Value == 1)
                {
                    invoiceSaveInput.buyertaxno = "";
                    invoiceSaveInput.buyerbank = "";
                    invoiceSaveInput.buyeraddr = "";
                }
                if (customizeInvoiceItem.InvoiceType.GetDescription().Contains("普通") || customizeInvoiceItem.InvoiceType.GetDescription().Contains("数电"))
                {
                    invoiceSaveInput.applicant = "";
                }
                inputs.Add(invoiceSaveInput);
            }
            if (inputs.Any())
            {
                var king = await _kingdeeApiClient.PushCustomizeInvoice(inputs, flow);
                if (king != null && king.Code == CodeStatusEnum.Success)
                {
                    return (1, $"操作成功");
                }
                else
                {
                    return (0, king.Message);
                }
            }
            else
            {
                return (0, "没有提交数据");
            }
        }
        private async Task<(int, string)> SubmitToKingdeeForRedAsync(
          List<CustomizeInvoiceItemPo> customizeInvoiceItems,
          string createBy,
          bool? isPushDefaultEmail,
          string flow = "Y",
          string customerEmail = "",
          string customerInvoice = "",
          bool useCustomerDefaultEmail = false
          )
        {
            // 邮箱字符串处理
            if (!string.IsNullOrEmpty(customerEmail))
            {
                // 替换逗号
                customerEmail = customerEmail.Replace(",", ";");
                // 去尾
                if (customerEmail[customerEmail.Length - 1] == ';')
                {
                    customerEmail = customerEmail.Substring(0, customerEmail.Length - 1);
                }
                // 掐头
                if (customerEmail[0] == ';')
                {
                    customerEmail = customerEmail.Substring(1, customerEmail.Length - 1);
                }
            }
            List<saveRedBillingApplicationInput> inputs = new List<saveRedBillingApplicationInput>();
            var classifyPo = await _db.CustomizeInvoiceClassify.Where(p => p.Id == customizeInvoiceItems[0].CustomizeInvoiceClassifyId).AsNoTracking().FirstAsync();
            foreach (var customizeInvoiceItem in customizeInvoiceItems)
            {
                //负数发票校验
                if (customizeInvoiceItem.InvoiceTotalAmount < 0)
                {
                    if (!customizeInvoiceItem.RedOffsetReason.HasValue)
                        return (0, "操作失败，原因：冲红原因必填，请检查！");
                }
                if (!string.IsNullOrEmpty(customizeInvoiceItem.RelationCode) &&
                    (customizeInvoiceItem.InvoiceType == InvoiceTypeEnum.DigitalCircuitTiket || customizeInvoiceItem.InvoiceType == InvoiceTypeEnum.DigitalCircuitUniversal))
                {
                    customizeInvoiceItem.RedOffsetOpter = null;
                }

                var details = new List<RedOriginalBillEntryModel>(); //商品详情
                var originalDetails = new List<RedOriginalBillArEntryModel>(); //应收明细
                var customizeInvoiceSubs = new List<CustomizeInvoiceSubDetailPo>();

                var sortDetailLst = customizeInvoiceItem.CustomizeInvoiceDetail
                                                        .OrderBy(z => z.Sort)
                                                        .ThenByDescending(z => z.CreatedTime)
                                                        .ThenByDescending(p => p.Price * p.Quantity);

                foreach (var d in sortDetailLst)
                {
                    var taxRate = d.TaxRate / 100.00M;
                    if (!d.Sort.HasValue)
                    {
                        return (0, $"“开票明细【{d.ProductName}】”没有开票序号！");
                    }
                    if (d.Value != 0)
                    {
                        var detail = new RedOriginalBillEntryModel
                        {
                            gift = false,
                            taxrate = taxRate,
                            //num = d.Price < 0 ? d.Quantity * -1 : d.Quantity,
                            specification = d.Specification,
                            goodsName = d.ProductName,
                            taxAmount = d.Value,
                            remark = "",
                            taxRateCodeId = d.TaxTypeNo,
                            policylogo = "0",
                            unit = d.PackUnit,
                            rowtype = "2",
                            invoiceSeq = d.Sort.Value - 1,
                            //tax = d.Value / (1 + taxRate) * taxRate,
                        };
                        details.Add(detail);
                    }
                }
                var creditCodes = customizeInvoiceItem.CustomizeInvoiceDetail.Select(p => p.CreditBillCode).Distinct().ToList();
                var creditCodeStr = string.Join(',', creditCodes);
                var creditCodeArry = creditCodeStr.Split(',', StringSplitOptions.RemoveEmptyEntries).Distinct().ToHashSet();
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                if (customizeInvoiceCredits.Any()) //如果开票明细关系表中应收单号
                {
                    creditCodeArry = customizeInvoiceCredits.Select(p => p.CreditCode).Distinct().ToHashSet();
                }
                var credits = await _db.Credits.Include(p => p.CreditDetails).Where(p => creditCodeArry.Contains(p.BillCode)).AsNoTracking().ToListAsync();
                var creditAllGroupArry = credits.GroupBy(p => p.CustomerId).ToList();
                #region 查询应收分录 
                if (customizeInvoiceCredits.Any())
                {
                    foreach (var creditCode in creditCodeArry)
                    {
                        var credit = credits.Where(p => p.BillCode == creditCode).FirstOrDefault();
                        if (credit != null)
                        {
                            var amount = decimal.Parse(customizeInvoiceCredits.Where(p => p.CreditCode == creditCode).
                                             Sum(p => p.CustomizeInvoiceDetailAmount).ToString("F2"));
                            var redOriginalBillArEntryModel = new RedOriginalBillArEntryModel
                            {
                                arBillNum = creditCode,
                                bizOrg = credit.BusinessDeptId,
                                customer = credit.CustomerId.ToString().ToUpper(),
                                order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                user = createBy,
                            };
                            var taxRateDetails = new List<taxRateDetail>();
                            var creditDetailGroup = credit.CreditDetails.GroupBy(p => p.TaxRate);
                            foreach (var g in creditDetailGroup)
                            {
                                var usedAmount = 0m;
                                foreach (var item in g.ToList())
                                {
                                    usedAmount += customizeInvoiceCredits.Where(p => p.CreditDetailId == item.Id).Sum(p => p.CustomizeInvoiceDetailAmount);
                                }

                                taxRateDetails.Add(new taxRateDetail
                                {
                                    taxrate = g.Key.Value,
                                    usedAmount = usedAmount
                                });

                            }
                            redOriginalBillArEntryModel.taxRateDetails = taxRateDetails;
                            originalDetails.Add(redOriginalBillArEntryModel);
                        }
                    }
                }
                else
                {
                    if (classifyPo.Classify != CustomizeInvoiceClassifyEnum.Pre)
                    {
                        if (string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                        {
                            customizeInvoiceSubs = await _db.CustomizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).AsNoTracking().ToListAsync();
                        }
                        else
                        {
                            var customize = await _db.CustomizeInvoiceItem.Where(p => p.Code == customizeInvoiceItem.RelationCode).AsNoTracking().FirstOrDefaultAsync();
                            if (customize != null)
                            {
                                customizeInvoiceSubs = await _db.CustomizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId == customize.Id).AsNoTracking().ToListAsync();
                            }
                        }
                        //查询分组下面的申开单
                        var customizes = await _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == customizeInvoiceItem.CustomizeInvoiceClassifyId).AsNoTracking().ToListAsync();
                        var classifyOhter = await _db.CustomizeInvoiceClassify.Where(p => !string.IsNullOrEmpty(p.RelationCode) && p.RelationCode == classifyPo.RelationCode).AsNoTracking().FirstOrDefaultAsync();

                        if (classifyOhter == null)
                        {
                            classifyOhter = await _db.CustomizeInvoiceClassify.Where(p => p.RelationCode == classifyPo.BillCode).AsNoTracking().FirstOrDefaultAsync();
                        }
                        if (customizes.Count > 1 || creditCodeArry.Count() == 1 || !customizeInvoiceSubs.Any() || classifyOhter != null) //拆单过的就直接给申开的的金额
                        {
                            classifyOhter = await _db.CustomizeInvoiceClassify.Where(p => p.RelationCode == classifyPo.BillCode).AsNoTracking().FirstOrDefaultAsync();
                        }
                        if (customizes.Count > 1 || creditCodeArry.Count() == 1 || !customizeInvoiceSubs.Any() || classifyOhter != null) //拆单过的就直接给申开的的金额
                        {
                            foreach (var creditCode in creditCodeArry)
                            {
                                var credit = credits.Where(p => p.BillCode == creditCode).FirstOrDefault();
                                if (credit != null)
                                {
                                    var redOriginalBillArEntryModel = new RedOriginalBillArEntryModel
                                    {
                                        arBillNum = creditCode,
                                        bizOrg = credit.BusinessDeptId,
                                        customer = credit.CustomerId.ToString().ToUpper(),
                                        order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                        project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                        user = createBy
                                    };
                                    if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.InitCredit)
                                    {
                                        var invoiceDetailPos = sortDetailLst.Where(p => p.CreditBillCode == creditCode).GroupBy(p => p.TaxRate).ToList();
                                        foreach (var item in invoiceDetailPos)
                                        {
                                            redOriginalBillArEntryModel.taxRateDetails.Add(new taxRateDetail
                                            {
                                                taxrate = item.Key,
                                                usedAmount = item.ToList().Sum(p => p.Value)
                                            });
                                        }
                                    }
                                    else
                                    {
                                        redOriginalBillArEntryModel.taxRateDetails = new List<taxRateDetail> {
                                            new taxRateDetail{
                                                taxrate= sortDetailLst.First().TaxRate,
                                                usedAmount=customizeInvoiceItem.CustomizeInvoiceDetail.Where(p => p.CreditBillCode.Contains(creditCode)).Sum(p => p.Value)
                                            }
                                        };
                                    }
                                    originalDetails.Add(redOriginalBillArEntryModel);
                                }
                                else
                                {
                                    return (0, $"“【{customizeInvoiceItem.Code}】,{creditCode}”没有找到应收单！");
                                }
                            }
                        }
                        else
                        {
                            var ismarge = customizeInvoiceItem.CustomizeInvoiceDetail.Count(p => p.CreditBillCode.Contains(","));
                            if (customizeInvoiceItem.RelationType == CustomizeInvoiceRelationTypeEunm.PartRedOffset || (ismarge == 0 && customizeInvoiceItem.RelationType != null))
                            {
                                foreach (var creditCode in creditCodeArry)
                                {
                                    var credit = credits.Where(p => p.BillCode == creditCode).FirstOrDefault();
                                    if (credit != null)
                                    {
                                        var redOriginalBillArEntryModel = new RedOriginalBillArEntryModel
                                        {
                                            arBillNum = creditCode,
                                            bizOrg = credit.BusinessDeptId,
                                            customer = credit.CustomerId.ToString().ToUpper(),
                                            order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                            project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                            user = createBy
                                        };
                                        if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.InitCredit)
                                        {
                                            var invoiceDetailPos = sortDetailLst.Where(p => p.CreditBillCode == creditCode).GroupBy(p => p.TaxRate).ToList();
                                            foreach (var item in invoiceDetailPos)
                                            {
                                                redOriginalBillArEntryModel.taxRateDetails.Add(new taxRateDetail
                                                {
                                                    taxrate = item.Key,
                                                    usedAmount = item.ToList().Sum(p => p.Value)
                                                });
                                            }
                                        }
                                        else
                                        {
                                            redOriginalBillArEntryModel.taxRateDetails = new List<taxRateDetail> {
                                                new taxRateDetail{
                                                    taxrate= sortDetailLst.First().TaxRate,
                                                    usedAmount=customizeInvoiceItem.CustomizeInvoiceDetail.Where(p => p.CreditBillCode.Contains(creditCode)).Sum(p => p.Value)
                                                }
                                            };
                                        }
                                        originalDetails.Add(redOriginalBillArEntryModel);
                                    }
                                    else
                                    {
                                        return (0, $"“【{customizeInvoiceItem.Code}】,{creditCode}”没有找到应收单！");
                                    }
                                }
                            }
                            else
                            {
                                foreach (var creditCode in creditCodeArry)
                                {
                                    var credit = credits.Where(p => p.BillCode == creditCode).FirstOrDefault();
                                    if (customizeInvoiceSubs.Any())
                                    {
                                        var amount = decimal.Parse(customizeInvoiceSubs.Where(p => p.CreditBillCode == creditCode).
                                            Sum(p => p.Quantity * p.Price).ToString("F2"));
                                        if (string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                                        {
                                            var redOriginalBillArEntryModel = new RedOriginalBillArEntryModel
                                            {
                                                arBillNum = creditCode,
                                                bizOrg = credit.BusinessDeptId,
                                                customer = credit.CustomerId.ToString().ToUpper(),
                                                order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                                project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                                user = createBy
                                            };
                                            if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.InitCredit)
                                            {
                                                var invoiceDetailPos = sortDetailLst.Where(p => p.CreditBillCode == creditCode).GroupBy(p => p.TaxRate).ToList();
                                                foreach (var item in invoiceDetailPos)
                                                {
                                                    redOriginalBillArEntryModel.taxRateDetails.Add(new taxRateDetail
                                                    {
                                                        taxrate = item.Key,
                                                        usedAmount = item.ToList().Sum(p => p.Value)
                                                    });
                                                }
                                            }
                                            else
                                            {
                                                redOriginalBillArEntryModel.taxRateDetails = new List<taxRateDetail> {
                                                    new taxRateDetail{
                                                        taxrate= sortDetailLst.First().TaxRate,
                                                        usedAmount= amount
                                                    }
                                                };
                                            }
                                            originalDetails.Add(redOriginalBillArEntryModel);
                                        }
                                        else
                                        {
                                            var redOriginalBillArEntryModel = new RedOriginalBillArEntryModel
                                            {
                                                arBillNum = creditCode,
                                                bizOrg = credit.BusinessDeptId,
                                                customer = credit.CustomerId.ToString().ToUpper(),
                                                order = string.IsNullOrEmpty(credit.OrderNo) ? "" : credit.OrderNo,
                                                project = string.IsNullOrEmpty(credit.ProjectCode) ? "" : credit.ProjectCode,
                                                user = createBy
                                            };
                                            if (classifyPo.Classify == CustomizeInvoiceClassifyEnum.InitCredit)
                                            {
                                                var invoiceDetailPos = sortDetailLst.Where(p => p.CreditBillCode == creditCode).GroupBy(p => p.TaxRate).ToList();
                                                foreach (var item in invoiceDetailPos)
                                                {
                                                    redOriginalBillArEntryModel.taxRateDetails.Add(new taxRateDetail
                                                    {
                                                        taxrate = item.Key,
                                                        usedAmount = item.ToList().Sum(p => p.Value)
                                                    });
                                                }
                                            }
                                            else
                                            {
                                                redOriginalBillArEntryModel.taxRateDetails = new List<taxRateDetail> {
                                                    new taxRateDetail{
                                                        taxrate= sortDetailLst.First().TaxRate,
                                                        usedAmount=-amount,
                                                    }
                                                };
                                            }
                                            originalDetails.Add(redOriginalBillArEntryModel);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion
                var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                {
                    Names = new List<string> { createBy }
                });
                if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
                {
                    return (0, $"“【{customizeInvoiceItem.Code}】,{createBy}”制单人在用户中心不存在！");
                }
                // 是否推送默认开票人邮箱
                if (!isPushDefaultEmail.HasValue)
                {
                    // 默认推送
                    isPushDefaultEmail = true;
                }
                string buyeremail = string.Empty;
                var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    id = customizeInvoiceItem.CustomerId
                });
                if (customer == null)
                {
                    return (0, $"“【{customizeInvoiceItem.Code}】,{customizeInvoiceItem.CustomerName}”客户不存在！");
                }
                if (customer.customerType1.Equals("0103") && !customizeInvoiceItem.InvoiceType.GetDescription().Contains("普通"))
                {
                    return (0, $"客户类型为【部队医院】只支持开普通发票！");
                }
                if (string.IsNullOrEmpty(customerEmail) && useCustomerDefaultEmail)
                {
                    customerEmail = customer.email ?? "";
                }
                if (isPushDefaultEmail.Value)
                {
                    buyeremail = user == null ? customerEmail : string.Concat(user.Data.List.First().Email, ";", customerEmail);
                }
                else
                {
                    buyeremail = customerEmail;
                }


                var invoice = customer.customerInvoices.FirstOrDefault(p => p.isInvoiceUnit == 1);
                if (!string.IsNullOrEmpty(customerInvoice))
                {
                    // 替换成用户选择的开票主体
                    invoice = JsonConvert.DeserializeObject<customerInvoice>(customerInvoice);
                }
                if (invoice == null)
                {
                    return (0, $"“【{customizeInvoiceItem.Code}】,{customizeInvoiceItem.CustomerName}”客户不存在开票主体！");
                }
                if (!customer.customerType1.Equals("0103") && string.IsNullOrEmpty(invoice.invoiceCode))
                {
                    if (invoice != null && invoice.invoiceTitle.HasValue && invoice.invoiceTitle.Value != 1)
                    {
                        return (0, $"“【{customizeInvoiceItem.Code}】,{customizeInvoiceItem.CustomerName}”客户开票主体税号为空！");
                    }
                }
                #region 校验
                if ((int)customizeInvoiceItem.InvoiceType == 0)
                {
                    return (0, $"[{customizeInvoiceItem.Code}]，{customizeInvoiceItem.CustomerName}开票中，开票类型不能为空！");
                }
                if (customizeInvoiceItem.CustomizeInvoiceDetail == null || customizeInvoiceItem.CustomizeInvoiceDetail.Count(cd => string.IsNullOrWhiteSpace(cd.TaxTypeNo)) > 0)
                {
                    return (0, $"[{customizeInvoiceItem.Code}]，{customizeInvoiceItem.CustomerName}开票明细中，税收分类编码不能为空！");
                }
                if (string.IsNullOrEmpty(customizeInvoiceItem.ApproveRemark))
                {
                    var differents = customizeInvoiceItem.CustomizeInvoiceDetail?.Where(p => p.OriginProductName != p.ProductName).ToList();
                    if (differents != null && differents.Count > 0)
                    {
                        string differentsStr = string.Join(",", differents.Select(p => $"排序号{p.Sort},原始品名:[{p.OriginProductName}]与开票品名:[{p.ProductName}]"));
                        return (0, $"[{customizeInvoiceItem.Code}]存在原始品名与开票名称不一致的数据，请填写审批备注后提交，不一致数据如下：{differentsStr}");
                    }
                }
                #endregion
                var invoiceSaveInput = new saveRedBillingApplicationInput()
                {
                    orgid = customizeInvoiceItem.NameCode,
                    billdate = customizeInvoiceItem.BillDate.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                    billsourcetype = "B",
                    applicant = customizeInvoiceItem.RedOffsetOpter == null ? "" : customizeInvoiceItem.RedOffsetOpter.ToString(), //申请方
                    redreason = customizeInvoiceItem.RedOffsetReason.ToString(),//红冲原因
                    infocode = string.IsNullOrEmpty(customizeInvoiceItem.RedOffsetCode) ? new List<string> { } : new List<string> { customizeInvoiceItem.RedOffsetCode },//红字信息表编号
                    originalInvoiceCode = customizeInvoiceItem.InvoiceCode,
                    originalInvoiceNo = !string.IsNullOrEmpty(customizeInvoiceItem.InvoiceNo) ? customizeInvoiceItem.InvoiceNo.TrimStart().TrimEnd() : string.Empty,
                    BillNo = customizeInvoiceItem.Code,
                    billproperties = customizeInvoiceItem.InvoiceTotalAmount < 0 ? "-1" : "1",
                    invoicetype = GetKingdeeInvoicetype(customizeInvoiceItem.InvoiceType.GetDescription()),
                    taxationstyle = "0", //目前固定0，普通征税=0,差额征税=2,减按计征=1,差额征税-全额开票=01,差额征税-差额开票=02
                    specialtype = "00",  //目前固定00，非特殊票种=00，收购=02，抵扣通行费=06，不抵扣通行费  07，成品油 08，卷烟  11，机动车 18，不动产租赁=E06，建筑服务=E03，不动产销售=E05，货物运输=E04，成品油=E01，卷烟=E18
                    buyeremail = buyeremail.TrimEnd(';'),
                    username = createBy,
                    buyerphone = user == null ? "" : user.Data.List.First().MfaPhoneNumber,
                    buyername = invoice == null ? "" : invoice.invoiceName,
                    buyertaxno = invoice == null ? "" : invoice.invoiceCode,
                    buyerbank = invoice == null ? "" : invoice.invoiceBank + invoice.invoiceBankNo,
                    buyeraddr = invoice == null ? "" : invoice.invoiceAddr + " " + invoice.invoiceTel,
                    invoiceremark = customizeInvoiceItem.Remark,
                    auditsuggestion = customizeInvoiceItem.ApproveRemark,
                    originalBillEntry = details,
                    arEntryList = originalDetails,
                    customer = customizeInvoiceItem.CustomerId.ToUpper(),
                };
                if (invoice != null && invoice.invoiceTitle.HasValue && invoice.invoiceTitle.Value == 1)
                {
                    invoiceSaveInput.buyertaxno = "";
                    invoiceSaveInput.buyerbank = "";
                    invoiceSaveInput.buyeraddr = "";
                }
                if (customizeInvoiceItem.InvoiceType.GetDescription().Contains("普通") || customizeInvoiceItem.InvoiceType.GetDescription().Contains("数电"))
                {
                    invoiceSaveInput.applicant = "";
                }
                inputs.Add(invoiceSaveInput);
            }
            if (inputs.Any())
            {
                var king = await _kingdeeApiClient.saveRedBillingApplication(inputs, flow);
                if (king != null && king.Code == CodeStatusEnum.Success)
                {
                    return (1, $"操作成功");
                }
                else
                {
                    return (0, king.Message);
                }
            }
            else
            {
                return (0, "没有提交数据");
            }
        }
        public async Task<BaseResponseData<string>> AuditForOA(SubmitCustomizeInvoiceInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功!");
            var customize = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(input.CustomizeInvoiceItemId);
            if (customize != null)
            {
                var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == customize.CustomizeInvoiceClassifyId).AsNoTracking().FirstOrDefaultAsync();
                string customerEmail = customizeInvoiceClassify == null ? string.Empty : customizeInvoiceClassify.CustomerEmail;

                var customizeInvoiceDetails = await _db.CustomizeInvoiceDetail
                                                    //.Include(p => p.CustomizeInvoiceSubDetails)
                                                    .Where(c => c.CustomizeInvoiceItemId == input.CustomizeInvoiceItemId)
                                                    .OrderBy(p => p.CreatedTime)
                                                    .ThenByDescending(p => p.Price)
                                                    .AsNoTracking().ToListAsync();
                (int count, string msg) = await SubmitToKingdeeAsync(customizeInvoiceDetails, customize, customize.CreatedBy, customizeInvoiceClassify.IsPushDefaultEmail, "Y", customerEmail, customizeInvoiceClassify.Invoiceofclassfiy);
                if (count <= 0)
                {
                    ret = BaseResponseData<string>.Failed(500, msg);
                    return ret;
                }
                //var king = await _kingdeeApiClient.AuditBillingApplication(new AuditBillingApplicationInput { billno = customize.Code });
                //if (king.Code != CodeStatusEnum.Success)
                //{
                //    ret = BaseResponseData<string>.Failed(500, king.Message);
                //    return ret;
                //}
                customize.Status = 2;
                customize.IsPush = true;
                await _customizeInvoiceItemRepository.UpdateAsync(customize.Adapt<CustomizeInvoiceItem>());
                await _unitOfWork.CommitAsync();
            }
            else
            {
                var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.Where(p => p.Id == input.CustomizeInvoiceClassifyId).AsNoTracking().FirstOrDefaultAsync();
                if (customizeInvoiceClassify == null)
                {
                    ret = BaseResponseData<string>.Failed(500, "操作失败，原因：没有找到分类数据。");
                    return ret;
                }
                var customizeInvoiceItems = await _db.CustomizeInvoiceItem.
                                            Include(p => p.CustomizeInvoiceDetail).
                                            // ThenInclude(p => p.CustomizeInvoiceSubDetails).
                                            Where(p => p.CustomizeInvoiceClassifyId == input.CustomizeInvoiceClassifyId).
                                            AsNoTracking().ToListAsync();
                int count = 0;
                string msg = "";
                if (customizeInvoiceItems[0].IsNoRedConfirm == 1)
                {
                    (count, msg) = await SubmitToKingdeeForRedAsync(customizeInvoiceItems, customizeInvoiceItems[0].CreatedBy, customizeInvoiceClassify.IsPushDefaultEmail, "Y", customizeInvoiceClassify.CustomerEmail, customizeInvoiceClassify.Invoiceofclassfiy); //flow=="N" 只做检查
                }
                else
                {
                    (count, msg) = await SubmitToKingdeeAsync(customizeInvoiceItems, customizeInvoiceItems[0].CreatedBy, customizeInvoiceClassify.IsPushDefaultEmail, "Y", customizeInvoiceClassify.CustomerEmail, customizeInvoiceClassify.Invoiceofclassfiy);
                }
                if (count <= 0)
                {
                    ret = BaseResponseData<string>.Failed(500, msg);
                    return ret;
                }
                foreach (var item in customizeInvoiceItems)
                {
                    item.Status = CustomizeInvoiceStatusEnum.WaitInvoice;
                    item.IsPush = true;
                }
                customizeInvoiceClassify.Status = CustomizeInvoiceStatusEnum.WaitInvoice;
                _db.CustomizeInvoiceClassify.Update(customizeInvoiceClassify);
                _db.CustomizeInvoiceItem.UpdateRange(customizeInvoiceItems);
                await _unitOfWork.CommitAsync();
            }
            return ret;
        }

        /// <summary>
        /// 一键生成服务费申开单
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> GenerateServiceCustomizeInvoice(GenerateServiceCustomizeInvoiceInput input, string createdBy)
        {
            var ret = BaseResponseData<int>.Success("操作成功!");
            var creditAllDB = await _db.Credits.Where(p => p.SaleSource == SaleSourceEnum.Spd &&
                                 p.CompanyId == input.CompanyId &&
                                 p.ProjectId == input.ProjectId &&
                                 p.CreditType == CreditTypeEnum.servicefee &&
                                 p.Value > 0 &&
                                 p.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed &&
                                 p.InvoiceStatus != InvoiceStatusEnum.invoiced
                                 ).ToListAsync();
            var creditBillCodes = new HashSet<string>();
            var creditIds = creditAllDB.Select(p => p.Id).ToHashSet();
            var creditDetaildbs = await _db.CreditDetails.Where(p => creditIds.Contains(p.CreditId)).ToListAsync();
            foreach (var item in creditAllDB)
            {
                var creditDetailTemps = creditDetaildbs.Where(p => p.CreditId == item.Id).ToList();
                if (creditDetailTemps != null && creditDetailTemps.Count > 0)
                {
                    var totalNoInvoiceAmount = creditDetailTemps.Sum(p => p.NoInvoiceAmount);
                    if (item.Value != totalNoInvoiceAmount)
                    {
                        creditBillCodes.Add(item.BillCode);
                    }
                }
            }

            var creditAll = creditAllDB.Where(p => !creditBillCodes.Contains(p.BillCode)).ToList();
            if (creditAll.Any())
            {
                var saleCodes = creditAll.Select(p => p.OrderNo.Contains("SA") ? p.OrderNo : p.RelateCode).Distinct().ToList();
                var saleOuts = await _sellApiClient.GetSaleList(new GetSaleListInput
                {
                    BillCodes = saleCodes,
                    PageNum = 1,
                    PageSize = int.MaxValue
                });
                var creditlessZero = creditAll.Where(p => p.Value < 0).FirstOrDefault();
                if (creditlessZero != null)
                {
                    return BaseResponseData<int>.Failed(500, $"操作失败，原因：{creditlessZero.BillCode}单号,金额<0,不允许操作！");
                }
                var customerIds = creditAll.Select(p => p.CustomerId).Distinct().ToList();
                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { input.CompanyId.ToString() }
                })).FirstOrDefault();
                var customizeInvoiceClassifys = new List<CustomizeInvoiceClassifyPo>();
                var customizeInvoiceItems = new List<CustomizeInvoiceItemPo>();
                var customizeInvoiceDetails = new List<CustomizeInvoiceDetailPo>();
                var customizeInvoiceCredits = new List<CustomizeInvoiceCreditPo>();
                var creditDetailAdds = new List<CreditDetailPo>();
                foreach (var customerId in customerIds)
                {
                    var credits = creditAll.Where(p => p.CustomerId == customerId).ToList();
                    var credit = credits.First();
                    var code = credit.RelateCode;
                    var orderNoStrs = credits.Select(p => p.RelateCode).ToList();
                    string attachFileIds = await GetAttachFileIds(orderNoStrs);
                    var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = code.Split('-')[0],
                        BillType = "CI",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1,
                        CompanyCode = companyInfo.nameCode
                    });
                    var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        id = customerId.ToString()
                    });
                    if (customer == null)
                    {
                        return BaseResponseData<int>.Failed(500, $"操作失败，原因：未找到id为{customerId}的客户！");
                    }
                    // email
                    if (input.IsPushCustomerEmail && string.IsNullOrEmpty(customer.email))
                    {
                        return BaseResponseData<int>.Failed(500, $"操作失败，原因：未找到名称为{customer.customerName}的客户邮箱！");
                    }
                    if (outPut.Status)
                    {
                        var outPutClassify = await _codeGenClient.ApplyCode(new ApplyCodeInput
                        {
                            BusinessArea = code.Split('-')[0],
                            BillType = "CIC",
                            SysMonth = companyInfo.sysMonth,
                            DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                            Num = 1,
                            CompanyCode = companyInfo.nameCode
                        });
                        var classifyId = Guid.NewGuid();
                        //添加分类
                        var invoiceClassify = new CustomizeInvoiceClassifyPo
                        {
                            Id = classifyId,
                            BillCode = outPutClassify.Codes.First(),
                            CompanyId = Guid.Parse(companyInfo.companyId),
                            CompanyName = companyInfo.companyName,
                            CustomerId = customerId.Value,
                            CustomerName = credit.CustomerName,
                            CreatedBy = createdBy,
                            Classify = CustomizeInvoiceClassifyEnum.Credit,
                            Status = CustomizeInvoiceStatusEnum.Cancel, //先作废
                            SaleSystemName = string.Empty,
                            AttachFileIds = attachFileIds,

                        };
                        if (input.IsPushCustomerEmail)
                        {
                            // email
                            invoiceClassify.CustomerEmail = string.IsNullOrEmpty(input.CustomerEmail) ? customer.email : input.CustomerEmail;
                        }
                        customizeInvoiceClassifys.Add(invoiceClassify);
                        //添加申开单
                        var itemId = Guid.NewGuid();
                        var customizeInvoiceItem = new CustomizeInvoiceItemPo()
                        {
                            Id = itemId,
                            Code = outPut.Codes.First(),
                            CustomerId = customerId.ToString(),
                            CustomerName = credit.CustomerName,
                            CompanyId = Guid.Parse(companyInfo.companyId),
                            CompanyName = companyInfo.companyName,
                            BillDate = DateTime.Now,
                            NameCode = companyInfo.nameCode,
                            InvoiceTotalAmount = credits.Sum(t => t.Value),
                            IsPush = true,
                            IsInvoiced = true,
                            Status = CustomizeInvoiceStatusEnum.WaitInvoice,
                            Remark = input.Remark,
                            CreatedBy = createdBy,
                            ApproveRemark = input.Remark ?? "服务费",
                            InvoiceType = (InvoiceTypeEnum)(int.Parse(customer?.customerInvoices?.FirstOrDefault(t => t.isInvoiceUnit == 1)?.salesInvoiceDetails ?? "0")),
                            CustomizeInvoiceClassifyId = classifyId,

                        };
                        customizeInvoiceItems.Add(customizeInvoiceItem);
                        //添加申开单明细
                        var detailId = Guid.NewGuid();
                        var customizeInvoiceDetail = new CustomizeInvoiceDetailPo
                        {
                            CreditBillCode = string.Join(",", credits.Select(p => p.BillCode)),
                            CreatedBy = createdBy,
                            CustomerId = customerId.ToString(),
                            CustomerName = credit.CustomerName,
                            CustomizeInvoiceItemId = itemId,
                            Id = detailId,
                            OriginDetailId = Guid.Empty.ToString(),
                            OrderNo = string.Join(",", credits.Select(p => p.OrderNo)),
                            OriginProductName = "服务费",
                            Price = customizeInvoiceItem.InvoiceTotalAmount,
                            OriginalPrice = customizeInvoiceItem.InvoiceTotalAmount,
                            PackUnit = input.Unit ??= string.Empty,
                            ProductName = string.IsNullOrEmpty(input.ProductName) ? "服务费" : input.ProductName,
                            ProductNo = "服务费",
                            Specification = input.Specification ??= "服务费",
                            Quantity = 1,
                            RelateCode = string.Join(",", credits.Select(p => p.RelateCode)),
                            TaxTypeNo = input.TaxTypeNo,
                            TaxRate = input.TaxRate,
                            TaxAmount = customizeInvoiceItem.InvoiceTotalAmount - (customizeInvoiceItem.InvoiceTotalAmount / (1 + input.TaxRate / 100)),
                        };
                        customizeInvoiceDetail.Value = customizeInvoiceDetail.Price;
                        customizeInvoiceDetails.Add(customizeInvoiceDetail);

                        foreach (var item in credits)
                        {
                            item.InvoiceStatus = InvoiceStatusEnum.invoiced;

                            var creditDetailId = Guid.NewGuid();
                            var saleOutTemps = saleOuts.Where(p => p.BillCode == item.RelateCode || p.BillCode == item.OrderNo).ToList();
                            if (saleOutTemps == null || saleOutTemps.Count <= 0)
                            {
                                return BaseResponseData<int>.Failed(500, $"操作失败，原因：未找到销售返回的明细数据！");
                            }
                            var saleDetail = saleOutTemps.First().SaleDetails[0];
                            var originDetailId = saleDetail.Id.ToString();
                            var creditDetail = creditDetaildbs.Where(p => p.CreditId == item.Id).FirstOrDefault();
                            if (creditDetail == null)
                            {
                                creditDetailAdds.Add(new CreditDetailPo
                                {
                                    OriginDetailId = originDetailId,
                                    Amount = item.Value,
                                    ProductId = saleDetail.ProductId,
                                    CreditId = item.Id,
                                    Id = creditDetailId,
                                    InvoiceAmount = item.Value,
                                    NoInvoiceAmount = 0,
                                    OrderNo = item.OrderNo,
                                    RelateCode = item.RelateCode,
                                    Price = item.Value,
                                    Quantity = 1,
                                    OriginalPrice = item.Value,
                                    ProductNo = saleDetail.ProductNo,
                                    Specification = saleDetail.ProductNo,
                                    ProductName = saleDetail.ProductName,
                                    TaxRate = saleDetail.SalesTaxRate,
                                    PackUnit = saleDetail.PackUnit,
                                    AgentId = saleDetail.AgentId.HasValue ? saleDetail.AgentId.ToString() : "",
                                    CustomerId = saleOutTemps.First().CustomerId.ToString(),
                                    CustomerName = saleOutTemps.First().CustomerName,
                                });
                            }
                            else
                            {
                                creditDetailId = creditDetail.Id;
                                creditDetail.InvoiceAmount = item.Value;
                                creditDetail.NoInvoiceAmount = 0;
                                creditDetail.UpdatedTime = DateTime.Now;
                            }
                            customizeInvoiceCredits.Add(new CustomizeInvoiceCreditPo
                            {
                                CreatedBy = createdBy,
                                CreditCode = item.BillCode,
                                CustomizeInvoiceItemId = itemId,
                                CustomizeInvoiceItemCode = customizeInvoiceItem.Code,
                                CreditDetailAmount = item.Value,
                                Value = item.Value,
                                CustomizeInvoiceDetailAmount = item.Value,
                                Id = Guid.NewGuid(),
                                OriginDetailId = originDetailId,
                                Price = item.Value,
                                Quantity = 1,
                                ProductNo = saleDetail.ProductNo,
                                ProductId = saleDetail.ProductId,
                                CreditDetailId = creditDetailId,
                                CustomizeInvoiceDetailId = customizeInvoiceDetail.Id
                            });
                        }
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"操作失败，原因：{outPut.Msg}");
                    }
                }
                try
                {
                    if (customizeInvoiceClassifys.Any())
                    {
                        await _db.CustomizeInvoiceClassify.AddRangeAsync(customizeInvoiceClassifys);
                    }
                    if (customizeInvoiceItems.Any())
                    {
                        await _db.CustomizeInvoiceItem.AddRangeAsync(customizeInvoiceItems);
                    }
                    if (customizeInvoiceDetails.Any())
                    {
                        await _db.CustomizeInvoiceDetail.AddRangeAsync(customizeInvoiceDetails);
                    }
                    if (customizeInvoiceCredits.Any())
                    {
                        await _db.CustomizeInvoiceCredits.AddRangeAsync(customizeInvoiceCredits);
                    }
                    if (creditDetailAdds.Any())
                    {
                        await _db.CreditDetails.AddRangeAsync(creditDetailAdds);
                    }
                    var retInsert = await _unitOfWork.CommitAsync();
                    if (retInsert >= 0)
                    {
                        (int count, string msg) = await SubmitToKingdeeAsync(customizeInvoiceItems, createdBy, true, "N", "", "", input.IsPushCustomerEmail); //flow=="N" 只做检查
                        if (count <= 0)
                        {
                            await RollbackSubmit(creditAll, customizeInvoiceClassifys, customizeInvoiceItems, customizeInvoiceDetails, customizeInvoiceCredits);
                            ret = BaseResponseData<int>.Failed(500, msg);
                        }
                        else
                        {
                            // 是否推送客户邮箱
                            if (input.IsPushCustomerEmail)
                            {
                                // email
                                (count, msg) = await SubmitToKingdeeAsync(customizeInvoiceItems, createdBy, true, "Y", "", "", input.IsPushCustomerEmail);
                            }
                            else
                            {
                                (count, msg) = await SubmitToKingdeeAsync(customizeInvoiceItems, createdBy, true, "Y");
                            }
                            if (count <= 0)
                            {
                                await RollbackSubmit(creditAll, customizeInvoiceClassifys, customizeInvoiceItems, customizeInvoiceDetails, customizeInvoiceCredits);
                                ret = BaseResponseData<int>.Failed(500, msg);
                            }
                            customizeInvoiceClassifys.ForEach(p => p.Status = CustomizeInvoiceStatusEnum.WaitInvoice);
                            _db.UpdateRange(customizeInvoiceClassifys);
                            await _unitOfWork.CommitAsync();
                        }
                    }
                }
                catch (Exception ex)
                {
                    await RollbackSubmit(creditAll, customizeInvoiceClassifys, customizeInvoiceItems, customizeInvoiceDetails, customizeInvoiceCredits);
                    return BaseResponseData<int>.Failed(500, $"操作失败，原因：{ex.Message}");
                }
                return ret;
            }
            else
            {
                return BaseResponseData<int>.Failed(500, $"操作失败，原因：没有找到满足该公司和项目的未开过票的服务费应收！");
            }

        }

        private async Task RollbackSubmit(List<CreditPo> credits,
            List<CustomizeInvoiceClassifyPo> invoiceClassify,
            List<CustomizeInvoiceItemPo> customizeInvoiceItem,
            List<CustomizeInvoiceDetailPo> customizeInvoiceDetail,
            List<CustomizeInvoiceCreditPo> customizeInvoiceCredits)
        {
            _db.CustomizeInvoiceClassify.RemoveRange(invoiceClassify);
            _db.CustomizeInvoiceItem.RemoveRange(customizeInvoiceItem);
            _db.CustomizeInvoiceDetail.RemoveRange(customizeInvoiceDetail);
            _db.CustomizeInvoiceCredits.RemoveRange(customizeInvoiceCredits);
            var creditIdsTemp = credits.Select(p => p.Id).ToList();
            var creditDetaildbs2 = await _db.CreditDetails.Where(p => creditIdsTemp.Contains(p.CreditId)).ToListAsync();
            foreach (var item in credits)
            {
                item.InvoiceStatus = InvoiceStatusEnum.noninvoice;
            }
            foreach (var creditDetail in creditDetaildbs2)
            {
                creditDetail.InvoiceAmount = 0;
                creditDetail.NoInvoiceAmount = creditDetail.Amount;
                creditDetail.UpdatedTime = DateTime.Now;
            }
            await _unitOfWork.CommitAsync();
        }

        /// <summary>
        /// 运营提交开票退回 - 金蝶
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ReturnCustomizeInvoice(KindeeCustomizeInvoiceInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功!");
            var customizeInvoiceItems = await _db.CustomizeInvoiceItem.Where(x => input.Codes.Contains(x.Code)).ToListAsync();
            if (customizeInvoiceItems.Count == 0)
            {
                ret = BaseResponseData<int>.Failed(500, "未找到开票申请单!");
                return ret;
            }
            var creditBillCodes = new List<string>();
            var customizeInvoiceItemIds = customizeInvoiceItems.Select(p => p.Id).ToList();
            var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => customizeInvoiceItemIds.Contains(p.CustomizeInvoiceItemId)).ToListAsync();

            if (customizeInvoiceCredits.Any())
            {
                foreach (var customizeInvoiceItem in customizeInvoiceItems)
                {
                    customizeInvoiceItem.Status = CustomizeInvoiceStatusEnum.Cancel;
                    var customizeInvoiceCreditTemps = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).ToList();

                    var creditDetailIds = customizeInvoiceCreditTemps.Select(p => p.CreditDetailId).Distinct().ToList();
                    var creditDetails = await _db.CreditDetails
                                       .Where(p => creditDetailIds.Contains(p.Id)).Distinct().ToListAsync();
                    var creditIds = creditDetails.Select(p => p.CreditId).ToList();
                    if (creditDetails.Any())
                    {
                        if (string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                        {
                            foreach (var subitem in creditDetails)
                            {
                                var invoiceAmount = customizeInvoiceCreditTemps.Where(p => p.CreditDetailId == subitem.Id).Sum(p => p.CustomizeInvoiceDetailAmount);
                                var totalInvoiceAmount = subitem.InvoiceAmount.HasValue ? Math.Abs(subitem.InvoiceAmount.Value) - Math.Abs(invoiceAmount) : Math.Abs(invoiceAmount);
                                if (totalInvoiceAmount < 0)
                                {
                                    totalInvoiceAmount = 0;
                                }
                                if (invoiceAmount == 0 && subitem.Amount != 0M)
                                {
                                    totalInvoiceAmount = 0;
                                }
                                subitem.InvoiceAmount = subitem.Amount.Value > 0 ? totalInvoiceAmount : -totalInvoiceAmount;
                                subitem.NoInvoiceAmount = subitem.Amount > 0 ? Math.Abs(subitem.Amount.Value) - Math.Abs(subitem.InvoiceAmount.Value) : -(Math.Abs(subitem.Amount.Value) - Math.Abs(subitem.InvoiceAmount.Value));
                                //subitem.Credit.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                            }

                            var credits = await _db.Credits.Where(p => creditIds.Contains(p.Id)).ToListAsync();
                            foreach (var item in credits)
                            {
                                item.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                            }
                        }
                        else
                        {
                            var invoiceItemOld = await _db.CustomizeInvoiceItem.Where(c => customizeInvoiceItem.RelationCode.Equals(c.Code)).FirstOrDefaultAsync();
                            if (invoiceItemOld != null)
                            {
                                var otherRedCustomizeInvoiceItem = await _db.CustomizeInvoiceItem.Where(p => p.RelationCode == customizeInvoiceItem.RelationCode && p.Id != customizeInvoiceItem.Id && p.Status != CustomizeInvoiceStatusEnum.Cancel).ToListAsync();
                                if (invoiceItemOld.ChangedStatus.HasValue)
                                {
                                    if (otherRedCustomizeInvoiceItem.Any())
                                    {
                                        invoiceItemOld.ChangedStatus = CustomizeInvoiceChangedStatusEnum.PartRedOffset;
                                    }
                                    else
                                    {
                                        invoiceItemOld.ChangedStatus = null;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                var customizeInvoiceSubDetails = await _db.CustomizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId.HasValue &&
                                                        customizeInvoiceItemIds.Contains(p.CustomizeInvoiceItemId.Value)).ToListAsync();
                var creditBillCodeAll = customizeInvoiceSubDetails.Select(p => p.CreditBillCode).ToList();
                var credits = await _db.Credits.Where(p => creditBillCodeAll.Contains(p.BillCode)).ToListAsync();
                foreach (var customizeInvoiceItem in customizeInvoiceItems)
                {
                    customizeInvoiceItem.Status = CustomizeInvoiceStatusEnum.Cancel;
                    if (string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                    {
                        creditBillCodes = customizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).Select(p => p.CreditBillCode).ToList();
                        var creditparts = credits.Where(p => creditBillCodes.Contains(p.BillCode));

                        foreach (var item in creditparts)
                        {
                            item.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                        }
                    }
                    else
                    {
                        var invoiceItemOld = await _db.CustomizeInvoiceItem.Where(c => customizeInvoiceItem.RelationCode.Equals(c.Code)).FirstOrDefaultAsync();
                        if (invoiceItemOld != null)
                        {
                            var otherRedCustomizeInvoiceItem = await _db.CustomizeInvoiceItem.Where(p => p.RelationCode == customizeInvoiceItem.RelationCode && p.Id != customizeInvoiceItem.Id && p.Status != CustomizeInvoiceStatusEnum.Cancel).ToListAsync();
                            if (invoiceItemOld.ChangedStatus.HasValue)
                            {
                                if (otherRedCustomizeInvoiceItem.Any())
                                {
                                    invoiceItemOld.ChangedStatus = CustomizeInvoiceChangedStatusEnum.PartRedOffset;
                                }
                                else
                                {
                                    invoiceItemOld.ChangedStatus = null;
                                }
                            }
                        }
                    }
                }
            }
            await _unitOfWork.CommitAsync();
            return ret;
        }

        /// <summary>
        /// 同步税收分类编码
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SyncTaxTypeNo(SyncTaxTypeNoInput input)
        {
            try
            {
                if (input.List == null || !input.List.Any())
                {
                    return BaseResponseData<int>.Failed(500, $"未获取到明细数据!");
                }
                var ret2 = await CheckCustomizeInvoiceDetail(input.List.FirstOrDefault().CustomizeInvoiceItemId);
                if (ret2.Code != CodeStatusEnum.Success)
                {
                    return BaseResponseData<int>.Failed(500, ret2.Message);
                }
                var details = await _db.CustomizeInvoiceDetail.Where(x => x.CustomizeInvoiceItemId == input.List.FirstOrDefault().CustomizeInvoiceItemId).ToListAsync();
                if (details.Count == 0)
                {
                    return BaseResponseData<int>.Failed(500, $"未获取到明细数据!");
                }
                var inputs = new List<ProductIdAndAgentIdInput>();
                foreach (var item in details)
                {
                    if (!string.IsNullOrEmpty(item.AgentId) && item.AgentId.Contains(','))
                    {
                        foreach (var agentId in item.AgentId.Split(',').ToList())
                        {
                            if (!string.IsNullOrEmpty(agentId))
                            {
                                inputs.Add(new ProductIdAndAgentIdInput()
                                {
                                    agentId = agentId,
                                    productId = item.ProductId
                                });
                            }
                        }
                    }
                    else
                    {
                        inputs.Add(new ProductIdAndAgentIdInput()
                        {
                            agentId = item.AgentId,
                            productId = item.ProductId
                        });
                    }
                }
                inputs = inputs.Distinct().ToList();
                var productList = await _bDSApiClient.GetProductbyIdsAsync(inputs.Select(p => p.productId.Value).ToList(), input.CompanyId.ToString(), inputs);

                foreach (var item in details)
                {

                    var product = productList.FirstOrDefault(p => p.productId == item.ProductId);
                    if (product != null)
                    {
                        if (product.ifTool != 3)
                        {
                            item.TaxTypeNo = product.taxClassCode;
                        }
                        else
                        {
                            item.TaxTypeNo = product.taxControlCode;
                        }
                    }
                }
                await _unitOfWork.CommitAsync();
                return BaseResponseData<int>.Success("同步成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 设置为另一个开票分类
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SetAnotherCic(SetAnotherCicInput input)
        {
            try
            {
                if (input.List == null || !input.List.Any())
                {
                    return BaseResponseData<string>.Failed(500, $"未获取到明细数据!");
                }
                // 获取原始开票分类
                var cicId = input.List[0].CustomizeInvoiceClassifyId;
                var originCic = await _db.CustomizeInvoiceClassify.FirstOrDefaultAsync(x => x.Id == cicId);
                if (originCic == null)
                {
                    return BaseResponseData<string>.Failed(500, $"开票分类不存在或已被删除!");
                }
                if (originCic.Status != CustomizeInvoiceStatusEnum.WaitSubmit)
                {
                    return BaseResponseData<string>.Failed(500, $"待提交才能设置为另一个开票分类!");
                }
                if (originCic.Classify == CustomizeInvoiceClassifyEnum.Credit && !string.IsNullOrEmpty(originCic.RelationCode))
                {
                    return BaseResponseData<string>.Failed(500, $"拆出的开票分类不允许二次拆分!");
                }
                // 复制给另一个开票分类单
                var newCic = originCic.Adapt<CustomizeInvoiceClassifyPo>();
                newCic.CreatedBy = input.CreateBy ??= originCic.CreatedBy;
                // 单号更改
                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { originCic.CompanyId.ToString() }
                })).FirstOrDefault();
                var outPutClassify = await _codeGenClient.ApplyCode(new ApplyCodeInput
                {
                    BusinessArea = originCic.BillCode.Split('-')[0],
                    BillType = "CIC",
                    SysMonth = companyInfo.sysMonth,
                    DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                    Num = 1,
                    CompanyCode = companyInfo.nameCode
                });
                var classifId = Guid.NewGuid();
                newCic.Id = classifId;
                newCic.BillCode = outPutClassify.Codes.First();
                newCic.OARequestId = null;
                newCic.RelationCode = originCic.BillCode;
                // 绑定为拆分关系
                _db.CustomizeInvoiceClassify.Update(originCic);
                _db.CustomizeInvoiceClassify.Add(newCic);
                // 查询开票申请单数据
                var ciiIds = input.List.Select(x => x.Id).ToList();
                // 跟踪实例直接更改对应关系
                var ciis = await _db.CustomizeInvoiceItem.Where(x => ciiIds.Contains(x.Id)).ToListAsync();
                foreach (var cii in ciis)
                {
                    cii.CustomizeInvoiceClassifyId = classifId;
                    cii.UpdatedBy = input.CreateBy;
                    cii.UpdatedTime = DateTime.UtcNow;
                }
                await _unitOfWork.CommitAsync();
                return BaseResponseData<string>.Success("设置成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 合并开票单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> MergeCii(SetAnotherCicInput input)
        {
            try
            {
                if (input.List == null || !input.List.Any())
                {
                    return BaseResponseData<string>.Failed(500, $"未获取到明细数据!");
                }
                // 获取原始开票分类
                var cicId = input.List[0].CustomizeInvoiceClassifyId;
                var originCic = await _db.CustomizeInvoiceClassify.FirstOrDefaultAsync(x => x.Id == cicId);
                if (originCic == null)
                {
                    return BaseResponseData<string>.Failed(500, $"开票分类不存在或已被删除!");
                }
                if (originCic.Status != CustomizeInvoiceStatusEnum.WaitSubmit)
                {
                    return BaseResponseData<string>.Failed(500, $"所有开票申请为待提交状态才可合并!");
                }
                var ciiCodes = input.List.Select(x => x.Code).Distinct().ToList();
                var customizeInvoiceItems = await _db.CustomizeInvoiceItem.Include(x => x.CustomizeInvoiceDetail).Where(x => ciiCodes.Contains(x.Code)).ToListAsync();
                if (ciiCodes.Count() != customizeInvoiceItems.Select(p => p.Code).Distinct().Count())
                {
                    return BaseResponseData<string>.Failed(500, $"开票申请单不存在或已被删除!");
                }
                if (customizeInvoiceItems.Select(p => p.CustomizeInvoiceClassifyId).Distinct().Count() > 1 || cicId != customizeInvoiceItems.First().CustomizeInvoiceClassifyId)
                {
                    return BaseResponseData<string>.Failed(500, $"开票申请单不是同一个开票分类!");
                }
                if (customizeInvoiceItems != null && customizeInvoiceItems.Any())
                {
                    //找出时间最早的开票单合并
                    var originCustomizeInvoiceItem = customizeInvoiceItems.OrderBy(x => x.CreatedTime).First();
                    var otherCustomizeInvoiceItems = customizeInvoiceItems.Where(x => x.Id != originCustomizeInvoiceItem.Id).ToList();
                    var otherCustomizeInvoiceItemIds = otherCustomizeInvoiceItems.Select(p => p.Id).ToList();
                    foreach (var otherCii in otherCustomizeInvoiceItems)
                    {
                        if (otherCii.Status != CustomizeInvoiceStatusEnum.WaitSubmit)
                        {
                            return BaseResponseData<string>.Failed(500, $"待提交才能合并开票单!");
                        }
                        if (otherCii.CustomizeInvoiceDetail == null || !otherCii.CustomizeInvoiceDetail.Any())
                        {
                            continue;
                        }
                        var otherCiiDetails = otherCii.CustomizeInvoiceDetail.ToList();
                        foreach (var otherCiiDetail in otherCiiDetails)
                        {
                            if (originCustomizeInvoiceItem != null)
                            {
                                // 更换详情绑定关系
                                otherCiiDetail.CustomizeInvoiceItemId = originCustomizeInvoiceItem.Id;
                                _db.CustomizeInvoiceDetail.Update(otherCiiDetail);
                            }
                        }
                    }
                    var otherCustomizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => otherCustomizeInvoiceItemIds.Contains(p.CustomizeInvoiceItemId)).ToListAsync();
                    foreach (var item in otherCustomizeInvoiceCredits)
                    {
                        item.CustomizeInvoiceItemId = originCustomizeInvoiceItem.Id;
                        item.CustomizeInvoiceItemCode = originCustomizeInvoiceItem.Code;
                    }
                    originCustomizeInvoiceItem.InvoiceTotalAmount = customizeInvoiceItems.SelectMany(p => p.CustomizeInvoiceDetail).Sum(p => p.Value);
                    // 删除其它开票单
                    _db.CustomizeInvoiceItem.RemoveRange(otherCustomizeInvoiceItems);
                }
                await _unitOfWork.CommitAsync();
                return BaseResponseData<string>.Success("合并成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 开票明细导入
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="customizeInvoiceItemId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportCustomizeInvoiceDetail(Guid fileId, Guid customizeInvoiceItemId, string userName)
        {
            var ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Success("操作成功！");
            try
            {
                var ret2 = await CheckCustomizeInvoiceDetail(customizeInvoiceItemId);
                if (ret2.Code != CodeStatusEnum.Success)
                {
                    return BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, ret2.Message);
                }
                var customizeInvoiceItem = await _customizeInvoiceItemRepository.GetWithNoTrackAsync(customizeInvoiceItemId);
                // 获取 CustomizeInvoiceDetail 数据
                var customizeInvoiceDetails = await _db.CustomizeInvoiceDetail.AsNoTracking().Where(t => t.CustomizeInvoiceItemId == customizeInvoiceItemId).ToListAsync();

                // 获取 CustomizeInvoiceCredit 数据
                var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits
                    .Where(c => customizeInvoiceDetails.Select(d => d.Id).Contains(c.CustomizeInvoiceDetailId))
                    .ToListAsync();
                if (customizeInvoiceItem == null || customizeInvoiceDetails == null || customizeInvoiceDetails.Count() == 0)
                {
                    ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "开票明细为空");
                    return ret;
                }
                var creditsCods = customizeInvoiceCredits.Select(p => p.CreditCode).Distinct().ToList();
                var credits = await _db.Credits.Where(p => creditsCods.Contains(p.BillCode)).ToListAsync();

                int failCount = 0;
                var stream = await _fileGatewayClient.GetTempFileStreamAsync(fileId);
                var excelDetailList = new List<CustomizeInvoiceDetailExcelModel>();
                var newCustomizeInvoiceDetails = new List<CustomizeInvoiceDetail>();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    stream.Close();
                    var worksheet = package.Workbook.Worksheets[0]; //获取第一个sheet 
                                                                    //去掉最后的空白行
                    worksheet.TrimLastEmptyRows();
                    int rows = worksheet.Dimension.End.Row;   //获取worksheet的行数 
                    for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                    {
                        var errText = string.Empty;
                        int.TryParse(worksheet.Cells[index, 1].Text.Trim(), out int sort); //开票序号
                        decimal.TryParse(worksheet.Cells[index, 5].Text.Trim(), out decimal quantity); //数量
                        decimal.TryParse(worksheet.Cells[index, 4].Text.Trim(), out decimal price);//单价
                        if (string.IsNullOrEmpty(worksheet.Cells[index, 1].Text.Trim()) || string.IsNullOrEmpty(worksheet.Cells[index, 2].Text.Trim()) || string.IsNullOrEmpty(worksheet.Cells[index, 3].Text.Trim()) || string.IsNullOrEmpty(worksheet.Cells[index, 4].Text.Trim()) || string.IsNullOrEmpty(worksheet.Cells[index, 5].Text.Trim()))
                        {
                            errText = "请输入完整的数据";
                            failCount++;
                        }
                        else if (excelDetailList.Select(p => p.Sort).ToList().Contains(sort))
                        {
                            errText = "存在重复的开票序号";
                            failCount++;
                        }
                        else if (Math.Round(quantity, 2) <= 0)
                        {
                            errText = "数量只支持正数(保留两位小数)";
                            failCount++;
                        }
                        else if (Math.Round(price, 4) < 0)
                        {
                            errText = "单价不支持负数(保留四位小数)";
                            failCount++;
                        }
                        excelDetailList.Add(new CustomizeInvoiceDetailExcelModel()
                        {
                            Sort = sort, //开票序号
                            ProductNo = worksheet.Cells[index, 2].Text.Trim(),//原始规格
                            Specification = worksheet.Cells[index, 3].Text.Trim(), //开票规格
                            Price = price,
                            Quantity = quantity,
                            ErrMsg = errText, //错误信息
                        });

                    }
                    if (excelDetailList == null || excelDetailList.Count() == 0)
                    {
                        ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "没有获取到表格的开票明细");
                        return ret;
                    }

                    foreach (var detail in customizeInvoiceDetails)
                    {
                        if (detail.Quantity < 0)
                        {
                            ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "原始开票明细中序号：" + detail.Sort + "数量为负数，不允许导入开票明细");
                            return ret;
                        }
                        if (detail.Price < 0)
                        {
                            ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "原始开票明细中序号：" + detail.Sort + "单价为负数，不允许导入开票明细");
                            return ret;
                        }
                        if (detail.Tag == "折扣行")
                        {
                            ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "原始开票明细中序号：" + detail.Sort + "折扣行，不允许导入开票明细");
                            return ret;
                        }
                        var creditBillCodes = customizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == detail.Id).Select(p => p.CreditCode).Distinct().ToList();
                        if (creditBillCodes.Count() > 1) //如果开票明细中一行应收单号
                        {
                            ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "原始开票明细中序号：" + detail.Sort + "有多个应收，不允许导入开票明细");
                            return ret;
                        }
                        if (excelDetailList.Where(p => p.ProductNo == detail.ProductNo && p.Price == detail.Price).ToList().Count == 0) //用原始明细去匹配表格明细差异（唯一性）
                        {
                            if (!worksheet.Cells[excelDetailList.Count + 2, 6].Text.Contains("原始规格：" + detail.ProductNo + ",单价：" + detail.Price))
                            {
                                worksheet.Cells[excelDetailList.Count + 2, 6].Value = worksheet.Cells[excelDetailList.Count + 2, 6].Text + "导入的开票明细缺少原始开票明细数据，原始规格：" + detail.ProductNo + ",单价：" + detail.Price + "\r\n";
                            }
                            failCount++;
                        }
                    }

                    var groupList = excelDetailList.GroupBy(p => new { p.ProductNo, p.Price });//用表格去匹配原始明细差异（唯一性）
                    foreach (var group in groupList)
                    {
                        //校验excel中 按原始规格+单价分组 ，得到数量是否与 开票明细中的原始数量 （规格+单价分组）是否一致，不一致报错
                        var excelDetailGroups = excelDetailList.Where(p => p.ProductNo == group.Key.ProductNo && p.Price == group.Key.Price).ToList();
                        var excelDetailGroupsZore = excelDetailGroups.Where(p => p.Price == 0).ToList();
                        var detailListGroups = customizeInvoiceDetails.Where(p => p.ProductNo == group.Key.ProductNo && p.Price == group.Key.Price).ToList();
                        if (detailListGroups.Sum(p => p.Quantity) == 0)
                        {
                            foreach (var excelDetail in excelDetailGroups)
                            {
                                if (string.IsNullOrEmpty(excelDetail.ErrMsg))
                                {
                                    excelDetail.ErrMsg = "原始明细中不存在该单价原始规格数据";
                                    failCount++;
                                }
                            }
                        }
                        else if (excelDetailGroups.Sum(p => p.Quantity) != detailListGroups.Sum(p => p.Quantity))
                        {
                            foreach (var excelDetail in excelDetailGroups)
                            {
                                if (string.IsNullOrEmpty(excelDetail.ErrMsg))
                                {
                                    excelDetail.ErrMsg = "导入的开票明细数据按原始规格和单价分组得到的总数量和原始开票明细不一致";
                                    failCount++;
                                }
                            }
                        }
                        else if (excelDetailGroupsZore.Count() > 1)
                        {

                            foreach (var excelDetail in excelDetailGroupsZore)
                            {
                                if (string.IsNullOrEmpty(excelDetail.ErrMsg))
                                {
                                    excelDetail.ErrMsg = "单价为0的明细不允许拆分";
                                    failCount++;
                                }
                            }
                        }
                    }
                    if (failCount > 0)
                    {
                        for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                        {
                            int.TryParse(worksheet.Cells[index, 1].Text.Trim(), out int sort); //开票序号
                            var errMsg = excelDetailList.Where(p => p.Sort == sort).LastOrDefault().ErrMsg;
                            if (!string.IsNullOrEmpty(errMsg))//出错信息
                            {
                                worksheet.Cells[index, 6].Value = errMsg;
                            }
                        }
                        worksheet.Column(6).Style.Font.Color.SetColor(Color.Red);
                        //生成错误报告文件
                        MemoryStream msFailReport = new MemoryStream(package.GetAsByteArray());
                        ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Success("导入失败！");
                        ret.Data = new CustomizeInvoiceDetailExcelOutput()
                        {
                            FailReportFileId = await _fileGatewayClient.UploadTempFileContentAsync(msFailReport, "开票明细导入错误数据.xlsx"),
                            FailNumber = excelDetailList.Count(),
                            SuccessNumber = 0,
                            Total = excelDetailList.Count(),
                        };
                        return ret;
                    }
                    else
                    {
                        var newCustomizeInvoiceCredits = new List<CustomizeInvoiceCreditPo>();

                        int index = 1;
                        foreach (var item in excelDetailList.OrderBy(p => p.Sort))//按排序循环插入
                        {
                            var details = customizeInvoiceDetails.Where(p => p.ProductNo == item.ProductNo && p.Price == item.Price).ToList();
                            if (details == null || customizeInvoiceCredits == null)
                            {
                                throw new ArgumentNullException("details or customizeInvoiceCredits cannot be null");
                            }

                            var detail = details.FirstOrDefault();
                            var newCustomizeInvoiceDetailId = Guid.NewGuid();

                            // 找到原始开票明细Ids
                            var customizeInvoiceDetailIds = new HashSet<Guid>(details.Select(p => p.Id));

                            // 找到原始开票与原始开票明细关联
                            var customizeInvoiceCreditsTemp = customizeInvoiceCredits
                                .Where(p => customizeInvoiceDetailIds.Contains(p.CustomizeInvoiceDetailId))
                                .OrderBy(p => p.CustomizeInvoiceDetailAmount)
                                .ToList();
                            //剩余金额
                            var remainValue = Math.Abs(item.Value);
                            foreach (var subitem in customizeInvoiceCreditsTemp)
                            {
                                if (remainValue < 0)
                                {
                                    break;
                                }
                                var absSubitemValue = Math.Abs(subitem.CustomizeInvoiceDetailAmount);
                                if (remainValue >= absSubitemValue)
                                {
                                    subitem.CustomizeInvoiceDetailId = newCustomizeInvoiceDetailId;
                                }
                                else
                                {
                                    //创建新开票明细
                                    var newCustomizeInvoiceCredit = subitem.DeepClone();
                                    var newCustomizeInvoiceCreditId = Guid.NewGuid();
                                    newCustomizeInvoiceCredit.CustomizeInvoiceDetailId = newCustomizeInvoiceDetailId;
                                    newCustomizeInvoiceCredit.Id = newCustomizeInvoiceCreditId;
                                    newCustomizeInvoiceCredit.CustomizeInvoiceDetailAmount = remainValue;
                                    newCustomizeInvoiceCredit.Quantity = remainValue / item.Price;
                                    newCustomizeInvoiceCredits.Add(newCustomizeInvoiceCredit);

                                    //修改原开票明细金额和数量
                                    subitem.CustomizeInvoiceDetailAmount = absSubitemValue - remainValue;
                                    subitem.Quantity -= newCustomizeInvoiceCredit.Quantity;
                                }
                                remainValue = remainValue - absSubitemValue;
                            }
                            newCustomizeInvoiceCredits = newCustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailAmount != 0).ToList();
                            var thisCustomizeInvoiceCredits = customizeInvoiceCreditsTemp.Where(p => p.CustomizeInvoiceDetailId == newCustomizeInvoiceDetailId).Select(p => new { p.CreditCode, p.CustomizeInvoiceDetailAmount }).ToList();
                            var thisCustomizeInvoiceCredits2 = newCustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceDetailId == newCustomizeInvoiceDetailId).Select(p => new { p.CreditCode, p.CustomizeInvoiceDetailAmount }).ToList();
                            thisCustomizeInvoiceCredits.AddRange(thisCustomizeInvoiceCredits2);
                            var creditBillCode = thisCustomizeInvoiceCredits.Select(p => p.CreditCode).Distinct().ToList();
                            var newCustomizeInvoiceDetailValue = thisCustomizeInvoiceCredits.Sum(p => p.CustomizeInvoiceDetailAmount);
                            var creditsTemp = credits.Where(p => creditBillCode.Contains(p.BillCode)).ToList();
                            var orderNos = creditsTemp.Select(p => p.OrderNo).Distinct().ToList();
                            var relateCode = creditsTemp.Select(p => p.RelateCode).Distinct().ToList();
                            if (index == excelDetailList.Count)
                            {
                                newCustomizeInvoiceDetailValue = customizeInvoiceItem.InvoiceTotalAmount - newCustomizeInvoiceDetails.Sum(p => p.Value);
                            }
                            newCustomizeInvoiceDetails.Add(new CustomizeInvoiceDetail()
                            {
                                ProductNo = item.ProductNo,
                                ProductName = detail.ProductName,
                                Quantity = item.Quantity,
                                Price = item.Price,
                                Value = newCustomizeInvoiceDetailValue,
                                Sort = item.Sort,
                                CreatedTime = DateTimeOffset.Now,
                                CreditBillCode = string.Join(",", creditBillCode),
                                CustomerId = detail.CustomerId,
                                CustomerName = detail.CustomerName,
                                CustomizeInvoiceIndex = detail.CustomizeInvoiceIndex,
                                CustomizeInvoiceItemId = detail.CustomizeInvoiceItemId,
                                OriginalPrice = detail.OriginalPrice,
                                OriginDetailId = detail.OriginDetailId,
                                OrderNo = string.Join(",", orderNos),
                                OriginProductName = detail.OriginProductName,
                                OriginSpecification = item.ProductNo,
                                PackUnit = detail.PackUnit,
                                ProductId = detail.ProductId,
                                Specification = item.Specification,
                                RelateCode = string.Join(",", relateCode),
                                Tag = detail.Tag,          //金额=数量*单价， 税额 = (金额 / (1+税率/100)) * 税率/100
                                TaxAmount = Math.Round(newCustomizeInvoiceDetailValue / (1 + (detail.TaxRate / 100)) * (detail.TaxRate / 100), 2),
                                TaxRate = detail.TaxRate,
                                TaxTypeNo = detail.TaxTypeNo,
                                CreatedBy = userName,
                                Id = newCustomizeInvoiceDetailId
                            });
                            index++;
                        }
                        if (newCustomizeInvoiceCredits.Any() && newCustomizeInvoiceCredits.Count() > 0)
                        {
                            await _db.CustomizeInvoiceCredits.AddRangeAsync(newCustomizeInvoiceCredits);
                        }
                        ret.Data = new CustomizeInvoiceDetailExcelOutput()
                        {
                            FailNumber = 0,
                            SuccessNumber = newCustomizeInvoiceDetails.Count(),
                            Total = newCustomizeInvoiceDetails.Count(),
                        };
                    }
                }

                _db.CustomizeInvoiceDetail.RemoveRange(customizeInvoiceDetails);//删除原来的开票明细
                customizeInvoiceItem.UpdateBy(userName);
                await _customizeInvoiceItemRepository.UpdateAsync(customizeInvoiceItem);
                await _customizeInvoiceDetailRepository.AddManyAsync(newCustomizeInvoiceDetails); //加上新的开票明细
                await _unitOfWork.CommitAsync();
                return ret;
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "导入失败！错误信息：" + ex.Message);
                return ret;
            }
        }

        /// <summary>
        /// 批量开票(导入并提交)
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportBatchInvoicing(Guid fileId, string userName, Guid? userId)
        {
            var ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Success("操作成功！");
            var successMsg = string.Empty;
            try
            {
                int failCount = 0;
                var stream = await _fileGatewayClient.GetTempFileStreamAsync(fileId);
                var excelDetailList = new List<CustomizeInvoicingExcelModel>();

                var newCustomizeInvoiceDetail = new List<CustomizeInvoiceDetail>();

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    stream.Close();
                    var worksheet = package.Workbook.Worksheets[0];
                    worksheet.TrimLastEmptyRows();
                    int rows = worksheet.Dimension.End.Row;
                    for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                    {
                        var errText = string.Empty;
                        string c = worksheet.Cells[index, 1].Text.TrimStart().TrimEnd(); //公司
                        string o = worksheet.Cells[index, 2].Text.TrimStart().TrimEnd(); //订单
                        if (string.IsNullOrEmpty(worksheet.Cells[index, 1].Text.Trim()) || string.IsNullOrEmpty(worksheet.Cells[index, 2].Text.Trim()))
                        {
                            errText = "请输入完整的数据";
                            failCount++;
                        }
                        else if (excelDetailList.Select(p => p.OrderNo).ToList().Contains(o))
                        {
                            errText = "存在重复的订单号";
                            failCount++;
                        }
                        excelDetailList.Add(new CustomizeInvoicingExcelModel()
                        {
                            CompanyName = c,
                            OrderNo = o,
                            ErrMsg = errText, //错误信息
                        });
                    }
                    if (excelDetailList == null || excelDetailList.Count() == 0)
                    {
                        ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "没有获取到表格的开票信息");
                        return ret;
                    }
                    if (excelDetailList.Select(x => x.CompanyName).Distinct().Count() > 1)
                    {
                        excelDetailList.ForEach(x =>
                        {
                            x.ErrMsg = "仅支持同一个公司的导入开票";
                        });
                        failCount = excelDetailList.Count();
                    }
                    //拉取到订单的原始应收明细(公司+客户+应收单号)
                    var orderNos = excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).Select(x => x.OrderNo).ToList();
                    var companyName = excelDetailList[0].CompanyName;
                    //获取公司信息
                    var companylist = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        names = new List<string> { companyName }
                    }));
                    var companyInfo = companylist.Where(x => x.companyName == companyName).FirstOrDefault();
                    if (companyInfo == null)
                    {
                        excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                        {
                            x.ErrMsg = "公司错误";
                        });
                        failCount = excelDetailList.Count();
                    }
                    //根据公司和订单号查询到未开票且需要开票的应收（不包含订单修订）
                    var checkCredits = await _db.Credits.Where(x => !string.IsNullOrEmpty(x.OrderNo) && x.CompanyName == companyName && orderNos.Any(p => p == x.OrderNo)).ToListAsync();
                    //错误信息小于总条数则进行下一步，等于总条数直接抛出错误
                    if (excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count() < excelDetailList.Count())
                    {
                        if (checkCredits == null || !checkCredits.Any())
                        {
                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                            {
                                x.ErrMsg = "根据公司和订单没有查询到应收数据，请检查订单号或公司是否正确";
                            });
                            failCount = excelDetailList.Count();
                        }
                        //查询当前登录人是否有操作该公司的权限
                        var strategryquery = new StrategyQueryInput() { userId = userId, functionUri = "metadata://fam" };
                        var strategry = await _pCApiClient.GetStrategyAsync(strategryquery);
                        if (strategry != null && strategry.RowStrategies.Count > 0)
                        {
                            var rowStrategies = strategry.RowStrategies;
                            if (!rowStrategies.Keys.Contains("company"))
                            {
                                excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                {
                                    x.ErrMsg = "您没有操作当前公司的权限";
                                });
                                failCount = excelDetailList.Count();
                            }
                            foreach (var key in rowStrategies.Keys)
                            {
                                if (key.ToLower() == "company")
                                {
                                    if (!rowStrategies[key].Any(s => s == "@all"))
                                    {
                                        var strategList = rowStrategies[key].Where(x => x == companyInfo.companyId).Select(s => Guid.Parse(s.ToLower())).ToList();
                                        if (strategList == null || !strategList.Any())
                                        {
                                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                            {
                                                x.ErrMsg = "您没有操作当前公司的权限";
                                            });
                                            failCount = excelDetailList.Count();
                                            break;
                                        }
                                    }
                                }
                                if (key.ToLower() == "customer")
                                {
                                    //查询当前登录人是否有操作该客户的权限
                                    if (!rowStrategies[key].Any(z => z == "@all"))
                                    {
                                        var customrtIds = checkCredits.Select(x => x.CustomerId.ToString().ToUpper()).ToList();
                                        if (customrtIds != null && customrtIds.Any())
                                        {
                                            var strategList = rowStrategies[key].Where(x => customrtIds.ToHashSet().Contains(x)).ToHashSet();
                                            if (strategList == null || !strategList.Any())
                                            {
                                                excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                                {
                                                    x.ErrMsg = "您没有操作当前客户的权限";
                                                });
                                                failCount = excelDetailList.Count();
                                                break;
                                            }
                                            else
                                            {
                                                var noexistOrders = checkCredits.Where(x => x.CustomerId.HasValue && !strategList.ToHashSet().Contains(x.CustomerId.Value.ToString().ToUpper())).Select(x => x.OrderNo).ToList();
                                                if (noexistOrders != null && noexistOrders.Any())
                                                {
                                                    excelDetailList.Where(x => noexistOrders.ToHashSet().Contains(x.OrderNo)).ForEach(x =>
                                                    {
                                                        x.ErrMsg = "您没有操作当前客户的权限";
                                                    });
                                                    failCount += excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && noexistOrders.ToHashSet().Contains(x.OrderNo)).Count();
                                                    continue;
                                                }
                                            }
                                        }
                                    }
                                }
                                if (key.ToLower() == "accountingdept")
                                {
                                    if (!rowStrategies[key].Any(s => s == "@all"))
                                    {
                                        var accountingdeptIds = checkCredits.Select(x => x.BusinessDeptId).ToList();
                                        if (accountingdeptIds != null && accountingdeptIds.Any())
                                        {
                                            var strategList = rowStrategies[key].Where(x => accountingdeptIds.ToHashSet().Contains(x)).ToHashSet();
                                            if (strategList == null || !strategList.Any())
                                            {
                                                excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                                {
                                                    x.ErrMsg = "您没有操作当前部门的权限";
                                                });
                                                failCount = excelDetailList.Count();
                                                continue;
                                            }
                                            else
                                            {
                                                var noexistOrders = checkCredits.Where(x => !string.IsNullOrEmpty(x.BusinessDeptId) && !strategList.Contains(x.BusinessDeptId)).Select(x => x.OrderNo).ToHashSet();
                                                if (noexistOrders != null && noexistOrders.Any())
                                                {
                                                    excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && noexistOrders.ToHashSet().Contains(x.OrderNo)).ForEach(x =>
                                                    {
                                                        x.ErrMsg = "您没有操作当前部门的权限";
                                                    });
                                                    failCount += excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && noexistOrders.ToHashSet().Contains(x.OrderNo)).Count();
                                                    continue;
                                                }
                                            }
                                        }
                                    }
                                }
                                if (key.ToLower() == "project")
                                {
                                    if (!rowStrategies[key].Any(s => s == "@all"))
                                    {
                                        var strategList = rowStrategies[key].ToList();
                                        if (strategList != null && strategList.Any())
                                        {
                                            if (checkCredits != null && checkCredits.Any())
                                            {
                                                var projectCredits = checkCredits.Where(x => x.ProjectId.HasValue && strategList.ToHashSet().Contains(x.ProjectId.Value.ToString().ToUpper()));
                                                if (projectCredits == null || !projectCredits.Any())
                                                {
                                                    excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                                    {
                                                        x.ErrMsg = "您没有操作当前项目的权限";
                                                    });
                                                    failCount = excelDetailList.Count();
                                                    continue;
                                                }
                                                else
                                                {
                                                    foreach (var item in projectCredits)
                                                    {
                                                        if (!strategList.ToHashSet().Contains(item.ProjectId.Value.ToString().ToUpper()))
                                                        {
                                                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && x.OrderNo == item.OrderNo).ForEach(x =>
                                                            {
                                                                x.ErrMsg = "您没有操作当前项目的权限";
                                                            });
                                                            failCount += excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && x.OrderNo == item.OrderNo).Count();
                                                            continue;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                            {
                                                x.ErrMsg = "您没有操作当前项目的权限";
                                            });
                                            failCount = excelDetailList.Count();
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                            {
                                x.ErrMsg = "您没有操作当前公司的权限";
                            });
                            failCount = excelDetailList.Count();
                        }
                    }

                    var ids = checkCredits.Select(x => x.Id).ToList();
                    var invoiceCredits = await _db.InvoiceCredits.Where(x => ids.Any(p => p == x.CreditId)).ToListAsync();
                    var creditDetails = await _db.CreditDetails.Include(x => x.Credit).Where(x => ids.Any(p => p == x.CreditId)).AsNoTracking().ToListAsync();
                    var allCredits = new List<CreditPo>();
                    //错误信息小于总条数则进行下一步，等于总条数直接抛出错误
                    if (excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count() < excelDetailList.Count())
                    {
                        foreach (var item in excelDetailList)
                        {
                            if (!string.IsNullOrEmpty(item.ErrMsg))
                            {
                                continue;
                            }
                            var errCount = 0;
                            var errMsg = string.Empty;
                            var currentlist = checkCredits.Where(x => x.OrderNo == item.OrderNo).ToList();
                            if (currentlist == null || !currentlist.Any())
                            {
                                item.ErrMsg = "根据公司和订单没有查询到应收数据，请检查订单号或公司是否正确";
                                failCount++;
                                continue;
                            }
                            else
                            {
                                var checkbox = new List<CreditPo>();
                                var creditCodes = currentlist.Select(x => x.BillCode).ToList();
                                var creditCodesStr = string.Join(",", creditCodes);
                                var isSaleReviseCount = currentlist.Where(x => x.SaleType == SaleTypeEnum.SaleRevise).Count();
                                if (isSaleReviseCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"不支持订单修订生成的应收导入";
                                    failCount++;
                                    continue;
                                }
                                else if (isSaleReviseCount > 0)
                                {
                                    //errCount += isSaleReviseCount;
                                    errMsg += "不支持订单修订生成的应收";
                                }
                                var isCreditTypeCount = currentlist.Where(x => x.CreditType == CreditTypeEnum.revise || x.CreditType == CreditTypeEnum.selfrevise || x.CreditType == CreditTypeEnum.servicefeerevise || x.CreditType == CreditTypeEnum.origin).Count();
                                if (isCreditTypeCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"不支持订单修订、初始应收或服务费修订生成的应收导入";
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        item.ErrMsg += "或";
                                    }
                                    item.ErrMsg += errMsg;
                                    failCount++;
                                    continue;
                                }
                                else if (isCreditTypeCount > 0)
                                {
                                    // 排除不满足条件的应收
                                    currentlist = currentlist.Where(x => x.CreditType != CreditTypeEnum.revise && x.CreditType != CreditTypeEnum.selfrevise && x.CreditType != CreditTypeEnum.servicefeerevise && x.CreditType != CreditTypeEnum.origin).ToList();
                                    //errCount += isCreditTypeCount;
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        errMsg += "或";
                                    }
                                    errMsg += "初始应收或服务费修订生成的应收";
                                }
                                var isNotNeedInvoiceCount = currentlist.Where(x => x.IsNoNeedInvoice == IsNoNeedInvoiceEnum.NoNeed).Count();
                                if (isNotNeedInvoiceCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"订单对应的应收单均为无需开票";
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        item.ErrMsg += "或";
                                    }
                                    item.ErrMsg += errMsg;
                                    failCount++;
                                    continue;
                                }
                                else if (isNotNeedInvoiceCount > 0)
                                {
                                    // 排除不满足条件的应收
                                    currentlist = currentlist.Where(x => x.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed).ToList();
                                    //errCount += isNotNeedInvoiceCount;
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        errMsg += "或";
                                    }
                                    errMsg += "无需开票";
                                }
                                var isInvoiceStatusCount = currentlist.Where(x => x.InvoiceStatus == InvoiceStatusEnum.invoiced).Count();
                                if (isInvoiceStatusCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"订单对应的应收数据已经存在于开票申请中";
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        item.ErrMsg += "或";
                                    }
                                    item.ErrMsg += errMsg;
                                    failCount++;
                                    continue;
                                }
                                else if (isInvoiceStatusCount > 0)
                                {
                                    // 排除不满足条件的应收
                                    currentlist = currentlist.Where(x => x.InvoiceStatus != InvoiceStatusEnum.invoiced).ToList();
                                    //errCount += isInvoiceStatusCount;
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        errMsg += "或";
                                    }
                                    errMsg += "已经存在于开票申请中";
                                }
                                //校验红冲开票的情况
                                var count = checkCredits.Where(x => x.OrderNo == item.OrderNo).Select(x => x.Id).Count();
                                //var currentIds = checkCredits.Where(x => x.OrderNo == item.OrderNo && x.InvoiceStatus != InvoiceStatusEnum.invoiced && x.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed && x.CreditType != CreditTypeEnum.revise && x.CreditType != CreditTypeEnum.selfrevise && x.CreditType != CreditTypeEnum.servicefeerevise && x.CreditType != CreditTypeEnum.origin && x.SaleType != SaleTypeEnum.SaleRevise).Select(x => x.Id).ToList();
                                var currentIds = currentlist.Select(x => x.Id).ToList();
                                var currentCreditDetails = creditDetails.Where(x => currentIds.Any(p => p == x.CreditId)).ToList();
                                //如果订单对应的应收没有creditdetail，表示这个应收是没有做过任何开票处理的，可以导入
                                if (currentCreditDetails != null && currentCreditDetails.Any())
                                {
                                    //如果订单对应的所有应收的invoicestatus=0 且creditdetail 对应的所有的明细的amount和noinvoiceamount 不相等，校验提示出来，（这个就是这个应收只是被释放了一部分）
                                    var partAmountCheckCount = currentCreditDetails.Where(x => x.NoInvoiceAmount != x.Amount).Count();
                                    if (partAmountCheckCount == currentCreditDetails.Count())
                                    {
                                        item.ErrMsg += $"订单对应的应收的可开金额和金额不一致";
                                        if (!string.IsNullOrEmpty(errMsg))
                                        {
                                            item.ErrMsg += "或";
                                        }
                                        item.ErrMsg += errMsg;
                                        failCount++;
                                        continue;
                                    }
                                    if (partAmountCheckCount > 0)
                                    {
                                        var partAmountChecks = currentCreditDetails.Where(x => x.NoInvoiceAmount != x.Amount).Select(x => x.CreditId).ToList();
                                        currentlist = currentlist.Where(x => !partAmountChecks.ToHashSet().Contains(x.Id)).ToList();
                                        //errCount += partAmountCheckCount;
                                        if (!string.IsNullOrEmpty(errMsg))
                                        {
                                            errMsg += "或";
                                        }
                                        errMsg += "订单对应的应收的可开金额和金额不一致";
                                    }
                                }

                                if (currentlist.Count == 0)
                                {
                                    item.ErrMsg = errMsg;
                                    failCount += currentlist.Count;
                                    continue;
                                }
                                allCredits.AddRange(currentlist);
                                #region 以前的逻辑，作废（2025-02-18，保留一个月以待查看）
                                //var invoiceCreditCount = invoiceCredits.Where(x => currentIds.Any(p => p == x.CreditId)).Count();
                                //if (count <= invoiceCreditCount)
                                //{
                                //    item.ErrMsg += "订单对应的应收开过票，不支持导入";
                                //    failCount++;
                                //    continue;
                                //}
                                //var isInvoicedCount = currentlist.Where(x => x.IsInvoiced.HasValue && x.IsInvoiced.Value).Count();
                                //if (isInvoicedCount > 0 && isInvoicedCount + invoiceCreditCount >= count)
                                //{
                                //    item.ErrMsg += "订单对应的应收开过票或者开票申请中";
                                //    failCount++;
                                //    continue;
                                //}
                                //if (isNotNeedInvoiceCount > 0 && isInvoicedCount + invoiceCreditCount + isNotNeedInvoiceCount >= count)
                                //{
                                //    item.ErrMsg += "订单对应的应收开过票、在开票申请中或者无需开票";
                                //    failCount++;
                                //    continue;
                                //}
                                //if (isInvoiceStatusCount > 0 && isInvoicedCount + invoiceCreditCount + isNotNeedInvoiceCount + isInvoiceStatusCount >= count)
                                //{
                                //    item.ErrMsg += "订单对应的应收开过票、在开票申请中或者无需开票";
                                //    failCount++;
                                //    continue;
                                //}
                                #endregion
                            }
                        }

                        if (allCredits.Any())
                        {
                            //剔除校验通过的无需开票、已开票以及订单修订的应收
                            //var allCredits = checkCredits.Where(x => x.InvoiceStatus != InvoiceStatusEnum.invoiced && (!x.IsNoNeedInvoice.HasValue || x.IsNoNeedInvoice.Value == IsNoNeedInvoiceEnum.Need) && x.SaleType != SaleTypeEnum.SaleRevise && x.CreditType != CreditTypeEnum.revise && x.CreditType != CreditTypeEnum.selfrevise && x.CreditType != CreditTypeEnum.origin && x.CreditType != CreditTypeEnum.servicefeerevise && (!x.IsInvoiced.HasValue || !x.IsInvoiced.Value)).ToList();
                            // allCredits = currentlist;
                            //根据客户分组
                            var groupbyCustomer = allCredits.GroupBy(x => x.CustomerId).Select(x => x.Key).ToList();
                            foreach (var customerId in groupbyCustomer)
                            {
                                var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                                {
                                    id = customerId.ToString()
                                });
                                var credits = allCredits.Where(x => x.CustomerId == customerId).ToList();
                                //处理开票
                                var originDetailQueryInput = new OriginDetailQueryInput
                                {
                                    CreditBillCodes = credits.Select(x => x.BillCode).ToList(),
                                    RelateCodes = credits.Select(x => x.RelateCode).ToList(),
                                    Values = credits.Select(x => x.Value).ToList()
                                };
                                var (list, detailCount) = await _customizeInvoiceQueryService.GetOriginDetailAsync(originDetailQueryInput);
                                if (list == null || !list.Any())
                                {
                                    excelDetailList.Where(x => x.CompanyName == credits.FirstOrDefault()?.CompanyName).ForEach(x =>
                                    {
                                        x.ErrMsg = "订单未拉取到任何明细";
                                    });
                                    failCount += excelDetailList.Where(x => x.CompanyName == credits.FirstOrDefault()?.CompanyName).Count();
                                    continue;
                                }
                                creditDetails = await _db.CreditDetails.Include(x => x.Credit).Where(x => ids.Any(p => p == x.CreditId)).ToListAsync();
                                //按开票名称、单价、规格合并明细
                                var newdetailList = new List<OriginDetailOutput>();
                                var addCustomizeInvoiceCredit = new List<CustomizeInvoiceCreditPo>();
                                var groupList = list.GroupBy(p => new { p.ProductName, p.Price, p.Specification }).Distinct().ToList();
                                foreach (var group in groupList)
                                {
                                    var currentList = list.Where(x => x.ProductName == group.Key.ProductName && x.Price == group.Key.Price && x.Specification == group.Key.Specification).ToList();
                                    if (currentList.Any())
                                    {
                                        var product = currentList.FirstOrDefault(x => x.ProductName == group.Key.ProductName);
                                        newdetailList.Add(new OriginDetailOutput
                                        {
                                            Id = Guid.NewGuid(),
                                            OriginDetailId = string.Join(",", currentList.Select(x => x.OriginDetailId).Distinct().ToList()),
                                            ProductNo = product != null ? product.ProductNo : string.Empty,
                                            OriginalPackSpec = string.Join(",", currentList.Select(x => x.OriginalPackSpec).Distinct().ToList()),
                                            ProductName = group.Key.ProductName,
                                            OriginProductName = product != null ? product.OriginProductName : string.Empty,
                                            PackUnit = product != null ? product.PackUnit : string.Empty,
                                            OriginPackUnit = product != null ? product.OriginPackUnit : string.Empty,
                                            Specification = group.Key.Specification,
                                            Quantity = currentList.Sum(x => x.Quantity),
                                            Price = product != null ? product.Price : 0,
                                            OriginalPrice = product != null ? product.OriginalPrice : 0,
                                            TaxRate = product != null ? product.TaxRate : 0,
                                            CreditBillCode = string.Join(",", currentList.Select(x => x.CreditBillCode).Distinct().ToList()),
                                            RelateCode = string.Join(",", currentList.Select(x => x.RelateCode).Distinct().ToList()),
                                            OrderNo = string.Join(",", currentList.Select(x => x.OrderNo).Distinct().ToList()),
                                            CustomerId = product != null ? product.CustomerId : string.Empty,
                                            CustomerName = product != null ? product.CustomerName : string.Empty,
                                            CompanyId = product != null ? product.CompanyId : Guid.Empty,
                                            CompanyName = product != null ? product.CompanyName : string.Empty,
                                            NameCode = companyInfo.nameCode,
                                            OriginSpecification = product != null ? product.OriginSpecification : string.Empty,
                                            ProductId = product != null ? product.ProductId : Guid.Empty,
                                            AgentId = product != null ? product.AgentId : string.Empty,
                                            IFHighValue = product != null ? product.IFHighValue : null,
                                        });
                                    }
                                }

                                var orderNoStrs = newdetailList.Select(p => p.RelateCode).Distinct().ToList();
                                string attachFileIds = await GetAttachFileIds(orderNoStrs);

                                var code = newdetailList.First().RelateCode;
                                var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                                {
                                    BusinessArea = code.Split('-')[0],
                                    BillType = "CI",
                                    SysMonth = companyInfo.sysMonth,
                                    DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                                    Num = 1,
                                    CompanyCode = companyInfo.nameCode
                                });
                                if (outPut.Status)
                                {
                                    var outPutClassify = await _codeGenClient.ApplyCode(new ApplyCodeInput
                                    {
                                        BusinessArea = code.Split('-')[0],
                                        BillType = "CIC",
                                        SysMonth = companyInfo.sysMonth,
                                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                                        Num = 1,
                                        CompanyCode = companyInfo.nameCode
                                    });
                                    successMsg += outPutClassify.Codes.First() + "，";
                                    var classifyId = Guid.NewGuid();
                                    //添加分类
                                    var invoiceClassify = new CustomizeInvoiceClassifyPo
                                    {
                                        Id = classifyId,
                                        BillCode = outPutClassify.Codes.First(),
                                        CompanyId = Guid.Parse(companyInfo.companyId),
                                        CompanyName = companyInfo.companyName,
                                        CustomerId = Guid.Parse(customer.customerId),
                                        CustomerName = customer.customerName,
                                        CreatedBy = userName,
                                        Status = CustomizeInvoiceStatusEnum.WaitSubmit,
                                        AttachFileIds = attachFileIds,
                                        Classify = CustomizeInvoiceClassifyEnum.Credit,
                                        SaleSystemName = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.SaleSystemName)).Select(p => p.SaleSystemName).Distinct())
                                    };
                                    await _db.CustomizeInvoiceClassify.AddAsync(invoiceClassify);

                                    var itemId = Guid.NewGuid();
                                    var customizeInvoiceItem = new CustomizeInvoiceItem()
                                    {
                                        Id = itemId,
                                        Code = outPut.Codes.First(),
                                        CompanyId = Guid.Parse(companyInfo.companyId),
                                        CompanyName = companyInfo.companyName,
                                        CustomerId = customer.customerId,
                                        CustomerName = customer.customerName,
                                        BillDate = DateTime.Now,
                                        NameCode = newdetailList.FirstOrDefault().NameCode,
                                        IsPush = false,
                                        IsInvoiced = false,
                                        Status = 0,
                                        Remark = "",
                                        InvoiceType = (InvoiceTypeEnum)(int.Parse(customer?.customerInvoices?.FirstOrDefault(t => t.isInvoiceUnit == 1)?.salesInvoiceDetails ?? "0")),
                                        CustomizeInvoiceClassifyId = classifyId
                                    };
                                    customizeInvoiceItem.CreateBy(userName);
                                    // 昆明致新康德医疗供应链管理有限公司 自动生成备注
                                    if (customizeInvoiceItem.CompanyName == "昆明致新康德医疗供应链管理有限公司")
                                    {
                                        string remark = string.Empty;
                                        var cicInput = new CustomizeInvoiceClassifyInput();
                                        var classifyOutput = new CustomizeInvoiceClassifyOutput();
                                        classifyOutput.CustomerId = Guid.Parse(customizeInvoiceItem.CustomerId);
                                        cicInput.classifyOutput = classifyOutput;
                                        var r = await BeforeSubmitClassfiy(cicInput);
                                        if (r.Code == CodeStatusEnum.Success && r.Data != null && r.Data.Any())
                                        {
                                            customerInvoice customerInvoice = r.Data.FirstOrDefault();
                                            if (customerInvoice != null)
                                            {
                                                // 组装备注
                                                remark += string.Concat("购买方地址:", customerInvoice.invoiceAddr, ";    电话:", customerInvoice.invoiceTel, ";");
                                                remark += string.Concat("\n购方开户银行:", customerInvoice.invoiceBank, ";    银行账号:", customerInvoice.invoiceBankNo, ";");
                                            }
                                        }

                                        var companyInvoices = await _bDSApiClient.GetCompanyById(customizeInvoiceItem.CompanyId.Value.ToString());
                                        if (companyInvoices != null && companyInvoices.Any())
                                        {
                                            var companyInvoice = companyInvoices.FirstOrDefault();
                                            remark += string.Concat("\n销售方地址:", companyInvoice.customerInvoiceAddr, ";    电话:", companyInvoice.customerInvoiceTel, ";");
                                            remark += string.Concat("\n销方开户银行:", companyInvoice.customerInvoiceBank, ";    银行账号:", companyInvoice.customerInvoiceBankNo);
                                        }
                                        customizeInvoiceItem.Remark = remark;
                                    }
                                    var inputs = new List<ProductIdAndAgentIdInput>();
                                    var detailList = new List<CustomizeInvoiceDetail>();
                                    int sortIndex = 1;
                                    var productNoList = await _bDSApiClient.GetByNos(newdetailList.Select(p => p.ProductNo).ToList(), companyInfo.companyId, inputs);
                                    foreach (var item in newdetailList)
                                    {
                                        if (!string.IsNullOrEmpty(item.AgentId) && item.AgentId.Contains(','))
                                        {
                                            foreach (var agentId in item.AgentId.Split(',').ToList())
                                            {
                                                if (!string.IsNullOrEmpty(agentId))
                                                {
                                                    inputs.Add(new ProductIdAndAgentIdInput()
                                                    {
                                                        agentId = agentId,
                                                        productId = item.ProductId
                                                    });
                                                }
                                            }
                                        }
                                        else
                                        {
                                            inputs.Add(new ProductIdAndAgentIdInput()
                                            {
                                                agentId = item.AgentId,
                                                productId = item.ProductId
                                            });
                                        }

                                        //映射CustomizeInvoiceDetail表
                                        var customizeInvoiceDetail = item.Adapt<CustomizeInvoiceDetail>();
                                        customizeInvoiceDetail.Id = Guid.NewGuid();
                                        if (string.IsNullOrEmpty(customizeInvoiceDetail.ProductNo))
                                        {
                                            var origin = list.FirstOrDefault(p => p.ProductId == customizeInvoiceDetail.ProductId);
                                            if (origin != null)
                                            {
                                                customizeInvoiceDetail.ProductNo = origin.ProductNo;
                                            }
                                        }
                                        customizeInvoiceDetail.Sort = sortIndex;
                                        customizeInvoiceDetail.PackUnit = customizeInvoiceDetail.PackUnit ?? "";
                                        var product = productNoList.Find(p => p.productNo == customizeInvoiceDetail.ProductNo);
                                        if (product != null)
                                        {
                                            if (product.ifTool != 3)
                                            {
                                                customizeInvoiceDetail.TaxTypeNo = product.taxClassCode;
                                            }
                                            else
                                            {
                                                customizeInvoiceDetail.TaxTypeNo = product.taxControlCode;
                                            }
                                        }
                                        customizeInvoiceDetail.CustomizeInvoiceItemId = itemId;
                                        customizeInvoiceDetail.Specification = customizeInvoiceDetail.Specification ?? "";
                                        customizeInvoiceDetail.OriginProductName = customizeInvoiceDetail.OriginProductName;
                                        if (!customizeInvoiceDetail.ProductId.HasValue)
                                        {
                                            var origin = list.FirstOrDefault(p => p.ProductNo == customizeInvoiceDetail.ProductNo);
                                            if (origin != null)
                                            {
                                                customizeInvoiceDetail.ProductId = origin.ProductId;
                                            }
                                        }
                                        sortIndex++;
                                        customizeInvoiceDetail.Value = Math.Round(customizeInvoiceDetail.Quantity * customizeInvoiceDetail.Price, 4);
                                        customizeInvoiceDetail.TaxAmount = (customizeInvoiceDetail.Value / (1 + (customizeInvoiceDetail.TaxRate / 100))) * (customizeInvoiceDetail.TaxRate / 100);
                                        detailList.Add(customizeInvoiceDetail);

                                        //更新应收明细
                                        var listTemps = list.Where(p => item.OriginDetailId.Contains(p.OriginDetailId)).ToList();
                                        foreach (var subItem in listTemps)
                                        {
                                            var creditDetail = creditDetails.FirstOrDefault(p => p.ProductId == subItem.ProductId && p.Credit.BillCode == subItem.CreditBillCode && p.OriginDetailId == subItem.OriginDetailId);
                                            if (creditDetail != null)
                                            {
                                                addCustomizeInvoiceCredit.Add(new CustomizeInvoiceCreditPo
                                                {
                                                    CreatedBy = userName,
                                                    CreditCode = subItem.CreditBillCode,
                                                    CustomizeInvoiceDetailAmount = creditDetail.NoInvoiceAmount.Value,
                                                    CustomizeInvoiceItemCode = customizeInvoiceItem.Code,
                                                    CustomizeInvoiceItemId = customizeInvoiceItem.Id,
                                                    Id = Guid.NewGuid(),
                                                    ProductId = subItem.ProductId,
                                                    ProductNo = subItem.ProductNo,
                                                    Quantity = subItem.Quantity,
                                                    Price = subItem.Price,
                                                    OriginDetailId = subItem.OriginDetailId,
                                                    CustomizeInvoiceDetailId = customizeInvoiceDetail.Id,
                                                    CreditDetailId = creditDetail.Id,
                                                    CreditDetailAmount = creditDetail.Amount,
                                                    Value = creditDetail.Credit.Value
                                                });
                                                creditDetail.NoInvoiceAmount = 0;
                                                creditDetail.InvoiceAmount = creditDetail.Amount;
                                            }
                                        }
                                    }
                                    inputs = inputs.Distinct().ToList();

                                    customizeInvoiceItem.InvoiceTotalAmount = detailList.Sum(t => t.Value);

                                    var zoreDetailLst = detailList.Where(p => p.Quantity == 0).ToList();
                                    var notZoreDetailLst = detailList.Where(p => p.Quantity != 0).ToList();
                                    if (zoreDetailLst.Any())
                                    {
                                        var notZoreDetailIds = notZoreDetailLst.Select(p => p.Id).ToList();

                                        var customizeInvoiceCredits1 = addCustomizeInvoiceCredit.Where(p => notZoreDetailIds.Contains(p.CustomizeInvoiceDetailId)).ToList();
                                        var creditCodes1 = customizeInvoiceCredits1.Select(p => p.CreditCode).ToList();
                                        var creditDetailIds1 = customizeInvoiceCredits1.Select(p => p.CreditDetailId).ToList();
                                        var creditDetail1 = creditDetails.Where(p => creditDetailIds1.Contains(p.Id)).Distinct().ToList();

                                        foreach (var item in zoreDetailLst)
                                        {
                                            var needUpdateCustomizeInvoiceCredits = addCustomizeInvoiceCredit.Where(p => p.CustomizeInvoiceDetailId == item.Id).ToList();
                                            var creditCodes = needUpdateCustomizeInvoiceCredits.Select(p => p.CreditCode).ToList();
                                            var creditDetailIds = needUpdateCustomizeInvoiceCredits.Select(p => p.CreditDetailId).ToList();
                                            var originalIds = creditDetails.Where(p => creditDetailIds.Contains(p.Id) && p.OriginalId.HasValue)
                                                              .Select(p => p.OriginalId).Distinct().ToList();
                                            var creditDetailTemp = creditDetail1.Where(p => originalIds.Contains(p.OriginalId)).FirstOrDefault();
                                            if (creditDetailTemp != null)
                                            {
                                                var customizeInvoiceCreditTemp = addCustomizeInvoiceCredit.FirstOrDefault(p => p.CreditDetailId == creditDetailTemp.Id);
                                                if (customizeInvoiceCreditTemp != null)
                                                {
                                                    foreach (var subitem in needUpdateCustomizeInvoiceCredits)
                                                    {
                                                        subitem.CustomizeInvoiceDetailId = customizeInvoiceCreditTemp.CustomizeInvoiceDetailId;
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                //随机给一个
                                                var customizeInvoiceCreditTemp = addCustomizeInvoiceCredit.FirstOrDefault(p => p.CreditDetailId == creditDetail1.First().Id);
                                                if (customizeInvoiceCreditTemp != null)
                                                {
                                                    foreach (var subitem in needUpdateCustomizeInvoiceCredits)
                                                    {
                                                        subitem.CustomizeInvoiceDetailId = customizeInvoiceCreditTemp.CustomizeInvoiceDetailId;
                                                    }
                                                }

                                            }
                                        }
                                    }
                                    foreach (var item in notZoreDetailLst)
                                    {
                                        var customizeInvoiceCreditsTemp = addCustomizeInvoiceCredit.Where(p => p.CustomizeInvoiceDetailId == item.Id).ToList();
                                        var creditCodes = customizeInvoiceCreditsTemp.Select(p => p.CreditCode).Distinct();
                                        item.CreditBillCode = string.Join(",", creditCodes);
                                        var creditTemps = credits.Where(p => creditCodes.Contains(p.BillCode)).ToList();
                                        item.OrderNo = string.Join(",", creditTemps.Select(p => p.OrderNo).Distinct());
                                        item.RelateCode = string.Join(",", creditTemps.Select(p => p.RelateCode).Distinct());
                                    }
                                    await _customizeInvoiceDetailRepository.AddManyAsync(notZoreDetailLst);
                                    await _customizeInvoiceItemRepository.AddAsync(customizeInvoiceItem);

                                    // 更改应收状态
                                    foreach (var credit in credits)
                                    {
                                        credit.InvoiceStatus = InvoiceStatusEnum.invoiced;
                                    }
                                    if (addCustomizeInvoiceCredit.Any())
                                    {
                                        await _db.CustomizeInvoiceCredits.AddRangeAsync(addCustomizeInvoiceCredit);
                                    }
                                }
                            }
                            await _unitOfWork.CommitAsync();
                        }
                    }

                    if (excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count() > 0)
                    {
                        for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                        {
                            string b = worksheet.Cells[index, 2].Text.Trim(); //订单号
                            var errModel = excelDetailList.LastOrDefault(p => p.OrderNo == b);
                            var errMsg = errModel != null ? errModel.ErrMsg : string.Empty;
                            if (!string.IsNullOrEmpty(errMsg))//出错信息
                            {
                                worksheet.Cells[index, 4].Value = errMsg;
                            }
                        }
                        worksheet.Column(4).Style.Font.Color.SetColor(Color.Red);
                        //生成错误报告文件
                        MemoryStream msFailReport = new MemoryStream(package.GetAsByteArray());
                        ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Success("导入失败！");
                        ret.Data = new CustomizeInvoiceDetailExcelOutput()
                        {
                            FailReportFileId = await _fileGatewayClient.UploadTempFileContentAsync(msFailReport, "批量开票错误数据.xlsx"),
                            FailNumber = excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count(),
                            SuccessNumber = excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).Count(),
                            Total = excelDetailList.Count(),
                        };
                        if (!string.IsNullOrEmpty(successMsg))
                        {
                            successMsg = successMsg.Substring(0, successMsg.Length - 1);
                            ret.Message = excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count() < excelDetailList.Count() ? "保存开票明细部分成功" + successMsg : "保存开票明细失败，请下载错误信息文件查看原因";
                        }
                        return ret;
                    }
                    else
                    {
                        ret.Data = new CustomizeInvoiceDetailExcelOutput()
                        {
                            FailNumber = 0,
                            SuccessNumber = excelDetailList.Count(),
                            Total = excelDetailList.Count(),
                        };
                        if (!string.IsNullOrEmpty(successMsg))
                        {
                            successMsg = successMsg.Substring(0, successMsg.Length - 1);
                            ret.Message = "保存开票明细成功" + successMsg;
                        }
                    }
                }
                return ret;
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "导入失败！错误信息：" + ex.Message);
                return ret;
            }
        }


        /// <summary>
        /// 批量开票(导入开票明细)
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportBatchInvoiceDetail(Guid fileId, string userName, Guid? userId)
        {
            var ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Success("操作成功！");
            var successMsg = string.Empty;
            try
            {
                int failCount = 0;
                var stream = await _fileGatewayClient.GetTempFileStreamAsync(fileId);
                var excelDetailList = new List<CustomizeInvoicingExcelModel>();

                var newCustomizeInvoiceDetail = new List<CustomizeInvoiceDetail>();

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    stream.Close();
                    var worksheet = package.Workbook.Worksheets[0];
                    worksheet.TrimLastEmptyRows();
                    int rows = worksheet.Dimension.End.Row;
                    for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                    {
                        var errText = string.Empty;
                        string c = worksheet.Cells[index, 1].Text.TrimStart().TrimEnd(); //公司
                        string o = worksheet.Cells[index, 2].Text.TrimStart().TrimEnd(); //订单
                        if (string.IsNullOrEmpty(worksheet.Cells[index, 1].Text.Trim()) || string.IsNullOrEmpty(worksheet.Cells[index, 2].Text.Trim()))
                        {
                            errText = "请输入完整的数据";
                            failCount++;
                        }
                        else if (excelDetailList.Select(p => p.OrderNo).ToList().Contains(o))
                        {
                            errText = "存在重复的订单号";
                            failCount++;
                        }
                        excelDetailList.Add(new CustomizeInvoicingExcelModel()
                        {
                            CompanyName = c,
                            OrderNo = o,
                            ErrMsg = errText, //错误信息
                        });
                    }
                    if (excelDetailList == null || excelDetailList.Count() == 0)
                    {
                        ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "没有获取到表格的开票信息");
                        return ret;
                    }
                    if (excelDetailList.Select(x => x.CompanyName).Distinct().Count() > 1)
                    {
                        excelDetailList.ForEach(x =>
                        {
                            x.ErrMsg = "仅支持同一个公司的导入开票";
                        });
                        failCount = excelDetailList.Count();
                    }
                    //拉取到订单的原始应收明细(公司+客户+应收单号)
                    var orderNos = excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).Select(x => x.OrderNo).ToList();
                    var companyName = excelDetailList[0].CompanyName;
                    //获取公司信息
                    var companylist = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        names = new List<string> { companyName }
                    }));
                    var companyInfo = companylist.Where(x => x.companyName == companyName).FirstOrDefault();
                    if (companyInfo == null)
                    {
                        excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                        {
                            x.ErrMsg = "公司错误";
                        });
                        failCount = excelDetailList.Count();
                    }
                    //根据公司和订单号查询到未开票且需要开票的应收（不包含订单修订）
                    var checkCredits = await _db.Credits.Where(x => !string.IsNullOrEmpty(x.OrderNo) && x.CompanyName == companyName && orderNos.Any(p => p == x.OrderNo)).Select(p => p.Adapt<CreditQueryListOutput>()).ToListAsync();
                    var checkCreditsGroupByCustomerName = checkCredits.GroupBy(p => p.CustomerName).ToList();
                    if (checkCreditsGroupByCustomerName.Count() > 1)
                    {
                        var customerNames = checkCreditsGroupByCustomerName.Select(p => p.Key).ToList();
                        ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, $"导入的数据中存在【{string.Join("、", customerNames)}】的应收数据，请检查后操作");
                        return ret;
                    }
                    //错误信息小于总条数则进行下一步，等于总条数直接抛出错误
                    if (excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count() < excelDetailList.Count())
                    {
                        if (checkCredits == null || !checkCredits.Any())
                        {
                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                            {
                                x.ErrMsg = "根据公司和订单没有查询到应收数据，请检查订单号或公司是否正确";
                            });
                            failCount = excelDetailList.Count();
                        }
                        //查询当前登录人是否有操作该公司的权限
                        var strategryquery = new StrategyQueryInput() { userId = userId, functionUri = "metadata://fam" };
                        var strategry = await _pCApiClient.GetStrategyAsync(strategryquery);
                        if (strategry != null && strategry.RowStrategies.Count > 0)
                        {
                            var rowStrategies = strategry.RowStrategies;
                            if (!rowStrategies.Keys.Contains("company"))
                            {
                                excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                {
                                    x.ErrMsg = "您没有操作当前公司的权限";
                                });
                                failCount = excelDetailList.Count();
                            }
                            foreach (var key in rowStrategies.Keys)
                            {
                                if (key.ToLower() == "company")
                                {
                                    if (!rowStrategies[key].Any(s => s == "@all"))
                                    {
                                        var strategList = rowStrategies[key].Where(x => x == companyInfo.companyId).Select(s => Guid.Parse(s.ToLower())).ToList();
                                        if (strategList == null || !strategList.Any())
                                        {
                                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                            {
                                                x.ErrMsg = "您没有操作当前公司的权限";
                                            });
                                            failCount = excelDetailList.Count();
                                            break;
                                        }
                                    }
                                }
                                if (key.ToLower() == "customer")
                                {
                                    //查询当前登录人是否有操作该客户的权限
                                    if (!rowStrategies[key].Any(z => z == "@all"))
                                    {
                                        var customrtIds = checkCredits.Select(x => x.CustomerId.ToString().ToUpper()).ToList();
                                        if (customrtIds != null && customrtIds.Any())
                                        {
                                            var strategList = rowStrategies[key].Where(x => customrtIds.ToHashSet().Contains(x)).ToHashSet();
                                            if (strategList == null || !strategList.Any())
                                            {
                                                excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                                {
                                                    x.ErrMsg = "您没有操作当前客户的权限";
                                                });
                                                failCount = excelDetailList.Count();
                                                break;
                                            }
                                            else
                                            {
                                                var noexistOrders = checkCredits.Where(x => x.CustomerId.HasValue && !strategList.Contains(x.CustomerId.Value.ToString().ToUpper())).Select(x => x.OrderNo).ToHashSet();
                                                if (noexistOrders != null && noexistOrders.Any())
                                                {
                                                    excelDetailList.Where(x => noexistOrders.Contains(x.OrderNo)).ForEach(x =>
                                                    {
                                                        x.ErrMsg = "您没有操作当前客户的权限";
                                                    });
                                                    failCount += excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && noexistOrders.Contains(x.OrderNo)).Count();
                                                    continue;
                                                }
                                            }
                                        }
                                    }
                                }
                                if (key.ToLower() == "accountingdept")
                                {
                                    if (!rowStrategies[key].Any(s => s == "@all"))
                                    {
                                        var accountingdeptIds = checkCredits.Select(x => x.BusinessDeptId).ToList();
                                        if (accountingdeptIds != null && accountingdeptIds.Any())
                                        {
                                            var strategList = rowStrategies[key].Where(x => accountingdeptIds.ToHashSet().Contains(x)).ToHashSet();
                                            if (strategList == null || !strategList.Any())
                                            {
                                                excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                                {
                                                    x.ErrMsg = "您没有操作当前部门的权限";
                                                });
                                                failCount = excelDetailList.Count();
                                                continue;
                                            }
                                            else
                                            {
                                                var noexistOrders = checkCredits.Where(x => !string.IsNullOrEmpty(x.BusinessDeptId) && !strategList.Contains(x.BusinessDeptId)).Select(x => x.OrderNo).ToHashSet();
                                                if (noexistOrders != null && noexistOrders.Any())
                                                {
                                                    excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && noexistOrders.Contains(x.OrderNo)).ForEach(x =>
                                                    {
                                                        x.ErrMsg = "您没有操作当前部门的权限";
                                                    });
                                                    failCount += excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && noexistOrders.Contains(x.OrderNo)).Count();
                                                    continue;
                                                }
                                            }
                                        }
                                    }
                                }
                                if (key.ToLower() == "project")
                                {
                                    if (!rowStrategies[key].Any(s => s == "@all"))
                                    {
                                        var strategList = rowStrategies[key].ToList();
                                        if (strategList != null && strategList.Any())
                                        {
                                            if (checkCredits != null && checkCredits.Any())
                                            {
                                                var projectCredits = checkCredits.Where(x => !string.IsNullOrEmpty(x.ProjectId) && strategList.ToHashSet().Contains(x.ProjectId.ToString().ToUpper()));
                                                if (projectCredits == null || !projectCredits.Any())
                                                {
                                                    excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                                    {
                                                        x.ErrMsg = "您没有操作当前项目的权限";
                                                    });
                                                    failCount = excelDetailList.Count();
                                                    continue;
                                                }
                                                else
                                                {
                                                    foreach (var item in projectCredits)
                                                    {
                                                        if (!strategList.ToHashSet().Contains(item.ProjectId.ToString().ToUpper()))
                                                        {
                                                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && x.OrderNo == item.OrderNo).ForEach(x =>
                                                            {
                                                                x.ErrMsg = "您没有操作当前项目的权限";
                                                            });
                                                            failCount += excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg) && x.OrderNo == item.OrderNo).Count();
                                                            continue;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                                            {
                                                x.ErrMsg = "您没有操作当前项目的权限";
                                            });
                                            failCount = excelDetailList.Count();
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ForEach(x =>
                            {
                                x.ErrMsg = "您没有操作当前公司的权限";
                            });
                            failCount = excelDetailList.Count();
                        }
                    }

                    var ids = checkCredits.Select(x => x.Id).ToList();
                    var creditDetails = await _db.CreditDetails.Include(x => x.Credit).Where(x => ids.Any(p => p == x.CreditId)).AsNoTracking().ToListAsync();
                    var invoiceCredits = await _db.InvoiceCredits.Where(x => ids.Any(p => p == x.CreditId)).ToListAsync();
                    await InitCreditInfo(checkCredits);
                    var allCredits = new List<CreditQueryListOutput>();
                    var CreditDetailsOutput = new List<OriginDetailOutput>();
                    //错误信息小于总条数则进行下一步，等于总条数直接抛出错误
                    if (excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count() < excelDetailList.Count())
                    {
                        foreach (var item in excelDetailList)
                        {
                            if (!string.IsNullOrEmpty(item.ErrMsg))
                            {
                                continue;
                            }
                            var errCount = 0;
                            var errMsg = string.Empty;
                            var currentlist = checkCredits.Where(x => x.OrderNo == item.OrderNo).ToList();
                            if (currentlist == null || !currentlist.Any())
                            {
                                item.ErrMsg = "根据公司和订单没有查询到应收数据，请检查订单号或公司是否正确";
                                failCount++;
                                continue;
                            }
                            else
                            {
                                var checkbox = new List<CreditPo>();
                                var creditCodes = currentlist.Select(x => x.BillCode).ToList();
                                var creditCodesStr = string.Join(",", creditCodes);
                                var isSPDCount = currentlist.Where(x => x.SaleSource == SaleSourceEnum.Spd).Count();
                                if (isSPDCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"不支持订单来源SPD生成的应收导入";
                                    failCount++;
                                    continue;
                                }
                                else if (isSPDCount > 0)
                                {
                                    errMsg += "不支持订单来源SPD生成的应收";
                                }
                                var isSaleReviseCount = currentlist.Where(x => x.SaleType == SaleTypeEnum.SaleRevise).Count();
                                if (isSaleReviseCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"不支持订单修订生成的应收导入";
                                    failCount++;
                                    continue;
                                }
                                else if (isSaleReviseCount > 0)
                                {
                                    //errCount += isSaleReviseCount;
                                    errMsg += "不支持订单修订生成的应收";
                                }
                                var isCreditTypeCount = currentlist.Where(x => x.CreditType == (int)CreditTypeEnum.revise || x.CreditType == (int)CreditTypeEnum.selfrevise || x.CreditType == (int)CreditTypeEnum.servicefeerevise || x.CreditType == (int)CreditTypeEnum.origin).Count();
                                if (isCreditTypeCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"不支持订单修订、初始应收或服务费修订生成的应收导入";
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        item.ErrMsg += "或";
                                    }
                                    item.ErrMsg += errMsg;
                                    failCount++;
                                    continue;
                                }
                                else if (isCreditTypeCount > 0)
                                {
                                    // 排除不满足条件的应收
                                    currentlist = currentlist.Where(x => x.CreditType != (int)CreditTypeEnum.revise && x.CreditType != (int)CreditTypeEnum.selfrevise && x.CreditType != (int)CreditTypeEnum.servicefeerevise && x.CreditType != (int)CreditTypeEnum.origin).ToList();
                                    //errCount += isCreditTypeCount;
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        errMsg += "或";
                                    }
                                    errMsg += "初始应收或服务费修订生成的应收";
                                }
                                var isNotNeedInvoiceCount = currentlist.Where(x => x.IsNoNeedInvoice == IsNoNeedInvoiceEnum.NoNeed).Count();
                                if (isNotNeedInvoiceCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"订单对应的应收单均为无需开票";
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        item.ErrMsg += "或";
                                    }
                                    item.ErrMsg += errMsg;
                                    failCount++;
                                    continue;
                                }
                                else if (isNotNeedInvoiceCount > 0)
                                {
                                    // 排除不满足条件的应收
                                    currentlist = currentlist.Where(x => x.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed).ToList();
                                    //errCount += isNotNeedInvoiceCount;
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        errMsg += "或";
                                    }
                                    errMsg += "无需开票";
                                }
                                var isInvoiceStatusCount = currentlist.Where(x => x.InvoiceStatus == (int)InvoiceStatusEnum.invoiced).Count();
                                if (isInvoiceStatusCount == currentlist.Count && currentlist.Count > 0)
                                {
                                    item.ErrMsg += $"订单对应的应收数据已经存在于开票申请中";
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        item.ErrMsg += "或";
                                    }
                                    item.ErrMsg += errMsg;
                                    failCount++;
                                    continue;
                                }
                                else if (isInvoiceStatusCount > 0)
                                {
                                    // 排除不满足条件的应收
                                    currentlist = currentlist.Where(x => x.InvoiceStatus != (int)InvoiceStatusEnum.invoiced).ToList();
                                    //errCount += isInvoiceStatusCount;
                                    if (!string.IsNullOrEmpty(errMsg))
                                    {
                                        errMsg += "或";
                                    }
                                    errMsg += "已经存在于开票申请中";
                                }
                                //校验红冲开票的情况
                                var count = checkCredits.Where(x => x.OrderNo == item.OrderNo).Select(x => x.Id).Count();
                                var currentIds = currentlist.Select(x => x.Id).ToList();
                                var currentCreditDetails = creditDetails.Where(x => currentIds.Any(p => p == x.CreditId)).ToList();
                                //如果订单对应的应收没有creditdetail，表示这个应收是没有做过任何开票处理的，可以导入
                                if (currentCreditDetails != null && currentCreditDetails.Any())
                                {
                                    //如果订单对应的所有应收的invoicestatus=0 且creditdetail 对应的所有的明细的amount和noinvoiceamount 不相等，校验提示出来，（这个就是这个应收只是被释放了一部分）
                                    var partAmountCheckCount = currentCreditDetails.Where(x => x.NoInvoiceAmount != x.Amount).Count();
                                    if (partAmountCheckCount == currentCreditDetails.Count())
                                    {
                                        item.ErrMsg += $"订单对应的应收的可开金额和金额不一致";
                                        if (!string.IsNullOrEmpty(errMsg))
                                        {
                                            item.ErrMsg += "或";
                                        }
                                        item.ErrMsg += errMsg;
                                        failCount++;
                                        continue;
                                    }
                                    if (partAmountCheckCount > 0)
                                    {
                                        var partAmountChecks = currentCreditDetails.Where(x => x.NoInvoiceAmount != x.Amount).Select(x => x.CreditId).ToList();
                                        currentlist = currentlist.Where(x => !partAmountChecks.ToHashSet().Contains(x.Id)).ToList();
                                        //errCount += partAmountCheckCount;
                                        if (!string.IsNullOrEmpty(errMsg))
                                        {
                                            errMsg += "或";
                                        }
                                        errMsg += "订单对应的应收的可开金额和金额不一致";
                                    }
                                }

                                if (currentlist.Count == 0)
                                {
                                    item.ErrMsg = errMsg;
                                    failCount += currentlist.Count;
                                    continue;
                                }
                                allCredits.AddRange(currentlist);
                            }
                        }

                        if (allCredits.Any())
                        {
                            //根据客户分组
                            var groupbyCustomer = allCredits.GroupBy(x => x.CustomerId).Select(x => x.Key).ToList();
                            foreach (var customerId in groupbyCustomer)
                            {
                                var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                                {
                                    id = customerId.ToString()
                                });
                                var credits = allCredits.Where(x => x.CustomerId == customerId).ToList();
                                //处理开票
                                var originDetailQueryInput = new OriginDetailQueryInput
                                {
                                    CreditBillCodes = credits.Select(x => x.BillCode).ToList(),
                                    RelateCodes = credits.Select(x => x.RelateCode).ToList(),
                                    Values = credits.Select(x => x.Value).ToList()
                                };
                                (CreditDetailsOutput, var detailCount) = await _customizeInvoiceQueryService.GetOriginDetailAsync(originDetailQueryInput);
                            }
                        }
                    }

                    if (excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count() > 0)
                    {
                        for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                        {
                            string b = worksheet.Cells[index, 2].Text.Trim(); //订单号
                            var errModel = excelDetailList.LastOrDefault(p => p.OrderNo == b);
                            var errMsg = errModel != null ? errModel.ErrMsg : string.Empty;
                            if (!string.IsNullOrEmpty(errMsg))//出错信息
                            {
                                worksheet.Cells[index, 4].Value = errMsg;
                            }
                        }
                        worksheet.Column(4).Style.Font.Color.SetColor(Color.Red);
                        //生成错误报告文件
                        MemoryStream msFailReport = new MemoryStream(package.GetAsByteArray());
                        ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Success("导入失败！");
                        ret.Data = new CustomizeInvoiceDetailExcelOutput()
                        {
                            FailReportFileId = await _fileGatewayClient.UploadTempFileContentAsync(msFailReport, "批量开票错误数据.xlsx"),
                            FailNumber = excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count(),
                            SuccessNumber = excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).Count(),
                            Total = excelDetailList.Count(),
                        };
                        if (!string.IsNullOrEmpty(successMsg))
                        {
                            successMsg = successMsg.Substring(0, successMsg.Length - 1);
                            ret.Message = excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count() < excelDetailList.Count() ? "保存开票明细部分成功" + successMsg : "保存开票明细失败，请下载错误信息文件查看原因";
                        }
                        return ret;
                    }
                    else
                    {
                        ret.Data = new CustomizeInvoiceDetailExcelOutput()
                        {
                            FailNumber = 0,
                            SuccessNumber = excelDetailList.Count(),
                            Total = excelDetailList.Count(),
                            Credits = allCredits,
                            CreditDetails = CreditDetailsOutput

                        };
                        if (!string.IsNullOrEmpty(successMsg))
                        {
                            successMsg = successMsg.Substring(0, successMsg.Length - 1);
                            ret.Message = "保存开票明细成功" + successMsg;
                        }
                    }
                }
                return ret;
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<CustomizeInvoiceDetailExcelOutput>.Failed(500, "导入失败！错误信息：" + ex.Message);
                return ret;
            }
        }
        private async Task InitCreditInfo(List<CreditQueryListOutput> list)
        {
            //获取已冲销金额
            var creditCodes = list.Select(p => p.BillCode).ToList();
            var abas = new List<AbatementPo>();
            var abas1 = await _db.Abatements.Where(x => creditCodes.ToHashSet().Contains(x.DebtBillCode)).AsNoTracking().ToListAsync();
            var abas2 = await _db.Abatements.Where(x => creditCodes.ToHashSet().Contains(x.CreditBillCode)).AsNoTracking().ToListAsync();
            abas.AddRange(abas1);
            abas.AddRange(abas2);
            abas = abas.Distinct().ToList();
            var abatments = abas.Select(p => new { p.DebtBillCode, p.CreditBillCode, p.Value }).ToList();
            //获取已开发票金额
            var creditIds = list.Select(p => p.Id).ToHashSet();
            var invoiceCredits = await _db.InvoiceCredits.Where(p => p.CreditId != null && creditIds.Contains((Guid)p.CreditId) && p.IsCancel != true)
                                     .Select(p => new { p.CreditId, p.InvoiceAmount, p.CreditAmount, p.Type }).ToListAsync();


            foreach (var credit in list)
            {
                credit.AbatmentAmount = abatments.Where(t => t.DebtBillCode == credit.BillCode || t.CreditBillCode == credit.BillCode).Sum(t => t.Value);
                credit.InvoiceAmount = invoiceCredits.Where(t => t.CreditId == credit.Id).Sum(p => p.InvoiceAmount > 0 ? Math.Abs(p.CreditAmount.Value) : -Math.Abs(p.CreditAmount.Value));
                if (invoiceCredits.FirstOrDefault(t => t.CreditId == credit.Id) != null)
                {
                    switch (invoiceCredits.FirstOrDefault(t => t.CreditId == credit.Id).Type)
                    {
                        case "电子普通发票":
                            credit.InvoiceType = InvoiceTypeEnum.DZUniversal;
                            break;
                        case "电子专用发票":
                            credit.InvoiceType = InvoiceTypeEnum.DZSpecial;
                            break;
                        case "纸质普通发票":
                            credit.InvoiceType = InvoiceTypeEnum.DZUniversal;
                            break;
                        case "纸质专用发票":
                            credit.InvoiceType = InvoiceTypeEnum.ZZSpecial;
                            break;
                        case "增值税普通发票(卷票)":
                            credit.InvoiceType = InvoiceTypeEnum.RollTicket;
                            break;
                        case "数电票(增值税专用发票)":
                            credit.InvoiceType = InvoiceTypeEnum.DigitalCircuitTiket;
                            break;
                        case "数电票(普通发票)":
                            credit.InvoiceType = InvoiceTypeEnum.DigitalCircuitUniversal;
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        /// <summary>
        /// 检查应收单号是否全部合并
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<string>> CheckWDTMerge(CheckWDTMergeInput input)
        {
            var originOrderNos = input.Details.Select(s => s.OriginOrderNo).ToHashSet();
            var credits = await _db.Credits
                .Where(x => x.Value != 0 && x.CreditSaleSubType.HasValue &&
                x.CreditSaleSubType == CreditSaleSubTypeEnum.personal &&
                 x.InvoiceStatus == InvoiceStatusEnum.noninvoice &&
                 originOrderNos.Contains(x.OriginOrderNo))
                .ToListAsync();
            List<string> needCode = new List<string>();
            foreach (var item in credits)
            {
                if (!input.Details.Select(s => s.BillCode).Contains(item.BillCode))
                {
                    needCode.Add(item.BillCode);
                }
            }
            if (needCode.Count > 0)
            {
                throw new ApplicationException($"{string.Join('、', needCode)}单号需要一起合并");
            }
            return new BaseResponseData<string>
            {
                Code = CodeStatusEnum.Success,
                Message = "合并成功"
            };
        }
    }

}
