﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using NPOI.POIFS.Crypt.Dsig;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class ReconciliationIncomeQueryService : IReconciliationIncomeQueryService
    {
        private readonly FinanceDbContext _db;
        public readonly List<string> InComeTypes = new List<string> { "销售出库", "暂存核销订单", "订单修订", "采购购货修订", "采购订单修订", "销售调回", "退货入库", "销售换入", "销售换出", "赠品出库", "跟台核销" };
        public ReconciliationIncomeQueryService(FinanceDbContext db)
        {
            _db = db;
        }
        /// <summary>
        /// 按公司
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListPages(ReconciliationItemInput input)
        {
            try
            {
                var list = await _db.ReconciliationIncomeDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId&& InComeTypes.Contains(p.BillTypeStr))
                         .GroupBy(p => p.ReconciliationItemId)
                         .Select(z => new ReconciliationOutput
                         {
                             Cost = z.Sum(z => z.Cost),
                             CostOfNoTax = z.Sum(z => z.CostOfNoTax),
                             Income = z.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.Income),
                             IncomeOfNoTax = z.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.IncomeOfNoTax),
                             KisData = 2
                         }).AsNoTracking().ToListAsync();
                var count = list.Count;

                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 根据供应商进行分组
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListByAgent(ReconciliationItemInput input)
        {
            try
            {
                var query = _db.ReconciliationIncomeDetail.AsNoTracking();
                //分页
                var list = await query.OrderByDefault(input.sort).Where(x => x.ReconciliationItemId == input.ReconciliationItemId && InComeTypes.Contains(x.BillTypeStr)).GroupBy(x => new { x.AgentId, x.CustomerId }).Select(z => new ReconciliationOutput
                {
                    IncomeOfNoTax = z.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.IncomeOfNoTax),
                    CostOfNoTax = z.Sum(z => z.CostOfNoTax),
                    Income = z.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.Income),
                    Cost = z.Sum(z => z.Cost),
                    AgentId = z.Key.AgentId,
                    CustomerId = z.Key.CustomerId,
                    KisData = 2
                }).ToListAsync();
                var count = list.Count;
                list = list.Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 根据客户进行分组
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListByCustomer(ReconciliationItemInput input)
        {
            try
            {
                var query = _db.ReconciliationIncomeDetail.AsNoTracking();
                //分页
                var list = await query.OrderByDefault(input.sort).Where(x => x.ReconciliationItemId == input.ReconciliationItemId && x.AgentId == input.AgentId && InComeTypes.Contains(x.BillTypeStr)).GroupBy(x => new { x.AgentId, x.CustomerId, x.SaleOrderNo }).Select(z => new ReconciliationOutput
                {
                    IncomeOfNoTax = z.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.IncomeOfNoTax),
                    CostOfNoTax = z.Sum(z => z.CostOfNoTax),
                    Income = z.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.Income),
                    Cost = z.Sum(z => z.Cost),
                    AgentId = z.Key.AgentId,
                    CustomerId = z.Key.CustomerId,
                    SaleOrderNo = z.Key.SaleOrderNo,
                    KisData = 2
                }).ToListAsync();
                var count = list.Count;
                list = list.Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        #region 导出

        /// <summary>
        /// 按公司
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListExport(ReconciliationItemExportInput input)
        {
            try
            {
                var list = await _db.ReconciliationIncomeDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId && InComeTypes.Contains(p.BillTypeStr))
                         .GroupBy(p => p.ReconciliationItemId)
                         .Select(z => new ReconciliationOutput
                         {
                             Cost = z.Sum(z => z.Cost),
                             CostOfNoTax = z.Sum(z => z.CostOfNoTax),
                             Income = z.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.Income),
                             IncomeOfNoTax = z.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.IncomeOfNoTax),
                             KisData = 2
                         }).AsNoTracking().ToListAsync();
                var count = list.Count;

                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 按公司,客户
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListOfCustomerExport(ReconciliationItemExportInput input)
        {
            try
            {
                var list = await _db.ReconciliationIncomeDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId && InComeTypes.Contains(p.BillTypeStr))
                         .GroupBy(p => new { p.ReconciliationItemId, p.CustomerId, p.CustomerName })
                         .Select(g => new ReconciliationOutput
                         {
                             Cost = g.Sum(z => z.Cost),
                             CostOfNoTax = g.Sum(z => z.CostOfNoTax),
                             Income = g.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.Income),
                             IncomeOfNoTax = g.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.IncomeOfNoTax),
                             CustomerName = g.Key.CustomerName,
                             KisData = 2
                         }).OrderBy(p => p.CustomerName).AsNoTracking().ToListAsync();
                var count = list.Count;

                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 按公司,客户,供应商，单据
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListOfBillExport(ReconciliationItemExportInput input)
        {
            try
            {
                var list = await _db.ReconciliationIncomeDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId && InComeTypes.Contains(p.BillTypeStr))
                         .GroupBy(p => new { p.ReconciliationItemId, p.CustomerId, p.CustomerName, p.BillType, p.BillTypeStr, p.AgentId, p.AgentName, p.SaleOrderNo, p.OrderNo })
                         .Select(g => new ReconciliationOutput
                         {
                             CustomerName = g.Key.CustomerName,
                             SaleOrderNo = g.Key.SaleOrderNo,
                             BillTypeStr = g.Key.BillTypeStr,
                             AgentName = g.Key.AgentName,
                             Cost = g.Sum(z => z.Cost),
                             OrderNo = g.Key.OrderNo,
                             CostOfNoTax = g.Sum(z => z.CostOfNoTax),
                             Income = g.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.Income),
                             IncomeOfNoTax = g.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.IncomeOfNoTax),
                             KisData = 2
                         }).OrderBy(p => p.BillTypeStr).ThenBy(p => p.SaleOrderNo).ThenBy(p => p.CustomerName).ThenBy(p => p.AgentName).AsNoTracking().ToListAsync();
                var count = list.Count;

                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 按公司,供应商,单据
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListOfAgentBillExport(ReconciliationItemExportInput input)
        {
            try
            {
                var list = await _db.ReconciliationIncomeDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId && InComeTypes.Contains(p.BillTypeStr))
                         .GroupBy(p => new
                         {
                             p.ReconciliationItemId,
                             p.BillType,
                             p.BillTypeStr,
                             p.AgentId,
                             p.AgentName,
                             p.SaleOrderNo,
                             p.OrderNo
                         })
                         .Select(g => new ReconciliationOutput
                         {
                             BillTypeStr = g.Key.BillTypeStr,
                             AgentName = g.Key.AgentName,
                             SaleOrderNo = g.Key.SaleOrderNo,
                             Cost = g.Sum(z => z.Cost),
                             CostOfNoTax = g.Sum(z => z.CostOfNoTax),
                             Income = g.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.Income),
                             IncomeOfNoTax = g.Where(it => it.BillTypeStr != "赠品出库").Sum(z => z.IncomeOfNoTax),
                             OrderNo = g.Key.OrderNo,
                         }).OrderBy(p => p.BillTypeStr).ThenBy(p => p.SaleOrderNo).ThenBy(p => p.AgentName).AsNoTracking().ToListAsync();
                var count = list.Count;

                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        #endregion
    }
}
