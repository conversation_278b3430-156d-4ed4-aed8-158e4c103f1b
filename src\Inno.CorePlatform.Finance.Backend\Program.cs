﻿using Autofac;
using Autofac.Extensions.DependencyInjection;
using Dapr.Actors.Client;
using EasyCaching.Serialization.SystemTextJson.Configurations;
using HealthChecks.UI.Client;
using Inno.CorePlatform.Common.AspNetCore;
using Inno.CorePlatform.Common.Azure;
using Inno.CorePlatform.Common.Clients;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Logging;
using Inno.CorePlatform.Finance.Adapter.Clients;
using Inno.CorePlatform.Finance.Adapter.Clients.Competence;
using Inno.CorePlatform.Finance.Application;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Backend;
using Inno.CorePlatform.Finance.Backend.Middleware;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Permission.Backend;
using Inno.CorePlatform.ServiceClient;
using Inno.CorePlatform.ServiceClient.OperationalLog;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Reflection;
using System.Runtime.Loader;
using Inno.CorePlatform.Finance.Backend.Filters;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.LogServices;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Adapter.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddSingleton<ITelemetryInitializer, CloudRoleNameTelemetryInitializer>();
builder.Services.AddApplicationInsightsTelemetry();

#if !DEBUG
builder.Logging.ClearProviders();
#endif
LoggingHelper.AddSerilog(builder.Logging, builder.Services);

//注入数据库
builder.Services.AddDbContextPool<FinanceDbContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("FinanceDbContext")));
builder.Services.AddHttpContextAccessor();

builder.Services.AddControllers(options =>
{
    options.Filters.Add<ParameterLoggingFilter>();
});
builder.Services.AddControllers(options =>
{
    options.Filters.Add<GlobalExceptionFilter>();
}).AddNewtonsoftJson(options =>
{
    options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver(); //序列化时key为驼峰样式
    options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;//忽略循环引用
}).AddDapr();

builder.Services.AddEasyCaching(options =>
{
    options.UseRedis(config =>
    {
        config.DBConfig.Configuration = builder.Configuration.GetConnectionString("RedisConnection");
        config.SerializerName = "systemTextJson";
    }, "default-redis").WithSystemTextJson("systemTextJson");
});
builder.Services.AddTransient<IUserDisplayNameTransfer, UserDisplayNameTransfer>();
builder.Services.Configure<KingdeeSetting>(options => builder.Configuration.GetSection("KingdeeSetting").Bind(options));
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(s =>
{
    s.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "财务管理服务接口文档",
        Version = "v1",
        Description = "Powered by .NET 7.0"
    });

    //添加注释服务
    var filePath = System.IO.Path.Combine(AppContext.BaseDirectory, "backend.xml");
    s.IncludeXmlComments(filePath, true);

#if !DEBUG
     var filePath2 = System.IO.Path.Combine(AppContext.BaseDirectory, "dto.xml");
    s.IncludeXmlComments(filePath2, true);
#endif
    //添加控制器注释
    s.DocumentFilter<SwaggerDocTag>();
});

builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());

builder.Host.ConfigureContainer<ContainerBuilder>(builder =>
{
    builder.RegisterAssemblyTypes(GetAssembly("Inno.CorePlatform.Finance.Application")).AsImplementedInterfaces().InstancePerLifetimeScope();
    builder.RegisterAssemblyTypes(GetAssembly("Inno.CorePlatform.Finance.Adapter")).AsImplementedInterfaces().InstancePerLifetimeScope();
    builder.RegisterAssemblyTypes(GetAssembly("Inno.CorePlatform.Finance.Domain")).AsImplementedInterfaces().InstancePerLifetimeScope();
    builder.RegisterGeneric(typeof(BaseAllQueryService<>)).As(typeof(IBaseAllQueryService<>)).InstancePerLifetimeScope();
    // 3. 注册需要增强的特定服务（关键步骤）
    builder.RegisterType<TempToSellAppService>()
           .Named<ITempToSellAppService>("original-service")
           .AsSelf();  // 保留原始类型注册

    // 4. 注册装饰器（必须后执行）
    builder.RegisterDecorator<ITempToSellAppService>(
        (context, instance) =>
            LoggingDecorator<ITempToSellAppService>.Create(
                instance,
                context.Resolve<ISubLogService>()
            ),
        fromKey: "original-service"
    );

    // 5. 注册日志服务
    builder.RegisterType<SubLogService>()
           .As<ISubLogService>()
           .InstancePerLifetimeScope();
});

builder.Services.AddHttpClient();
builder.Services.AddFileGatewayServices(builder.Configuration);
builder.Services.AddSingleton<IFileGatewayClient, FileGatewayClient>();
builder.Services.AddSingleton<IActorProxyFactory, ActorProxyFactory>();
builder.Services.AddSingleton<ICodeGenClient, CodeGenClient>();
builder.Services.AddSingleton<IWeaverApiClient, WeaverApiClient>();
builder.Services.AddSingleton<IKingdeeApiClient, KingdeeApiClient>();
builder.Services.AddSingleton<ISPDApiClient, SPDApiClient>();
builder.Services.AddServicesFromNamespaceTree("Inno.CorePlatform.Common.Clients.ApiServices", Assembly.Load("Inno.CorePlatform.Common"), ServiceLifetime.Singleton);

// 添加应用服务
builder.Services.AddApplicationServices();

// 添加优化的金蝶API客户端
builder.Services.AddKingdeeApiClients(builder.Configuration);
#if DEBUG
builder.Services.AddTransient<IAppServiceContextAccessor, FakeAppServiceContextAccessor>();
#else
    builder.Services.AddTransient<IAppServiceContextAccessor, DefaultAppServiceContextAccessor>();
#endif
//协调服务-导出
builder.Services.AddCoordinateClient(builder.Configuration);
//设置允许所有来源跨域
builder.Services.AddCors(options => options.AddPolicy("AllowAllOrigin",
builder =>
{
    builder.AllowAnyMethod()
        .AllowAnyHeader()
        .SetIsOriginAllowed(_ => true)
        .AllowCredentials();
}));

//健康检查
var strUri = builder.Configuration.GetValue<string>("HealthCheckUris");
var lstUri = strUri.Split(';').Select(t => new Uri(t)).ToList();

builder.Services.AddHealthChecks()
                .AddSqlServer(connectionString: builder.Configuration.GetConnectionString("FinanceDbContext"),
                              healthQuery: "SELECT 1;", name: "sql",
                              failureStatus: HealthStatus.Degraded,
                              tags: new string[] { "db", "sql", "sqlserver" })
                .AddUrlGroup(lstUri);
//提交数据大小限制
builder.Services.Configure<FormOptions>(options =>
{
    options.ValueCountLimit = 100000000; // 5000 items max
    options.ValueLengthLimit = 1024 * 1024 * 100; // 100MB max len form data
});

builder.WebHost.ConfigureKestrel((context, options) =>
{
    options.Limits.MaxRequestBodySize = 1024 * 1024 * 100; // 100MB;
});
var app = builder.Build();

LoggingHelper.UseSerilog(app.Configuration, app.Services);

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

using (var scope = app.Services.CreateScope())
#if DEBUG
{
    var db = scope.ServiceProvider.GetRequiredService<FinanceDbContext>();
    db.Database.Migrate();
}
#endif

app.UseSwagger();
app.UseSwaggerUI(s =>
{
    s.SwaggerEndpoint("/swagger/v1/swagger.json", "fam-backend v1");
});

app.UseCors("AllowAllOrigin");
app.UseAuthorization();

app.UseExceptionMiddleware();//捕获全局异常中间件
app.UseDecodeBearerToken();

//云事件
app.UseCloudEvents();
//添加订阅映射终端 处理程序
app.MapSubscribeHandler();

app.MapControllers();
//记录请求参数日志
#if !DEBUG
app.UseOperationalLog(); //添加操作日志中间件
#endif
//健康检查
app.MapHealthChecks("/health", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.Run();

Assembly GetAssembly(string assemblyName)
{
    var assembly = AssemblyLoadContext.Default.LoadFromAssemblyPath(AppContext.BaseDirectory + $"{assemblyName}.dll");
    return assembly;
}