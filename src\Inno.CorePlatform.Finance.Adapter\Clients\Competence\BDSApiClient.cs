﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.CommonDto;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class BDSApiClient : BaseDaprApiClient<BDSApiClient>, IBDSApiClient
    {
        private readonly ILogger<BDSApiClient> _logger;
        private readonly DaprClient _daprClient;

        public BDSApiClient(DaprClient daprClient, ILogger<BDSApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
            _daprClient = daprClient;
            _logger = logger;
        }

        /// <summary>
        /// 获取员工所属公司
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<List<StaffCompanyDto>> GetStaffCompaniesAsync(GetStaffCompaniesInputDto inputParam)
        {
            return await InvokeMethodAsync<GetStaffCompaniesInputDto, List<StaffCompanyDto>>(inputParam, AppCenter.BDS_APPID, $"{AppCenter.STAFF_COMPANIES}?userId={inputParam.userId}");
        }

        /// <summary>
        /// 获取员工信息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<StaffInfoDto> GetStaffInfoAsync(GetStaffInfoInputDto inputParam)
        {
            return await InvokeMethodAsync<GetStaffInfoInputDto, StaffInfoDto>(inputParam, AppCenter.BDS_APPID, $"{AppCenter.STAFF_INFO}?userId={inputParam.userId}");
        }

        /// <summary>
        /// 员工信息元数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<StaffInfo>> GetStaffMetaAsync(StaffsInput input)
        {
            return await InvokeMethodAsync<StaffsInput, List<StaffInfo>>(input, AppCenter.BDS_APPID, AppCenter.STAFF_META);
        }
        /// <summary>
        /// 所有员工基础信息元数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<StaffInfo>> GetStaffALLAsync(StaffsInput input)
        {
            return await InvokeMethodAsync<StaffsInput, List<StaffInfo>>(input, AppCenter.BDS_APPID, AppCenter.STAFF_ALL);
        }

        /// <summary>
        /// 从用户中心，获取员工数据 byNames
        /// </summary>
        /// <returns></returns>
        public async Task<ResponseData<UserOutput>> GetUserByNamesAsync(GetUserInput input)
        {
            return await InvokeMethodCommonAsync<GetUserInput, ResponseData<UserOutput>>(input, AppCenter.UC_APPID, AppCenter.UC_GETUSERBYNAMES);
        }

        /// <summary>
        /// 从用户中心，获取员工数据 byDisplayNames
        /// </summary>
        /// <returns></returns>
        public async Task<List<SmallUserQueryOutput>> GetSmallUsersByDisplayNames(List<string?>? input)
        {
            return await InvokeMethodAsync<List<string?>?, List<SmallUserQueryOutput>>(input, AppCenter.UC_APPID, AppCenter.UC_GETSMALLUSERBYDISPLAYNAMES);
        }


        /// <summary>
        /// Dapr调用方法header（有入参）
        /// </summary>
        /// <typeparam name="TResponse">返回泛型</typeparam>
        /// <param name="appId">能力中心AppID</param>
        /// <param name="methodName">请求路径</param>
        /// <returns></returns>
        public async Task<TResponse> InvokeMethodAsync<TRequest, TResponse>(TRequest inputParam, string appId, string methodName)
        {
            try
            {
                var requestBody = JsonConvert.SerializeObject(inputParam);
                _logger.LogInformation($"请求:【AppID】{appId}【MethodName】{methodName}内容{requestBody}");
                var result = _daprClient.CreateInvokeMethodRequest(appId, methodName, inputParam);
                var res = await _daprClient.InvokeMethodAsync<BDSDaprOutputDto<TResponse>>(result);

                if (res == null || res.Code != 200)
                {
                    _logger.LogInformation($"请求:【AppID】{appId}【MethodName】{methodName}【Message】{res.Message}");
                }
                return res.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"请求:【AppID】{appId}【MethodName】{methodName}");
                throw new ApplicationException($"调用远程接口时出现错误，请联系管理员" + ex);
            }
        }
        /// <summary>
        /// Dapr调用公共方法无header
        /// </summary>
        /// <typeparam name="TRequest">请求泛型</typeparam>
        /// <typeparam name="TResponse">返回泛型</typeparam>
        /// <param name="appId">能力中心AppID</param>
        /// <param name="methodName">请求路径</param>
        /// <returns></returns>

        public async Task<TResponse> InvokeMethodCommonAsync<TRequest, TResponse>(TRequest inputParam, string appId, string methodName)
        {

            try
            {
                _logger.LogInformation($"请求:【AppID】{appId}【MethodName】{methodName}");
                //using var _daprClient = new DaprClientBuilder().UseHttpEndpoint("http://localhost:3511").Build();
                var res = await _daprClient.InvokeMethodAsync<TRequest, TResponse>(HttpMethod.Post, appId, methodName, inputParam);
                return res;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"请求失败:" + ex);
                throw new ApplicationException($"调用远程接口时出现错误，请联系管理员" + ex);
            }
        }
        /// <summary>
        /// Dapr调用方法header（有入参）
        /// </summary>
        /// <typeparam name="TResponse">返回泛型</typeparam>
        /// <param name="appId">能力中心AppID</param>
        /// <param name="methodName">请求路径</param>
        /// <returns></returns>
        public async Task<TResponse> InvokeMethodAsync<TRequest, TResponse>(TRequest inputParam, string appId, string methodName, string userName, string userId)
        {
            try
            {
                _logger.LogInformation($"请求:【AppID】{appId}【MethodName】{methodName}");
                //using var _daprClient = new DaprClientBuilder().UseHttpEndpoint("http://localhost:3501").Build();
                var result = _daprClient.CreateInvokeMethodRequest(appId, methodName, inputParam);
                //userName = "wangyongjun";
                //userId = "3CB7AA38-0D27-4B63-B363-429AA37FA201";
                result.Headers.Add("X-Inno-UserName", userName);
                result.Headers.Add("X-Inno-UserId", userId);

                var res = await _daprClient.InvokeMethodAsync<BDSDaprOutputDto<TResponse>>(result);

                if (res == null || res.Code != 200)
                {
                    throw new ApplicationException("调用远程接口时出现错误，请联系管理员");
                }
                return res.Data;
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"请求失败:" + ex);
                throw new ApplicationException($"调用远程接口时出现错误，请联系管理员" + ex);
            }
        }
        /// <summary>
        /// 获取员工可以操作的业务单元
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ServiceMetaOutput>> GetServiceMetaAsync(ServiceMetaInput input)
        {
            return await InvokeMethodAsync<ServiceMetaInput, List<ServiceMetaOutput>>(input, AppCenter.BDS_APPID, AppCenter.SERVICE_META);
        }
        /// <summary>
        /// 获取业务单元详情列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<ServiceOutput>> QueryServiceItem(BDSBaseInput input)
        {
            return await InvokeMethodAsync<BDSBaseInput, List<ServiceOutput>>(input, AppCenter.BDS_APPID, AppCenter.SERVICE_QUERY_ITEM);
        }
        /// <summary>
        /// 获取当前供应商信息（带数据策略权限）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<AgentMetaOutput>> GetAgentMetaListAsync(AgentMetaInput input)
        {
            return await InvokeMethodAsync<AgentMetaInput, List<AgentMetaOutput>>(input, AppCenter.BDS_APPID, AppCenter.AGENT_META);
        }
        /// <summary>
        /// 获取供应商信息
        /// </summary>
        /// <param name="inputParam"></param>
        /// <returns></returns>
        public async Task<List<DaprAgentInfoOutput>> GetAgentInfoAsync(BDSBaseInput inputParam)
        {
            return await InvokeMethodAsync<BDSBaseInput, List<DaprAgentInfoOutput>>(inputParam, AppCenter.BDS_APPID, $"{AppCenter.AGENT_INFO}");
        }

        /// <summary>
        /// 获取公司信息
        /// </summary>
        /// <param name="inputParam"></param>
        /// <returns></returns>
        public async Task<List<DaprCompanyInfoOutput>> GetCompanyInfoAsync(BDSBaseInput inputParam)
        {
            return await InvokeMethodAsync<BDSBaseInput, List<DaprCompanyInfoOutput>>(inputParam, AppCenter.BDS_APPID, $"{AppCenter.COMPANY_INFO}");
        }
        /// <summary>
        /// 获取员工信息(包含事业部)
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<DaprUserInfoOutput> GetUserStaffInfoAsync(BDSBaseInput inputParam)
        {
            return await InvokeMethodAsync<BDSBaseInput, DaprUserInfoOutput>(inputParam, AppCenter.BDS_APPID, $"{AppCenter.USER_STAFF_INFO}?userId={inputParam.userId}");
        }

        /// <summary>
        /// 获取厂家
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<ProducerMetaOutput>> GetProducerMetaListAsync(ProducerMetaInput input)
        {
            return await InvokeMethodAsync<ProducerMetaInput, List<ProducerMetaOutput>>(input, AppCenter.BDS_APPID, AppCenter.PRODUCER_META);
        }

        /// <summary>
        /// 获取员工可以操作的事业部
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BusinessDeptOutput>> GetBusinessDeptMetaAsync(BusinessDeptMetaInput input)
        {
            return await InvokeMethodAsync<BusinessDeptMetaInput, List<BusinessDeptOutput>>(input, AppCenter.BDS_APPID, AppCenter.USER_DEPART_PERMISSION);
        }

        /// <summary>
        /// 获取核算部门列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BusinessDeptOutput>> GetBusinessDeptListAsync(BusinessDeptMetaInput input)
        {
            return await InvokeMethodAsync<BusinessDeptMetaInput, List<BusinessDeptOutput>>(input, AppCenter.BDS_APPID, AppCenter.DEPART_LIST);
        }
        /// <summary>
        /// 非树形数据 获取核算部门，权限,业务权限过滤接口,
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<GetFlatCheckedDeptsOutput>> GetFlatCheckedDepts(GetFlatCheckedDeptsInput input, string userName, string userId)
        {
            return await InvokeMethodAsync<GetFlatCheckedDeptsInput, List<GetFlatCheckedDeptsOutput>>(input, AppCenter.BDS_APPID, AppCenter.DEPART_GetFlatCheckedDepts, userName, userId);
        }

        /// <summary>
        /// 获取核算部门列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BusinessDeptQueryOutput>> GetBusinessDeptListByIds(List<string?> ids)
        {
            var p = new { ids = ids };
            return await InvokeMethodAsync<object, List<BusinessDeptQueryOutput>>(p, AppCenter.BDS_APPID, AppCenter.DEPART_QUERY);
        }

        /// <summary>
        /// 查询客户部门信息
        /// </summary>
        /// <param name="intput"></param>
        /// <returns></returns>
        public async Task<List<CustomerDeptMetaInfoOutput>> GetCustomerDept(BDSBaseInput input)
        {
            return await InvokeMethodAsync<BDSBaseInput, List<CustomerDeptMetaInfoOutput>>(input, AppCenter.BDS_APPID, AppCenter.CUSTOMER_DEPT_META);
        }
        /// <summary>
        /// 查询客户信息
        /// </summary>
        /// <param name="intput"></param>
        /// <returns></returns>
        public async Task<List<CustomerDeptMetaInfoOutput>> GetCustomerByNames(BDSBaseInput input)
        {
            return await InvokeMethodAsync<BDSBaseInput, List<CustomerDeptMetaInfoOutput>>(input, AppCenter.BDS_APPID, AppCenter.CUSTOMER_META);
        }
        /// <summary>
        /// 查询客户信息
        /// </summary>
        /// <param name="intput"></param>
        /// <returns></returns>
        public async Task<CustomerOutput> GetCustomer(BDSBaseInput input)
        {
            return await InvokeMethodAsync<BDSBaseInput, CustomerOutput>(input, AppCenter.BDS_APPID, string.Format(AppCenter.getByCustomerId, input.id));
        }

        /// <summary>
        /// 客户收款信息查询
        /// </summary>
        /// <param name="intput"></param>
        /// <returns></returns>
        public async Task<List<CustomerBankOutput>> SelectReceiptInfo(SelectReceiptInfoInput input)
        {
            return await InvokeMethodAsync<SelectReceiptInfoInput, List<CustomerBankOutput>>(input, AppCenter.BDS_APPID, AppCenter.selectReceiptInfo);
        }
        /// <summary>
        /// 获取员工可以操作的医院(带数据策略)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<HospitalMetaOutput>> GetHospitalMetaAsync(HospitalMetaInput input)
        {
            return await InvokeMethodAsync<HospitalMetaInput, List<HospitalMetaOutput>>(input, AppCenter.BDS_APPID, AppCenter.HOSPITAL_META);
        }
        /// <summary>
        /// 获取员工可以操作的公司
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<CompanyMetaOutput>> GetCompanyMetaAsync(CompanyMetaInput input)
        {
            return await InvokeMethodAsync<CompanyMetaInput, List<CompanyMetaOutput>>(input, AppCenter.BDS_APPID, AppCenter.COMPANY_META);
        }

        /// <summary>
        /// 获取系统月度
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public async Task<string> GetSystemMonth(string companyId)
        {
            return await InvokeMethodAsync<dynamic, string>(new { id = companyId }, AppCenter.BDS_APPID, $"{AppCenter.COMPANY_SYSTEM_MONTH}");
        }

        /// <summary>
        /// 获取公司信息
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public async Task<List<companyInvoice>> GetCompanyById(string companyId)
        {
            var param = new { id = companyId };
            return await InvokeMethodAsync<object, List<companyInvoice>>(param, AppCenter.BDS_APPID, AppCenter.COMPANY_QueryItem);
        }

        public async Task<List<ProductNameInfoOutput>> GetProductNameInfoAsync(List<Guid> productNameIds)
        {
            var param = new { ids = productNameIds, limit = int.MaxValue };
            return await InvokeMethodWithQueryObjectAsync<object, List<ProductNameInfoOutput>>(param, AppCenter.PRODUCTNAMEMETA);
        }

        public async Task<List<ProductNameInfoOutput>> GetProductbyIdsAsync(List<Guid> productIds, string companyId, List<ProductIdAndAgentIdInput> inputs)
        {
            var param = new { ids = productIds, companyId = companyId, ifQueryTaxClassCode = true, limit = int.MaxValue, classCodeList = inputs };
            var ret = await InvokeMethodWithQueryObjectAsync<object, ProductNameInfoData>(param, AppCenter.PRODUCTINFO);
            return ret.Data;
        }
        public async Task<(List<ProductNameInfoOutput>, int)> GetProductClassificationPageAsync(List<Guid> productNameIds, int page = 1)
        {
            var param = new { ids = productNameIds, limit = int.MaxValue, page = page };
            var ret = await InvokeMethodWithQueryObjectAsync<object, List<ProductNameInfoOutput>>(param, AppCenter.PRODUCTNAMEMETA);
            return (ret, ret.Count);
        }
        public async Task<List<AgentBankInfo>> GetAgentBankInfoByAgentIds(List<Guid> agentIds)
        {
            var param = new { ids = agentIds, limit = int.MaxValue };
            var res = await InvokeMethodAsync<object, List<AgentBankInfo>>(param, GetAppId(), AppCenter.AGENT_QUERYITEM);
            return res;
        }

        protected override string GetAppId()
        {
            return AppCenter.BDS_APPID;
        }



        public async Task<bool> UpdateSysMonth(List<Guid> companyIds, string sysMonth)
        {
            var param = new { companyIds = companyIds, sysMonth = sysMonth };
            var res = await InvokeMethodAsync<object, string>(param, GetAppId(), AppCenter.UPDATESYSMONTH);
            return !string.IsNullOrEmpty(res);
        }

        public async Task<List<CompanyMetaInfosOut>> GetCompanyMetaInfosAsync(CompanyMetaInfosInput input)
        {
            return await InvokeMethodAsync<CompanyMetaInfosInput, List<CompanyMetaInfosOut>>(input, AppCenter.BDS_APPID, AppCenter.COMPANY_INFOMetaInfos);
        }
        public async Task<List<ProductItem>> QueryProductsAsync(ProductQueryInput input)
        {
            var res = await InvokeMethodAsync<ProductQueryInput, ProductQueryOutput>(input, AppCenter.BDS_APPID, AppCenter.QueryProducts_Uri);
            return res.Content;
        }
        public async Task<List<ProductNoOuput>> GetByNos(List<string> productNos, string companyId, List<ProductIdAndAgentIdInput> inputs)
        {
            var param = new { productNos = productNos, companyId = companyId, classCodeList = inputs };
            return await InvokeMethodAsync<object, List<ProductNoOuput>>(param, AppCenter.BDS_APPID, AppCenter.GetByNos);
        }
        public async Task<List<ProductNoOuput>> GetByNos(List<string> productNos, string companyId)
        {
            var param = new { productNos = productNos, companyId = companyId };
            return await InvokeMethodAsync<object, List<ProductNoOuput>>(param, AppCenter.BDS_APPID, AppCenter.GetByNos);
        }
        public async Task<List<ValueLabelDTO>> GetBusinessDeptById(string deptId, string userName, string userId)
        {
            List<ValueLabelDTO> dictList = new();
            if (deptId.Split('|').Length >= 3)
            {
                deptId = deptId.Split('|')[3];
            }
            var roughDicts = await InvokeMethodAsync<object, List<BusinessDeptDto>>(new { ids = new List<string> { deptId } }, AppCenter.BDS_APPID, AppCenter.Business_QueryList, userName, userId);

            if (roughDicts == null)
                return new List<ValueLabelDTO>();

            roughDicts.ForEach(item =>
            {
                dictList.Add(new ValueLabelDTO
                {
                    Label = item.name,
                    Value = item.deptShortName
                });
            });
            return dictList;
        }
        /// <summary>
        /// 获取数据字典
        /// </summary>
        /// <param name="dicType"></param>
        /// <returns></returns>
        public async Task<List<DataDictionaryOutput>> GetDataDictionaryListByType(string dicType)
        {
            var input = new DataDictionaryInput() { DictionaryType = dicType };
            return await InvokeMethodAsync<DataDictionaryInput, List<DataDictionaryOutput>>(input, AppCenter.BDS_APPID, AppCenter.GetDataDictionaryListByType);
        }
        /// <summary>
        /// 获取所有税种
        /// </summary>
        /// <returns></returns>
        public async Task<List<TaxClassCodeOutput>> GetAllTaxClassCode()
        {
            return await InvokeMethodAsync<List<TaxClassCodeOutput>>(AppCenter.getAllTaxClassCode, RequestMethodEnum.POST);
        }
        /// <summary>
        /// 查询产品发票信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<SelectProductInvoicesOutput>> SelectProductInvoices(SelectProductInvoicesInput input)
        {
            return await InvokeMethodAsync<SelectProductInvoicesInput, List<SelectProductInvoicesOutput>>(input, AppCenter.BDS_APPID, AppCenter.selectProductInvoices);
        }
    }
}
