﻿using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice
{
    /// <summary>
    /// 开票明细导入模板
    /// </summary>
    public class CustomizeInvoiceDetailExcelModel
    {
        /// <summary>
        /// 开票序号
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 开票规格
        /// </summary>
        public string Specification { get; set; }
        /// <summary>
        /// 原始规格
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Value
        {
            get
            {
                return Math.Round(Quantity * Price, 2);
            }
        }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrMsg { get; set; }

    }


    /// <summary>
    /// 批量开票导入模板
    /// </summary>
    public class CustomizeInvoicingExcelModel
    {
        /// <summary>
        /// 公司
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>
        public string OrderNo { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrMsg { get; set; }

    }
}
