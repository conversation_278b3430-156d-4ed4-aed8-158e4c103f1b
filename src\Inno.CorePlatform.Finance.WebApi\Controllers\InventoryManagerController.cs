using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.WebApi.Filters;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InventoryManagerController : BaseController
    {
        public override bool EnableParameterLogging { get; set; } = true;
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        private readonly IApplyBFFService _applyBFFService;

        public InventoryManagerController(IInventoryMgmAppService inventoryMgmAppService, IApplyBFFService applyBFFService, ISubLogService logService) : base(logService)
        {
            _inventoryMgmAppService = inventoryMgmAppService;
            _applyBFFService = applyBFFService;
        }

        /// <summary>
        /// 更新库存盘点
        /// </summary>
        /// <param name="inputDto"></param>
        /// <returns></returns>
        [HttpPost("UpdateStoreCheck")]
        [OperationLog("更新库存盘点-库存调用")]
        public async Task<BaseResponseData<int>> UpdateStoreCheck(StoreInventoryUpdateInputDto inputDto)
        {
            var res = await _inventoryMgmAppService.UpdateStoreInventory(inputDto);
            return res;
        }

        /// <summary>
        /// 更新库存盘点，可以只给参数中的companyid和StoreCheckCode赋值
        /// </summary>
        /// <param name="inputDto"></param>
        /// <returns></returns>
        [HttpPost("DeleteStoreCheck")]
        [OperationLog("删除库存盘点-库存调用")]
        public async Task<BaseResponseData<int>> DeleteStoreCheck(StoreInventoryDeleteInputDto inputDto)
        {
            var res = await _inventoryMgmAppService.DeleteStoreInventory(inputDto);
            return res;
        }

        /// <summary>
        /// 获取公司盘点状态
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpPost("GetInventoryStatus")]
        [SkipLogging]
        public async Task<BaseResponseData<InventoryInfoForCompanyDto>> GetInventoryStatus(Guid companyId)
        {
            return await _inventoryMgmAppService.GetInventoryStatus(companyId);
        }

        /// <summary>
        /// 处理垫资盘点结果
        /// </summary>
        /// <param name="request">垫资盘点处理请求</param>
        /// <returns></returns>
        [HttpPost("ProcessAdvanceInventory")]
        [OperationLog("处理垫资盘点结果")]
        public async Task<BaseResponseData<string>> ProcessAdvanceInventory(AdvanceInventoryProcessRequestDto request)
        {
            try
            {
                // 获取公司信息
                var companyInfos = await _applyBFFService.GetCompanyInfosAsync(new BDSBaseInput
                {
                    ids = new List<string> { request.CompanyId.ToString() }
                });

                var companyInfo = companyInfos?.FirstOrDefault();
                if (companyInfo == null)
                {
                    return BaseResponseData<string>.Failed(400, "公司信息不存在");
                }

                // 调用垫资盘点处理逻辑
                var (executeCount, advanceCode) = await _inventoryMgmAppService.AdvanceRecordInventory(
                    request.CompanyId,
                    request.SysMonth,
                    companyInfo.NameCode ?? "",
                    companyInfo.Name ?? "",
                    request.UserName ?? "系统自动");

                if (executeCount == 0 || (executeCount > 0 && string.IsNullOrEmpty(advanceCode)))
                {
                    return BaseResponseData<string>.Failed(500, "垫资盘点创建失败");
                }
                else if (executeCount == -1)
                {
                    return BaseResponseData<string>.Success("无符合条件的垫资数据，已跳过处理", "");
                }

                return BaseResponseData<string>.Success($"垫资盘点处理成功，影响行数: {executeCount}", advanceCode);
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, $"垫资盘点处理失败: {ex.Message}");
            }
        }
    }
}
